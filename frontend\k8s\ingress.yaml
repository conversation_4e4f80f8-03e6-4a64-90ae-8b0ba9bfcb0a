apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    argocd.argoproj.io/tracking-id: ops:networking.k8s.io/Ingress:ops/ngiq-point-desktop-upload-oss-frontend
    traefik.ingress.kubernetes.io/router.tls: "true"
    traefik.ingress.kubernetes.io/router.tls.certresolver: aliyun
  labels:
    app: ngiq-point-desktop-upload-oss-frontend
  name: ngiq-point-desktop-upload-oss-frontend
  namespace: ops
spec:
  ingressClassName: gateway-internal
  rules:
  - host: ptc-tools.ngdevops.cn
    http:
      paths:
      - backend:
          service:
            name: ngiq-point-desktop-upload-oss-frontend
            port:
              number: 80
        path: /
        pathType: ImplementationSpecific
  tls:
  - hosts:
    - ptc-tools.ngdevops.cn
