package auth

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/config"
	"github.com/golang-jwt/jwt/v5"
	"github.com/sirupsen/logrus"
)

// KeycloakService 处理与Keycloak交互的服务
type KeycloakService struct {
	Config config.Auth
}

// UserInfo 用户信息
type UserInfo struct {
	Sub               string   `json:"sub"`
	Email             string   `json:"email"`
	Name              string   `json:"name"`
	PreferredUsername string   `json:"preferred_username"`
	GivenName         string   `json:"given_name"`
	FamilyName        string   `json:"family_name"`
	RealmAccess       struct{} `json:"realm_access"`
	Picture           string   `json:"picture"`
}

// TokenResponse Keycloak令牌响应
type TokenResponse struct {
	AccessToken      string `json:"access_token"`
	ExpiresIn        int    `json:"expires_in"`
	RefreshExpiresIn int    `json:"refresh_expires_in"`
	RefreshToken     string `json:"refresh_token"`
	TokenType        string `json:"token_type"`
	IDToken          string `json:"id_token"`
	SessionState     string `json:"session_state"`
	Scope            string `json:"scope"`
}

// NewKeycloakService 创建Keycloak服务实例
func NewKeycloakService(config config.Auth) *KeycloakService {
	return &KeycloakService{
		Config: config,
	}
}

// GetAuthURL 生成认证URL
func (s *KeycloakService) GetAuthURL(state string) string {
	return fmt.Sprintf("%s%s?client_id=%s&redirect_uri=%s&response_type=code&scope=openid email profile&state=%s",
		s.Config.SsoURL, s.Config.AuthEndpoint, s.Config.ClientID, s.Config.RedirectURI, state)
}

// GetLogoutURL 生成登出URL
func (s *KeycloakService) GetLogoutURL(idToken string) string {
	return fmt.Sprintf("%s%s?id_token_hint=%s&post_logout_redirect_uri=%s",
		s.Config.SsoURL, s.Config.LogoutEndpoint, idToken, s.Config.PostLogoutRedirectURI)
}

// ExchangeCodeForToken 使用授权码交换令牌
func (s *KeycloakService) ExchangeCodeForToken(code string) (*TokenResponse, error) {
	// 准备请求
	tokenURL := s.Config.SsoURL + s.Config.TokenEndpoint

	// 日志记录请求开始（不记录完整code，避免泄露敏感信息）
	logrus.WithFields(logrus.Fields{
		"sso_url":      s.Config.SsoURL,
		"redirect_uri": s.Config.RedirectURI,
		"code_length":  len(code),
	}).Debug("开始交换授权码")

	data := fmt.Sprintf(
		"grant_type=authorization_code&code=%s&redirect_uri=%s&client_id=%s",
		code, s.Config.RedirectURI, s.Config.ClientID,
	)

	if s.Config.ClientSecret != "" {
		data += fmt.Sprintf("&client_secret=%s", s.Config.ClientSecret)
	}

	req, err := http.NewRequest("POST", tokenURL, strings.NewReader(data))
	if err != nil {
		logrus.WithError(err).WithField("url", tokenURL).Error("创建令牌交换请求失败")
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	// 设置上下文超时
	ctx, cancel := context.WithTimeout(context.Background(), 15*time.Second)
	defer cancel()

	req = req.WithContext(ctx)

	// 发送请求
	client := &http.Client{
		Timeout: 15 * time.Second,
		Transport: &http.Transport{
			TLSHandshakeTimeout:   10 * time.Second,
			ResponseHeaderTimeout: 10 * time.Second,
			ExpectContinueTimeout: 1 * time.Second,
			DisableKeepAlives:     false,
			MaxIdleConnsPerHost:   10,
		},
	}

	start := time.Now()
	resp, err := client.Do(req)
	elapsed := time.Since(start)

	// 记录请求耗时
	logrus.WithFields(logrus.Fields{
		"elapsed_ms": elapsed.Milliseconds(),
		"sso_url":    s.Config.SsoURL,
	}).Debug("令牌交换请求耗时")

	if err != nil {
		// 根据错误类型提供更详细的错误信息
		if urlErr, ok := err.(*url.Error); ok {
			if urlErr.Timeout() {
				logrus.WithError(err).Error("令牌交换请求超时")
				return nil, fmt.Errorf("发送请求超时: %w", err)
			} else if urlErr.Err == context.DeadlineExceeded {
				logrus.WithError(err).Error("令牌交换请求超过最大等待时间")
				return nil, fmt.Errorf("请求超过最大等待时间: %w", err)
			} else if strings.Contains(urlErr.Err.Error(), "connection refused") {
				logrus.WithError(err).Error("连接认证服务器被拒绝")
				return nil, fmt.Errorf("连接认证服务器被拒绝: %w", err)
			} else if strings.Contains(urlErr.Err.Error(), "no such host") {
				logrus.WithError(err).Error("无法解析认证服务器地址")
				return nil, fmt.Errorf("无法解析认证服务器地址: %w", err)
			}
		}

		logrus.WithError(err).Error("发送令牌交换请求失败")
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体内容，用于错误信息和解析
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logrus.WithError(err).Error("读取令牌响应失败")
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 处理响应
	if resp.StatusCode != http.StatusOK {
		logrus.WithFields(logrus.Fields{
			"status_code": resp.StatusCode,
			"response":    string(body),
		}).Error("令牌交换请求返回错误状态码")

		return nil, fmt.Errorf("获取令牌失败: %s (%s)", resp.Status, string(body))
	}

	var tokenResp TokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		logrus.WithError(err).WithField("response", string(body)).Error("解析令牌响应失败")
		return nil, fmt.Errorf("解析令牌响应失败: %w", err)
	}

	// 验证响应中包含必要字段
	if tokenResp.AccessToken == "" {
		logrus.Error("令牌响应缺少访问令牌")
		return nil, errors.New("认证服务器返回无效的令牌响应: 缺少访问令牌")
	}

	logrus.WithFields(logrus.Fields{
		"expires_in":   tokenResp.ExpiresIn,
		"token_type":   tokenResp.TokenType,
		"has_id_token": tokenResp.IDToken != "",
	}).Info("成功获取访问令牌")

	return &tokenResp, nil
}

// GetUserInfo 获取用户信息
func (s *KeycloakService) GetUserInfo(accessToken string) (*UserInfo, error) {
	// 准备请求
	userinfoURL := s.Config.SsoURL + s.Config.UserinfoEndpoint
	req, err := http.NewRequest("GET", userinfoURL, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Add("Authorization", "Bearer "+accessToken)

	// 发送请求
	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 处理响应
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("获取用户信息失败: %s", resp.Status)
	}

	var userInfo UserInfo
	if err := json.NewDecoder(resp.Body).Decode(&userInfo); err != nil {
		return nil, fmt.Errorf("解析用户信息失败: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"username": userInfo.PreferredUsername,
		"email":    userInfo.Email,
	}).Info("成功获取用户信息")

	return &userInfo, nil
}

// ValidateToken 验证JWT令牌
func (s *KeycloakService) ValidateToken(tokenString string) (*jwt.Token, error) {
	token, err := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
		// 验证签名算法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}

		// TODO: 在生产环境中，应该从Keycloak获取公钥
		// 这里简化处理，假设使用HMAC算法和共享密钥
		return []byte(s.Config.ClientSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("令牌验证失败: %w", err)
	}

	return token, nil
}

// 身份验证中间件 - HTTP标准库版本
func AuthMiddleware(keycloakService *KeycloakService, next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		// 如果认证被禁用，直接继续
		if !keycloakService.Config.Enabled {
			next(w, r)
			return
		}

		// 检查Authorization头
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "未授权: 缺少Authorization头", http.StatusUnauthorized)
			return
		}

		// 提取令牌
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			http.Error(w, "未授权: 无效的Authorization头格式", http.StatusUnauthorized)
			return
		}

		// 验证令牌
		token, err := keycloakService.ValidateToken(tokenParts[1])
		if err != nil || !token.Valid {
			http.Error(w, "未授权: 无效的令牌", http.StatusUnauthorized)
			return
		}

		// 令牌有效，继续处理请求
		next(w, r)
	}
}

// 从请求上下文中获取用户信息
func GetUserFromContext(ctx context.Context) (*UserInfo, error) {
	user, ok := ctx.Value("user").(*UserInfo)
	if !ok {
		return nil, errors.New("无法从上下文中获取用户信息")
	}
	return user, nil
}
