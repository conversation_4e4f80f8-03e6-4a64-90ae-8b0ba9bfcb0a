@import '../../style/theme/default.less';

@btn-font-weight: @font-weight-400;
@btn-border-radius: @radius-small;
@btn-border-width: @border-1;

@btn-size-mini-height: @size-mini;
@btn-size-small-height: @size-small;
@btn-size-default-height: @size-default;
@btn-size-large-height: @size-large;

@btn-size-mini-radius: @btn-border-radius;
@btn-size-small-radius: @btn-border-radius;
@btn-size-default-radius: @btn-border-radius;
@btn-size-large-radius: @btn-border-radius;

@btn-size-mini-border-width: @btn-border-width;
@btn-size-small-border-width: @btn-border-width;
@btn-size-default-border-width: @btn-border-width;
@btn-size-large-border-width: @btn-border-width;

@btn-size-mini-icon-spacing: @spacing-2;
@btn-size-small-icon-spacing: @spacing-3;
@btn-size-default-icon-spacing: @spacing-4;
@btn-size-large-icon-spacing: @spacing-4;

@btn-size-mini-icon-vertical-align: -2px;
@btn-size-small-icon-vertical-align: -2px;
@btn-size-default-icon-vertical-align: -2px;
@btn-size-large-icon-vertical-align: -2px;

@btn-size-mini-padding-horizontal: 11px;
@btn-size-small-padding-horizontal: 15px;
@btn-size-default-padding-horizontal: 15px;
@btn-size-large-padding-horizontal: 19px;

@btn-size-mini-font-size: @font-size-body-1;
@btn-size-small-font-size: @font-size-body-3;
@btn-size-default-font-size: @font-size-body-3;
@btn-size-large-font-size: @font-size-body-3;

/***** Outline *****/
@btn-outline-color-text: @color-primary-6;
@btn-outline-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-primary-light-3');
@btn-outline-color-text_hover: @color-primary-5;
@btn-outline-color-text_active: @color-primary-7;
@btn-outline-color-bg: @color-transparent;
@btn-outline-color-bg_disabled: @color-transparent;
@btn-outline-color-bg_hover: @color-transparent;
@btn-outline-color-bg_active: @color-transparent;
@btn-outline-color-border: @color-primary-6;
@btn-outline-color-border_disabled: var(~'@{arco-cssvars-prefix}-color-primary-light-3');
@btn-outline-color-border_hover: @color-primary-5;
@btn-outline-color-border_active: @color-primary-7;
// status
@btn-outline-color-text_warning: @color-warning-6;
@btn-outline-color-text_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-outline-color-text_warning_hover: @color-warning-5;
@btn-outline-color-text_warning_active: @color-warning-7;
@btn-outline-color-bg_warning: @color-transparent;
@btn-outline-color-bg_warning_disabled: @color-transparent;
@btn-outline-color-bg_warning_hover: @color-transparent;
@btn-outline-color-bg_warning_active: @color-transparent;
@btn-outline-color-border_warning: @color-warning-6;
@btn-outline-color-border_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-outline-color-border_warning_hover: @color-warning-5;
@btn-outline-color-border_warning_active: @color-warning-7;

@btn-outline-color-text_danger: @color-danger-6;
@btn-outline-color-text_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-outline-color-text_danger_hover: @color-danger-5;
@btn-outline-color-text_danger_active: @color-danger-7;
@btn-outline-color-bg_danger: @color-transparent;
@btn-outline-color-bg_danger_disabled: @color-transparent;
@btn-outline-color-bg_danger_hover: @color-transparent;
@btn-outline-color-bg_danger_active: @color-transparent;
@btn-outline-color-border_danger: @color-danger-6;
@btn-outline-color-border_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-outline-color-border_danger_hover: @color-danger-5;
@btn-outline-color-border_danger_active: @color-danger-7;

@btn-outline-color-text_success: @color-success-6;
@btn-outline-color-text_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-outline-color-text_success_hover: @color-success-5;
@btn-outline-color-text_success_active: @color-success-7;
@btn-outline-color-bg_success: @color-transparent;
@btn-outline-color-bg_success_disabled: @color-transparent;
@btn-outline-color-bg_success_hover: @color-transparent;
@btn-outline-color-bg_success_active: @color-transparent;
@btn-outline-color-border_success: @color-success-6;
@btn-outline-color-border_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-outline-color-border_success_hover: @color-success-5;
@btn-outline-color-border_success_active: @color-success-7;
// border
@btn-outline-border-style: @border-solid;

/***** Primary *****/
@btn-primary-color-text: #fff;
@btn-primary-color-text_disabled: #fff;
@btn-primary-color-text_hover: #fff;
@btn-primary-color-text_active: #fff;
@btn-primary-color-bg: @color-primary-6;
@btn-primary-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-primary-light-3');
@btn-primary-color-bg_hover: @color-primary-5;
@btn-primary-color-bg_active: @color-primary-7;
@btn-primary-color-border: @color-transparent;
@btn-primary-color-border_disabled: @color-transparent;
@btn-primary-color-border_hover: @color-transparent;
@btn-primary-color-border_active: @color-transparent;
// status
@btn-primary-color-text_warning: #fff;
@btn-primary-color-text_warning_disabled: #fff;
@btn-primary-color-text_warning_hover: #fff;
@btn-primary-color-text_warning_active: #fff;
@btn-primary-color-bg_warning: @color-warning-6;
@btn-primary-color-bg_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-primary-color-bg_warning_hover: @color-warning-5;
@btn-primary-color-bg_warning_active: @color-warning-7;
@btn-primary-color-border_warning: @color-transparent;
@btn-primary-color-border_warning_disabled: @color-transparent;
@btn-primary-color-border_warning_hover: @color-transparent;
@btn-primary-color-border_warning_active: @color-transparent;

@btn-primary-color-text_danger: #fff;
@btn-primary-color-text_danger_disabled: #fff;
@btn-primary-color-text_danger_hover: #fff;
@btn-primary-color-text_danger_active: #fff;
@btn-primary-color-bg_danger: @color-danger-6;
@btn-primary-color-bg_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-primary-color-bg_danger_hover: @color-danger-5;
@btn-primary-color-bg_danger_active: @color-danger-7;
@btn-primary-color-border_danger: @color-transparent;
@btn-primary-color-border_danger_disabled: @color-transparent;
@btn-primary-color-border_danger_hover: @color-transparent;
@btn-primary-color-border_danger_active: @color-transparent;

@btn-primary-color-text_success: #fff;
@btn-primary-color-text_success_disabled: #fff;
@btn-primary-color-text_success_hover: #fff;
@btn-primary-color-text_success_active: #fff;
@btn-primary-color-bg_success: @color-success-6;
@btn-primary-color-bg_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-primary-color-bg_success_hover: @color-success-5;
@btn-primary-color-bg_success_active: @color-success-7;
@btn-primary-color-border_success: @color-transparent;
@btn-primary-color-border_success_disabled: @color-transparent;
@btn-primary-color-border_success_hover: @color-transparent;
@btn-primary-color-border_success_active: @color-transparent;
// border
@btn-primary-border-style: @border-solid;

/***** Secondary *****/
@btn-secondary-color-text: var(~'@{arco-cssvars-prefix}-color-text-2');
@btn-secondary-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@btn-secondary-color-text_hover: var(~'@{arco-cssvars-prefix}-color-text-2');
@btn-secondary-color-text_active: var(~'@{arco-cssvars-prefix}-color-text-2');
@btn-secondary-color-bg: var(~'@{arco-cssvars-prefix}-color-secondary');
@btn-secondary-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-secondary-disabled');
@btn-secondary-color-bg_hover: var(~'@{arco-cssvars-prefix}-color-secondary-hover');
@btn-secondary-color-bg_active: var(~'@{arco-cssvars-prefix}-color-secondary-active');
@btn-secondary-color-border: @color-transparent;
@btn-secondary-color-border_disabled: @color-transparent;
@btn-secondary-color-border_hover: @color-transparent;
@btn-secondary-color-border_active: @color-transparent;
// status
@btn-secondary-color-text_warning: @color-warning-6;
@btn-secondary-color-text_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-secondary-color-text_warning_hover: @color-warning-6;
@btn-secondary-color-text_warning_active: @color-warning-6;
@btn-secondary-color-bg_warning: var(~'@{arco-cssvars-prefix}-color-warning-light-1');
@btn-secondary-color-bg_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-1');
@btn-secondary-color-bg_warning_hover: var(~'@{arco-cssvars-prefix}-color-warning-light-2');
@btn-secondary-color-bg_warning_active: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-secondary-color-border_warning: @color-transparent;
@btn-secondary-color-border_warning_disabled: @color-transparent;
@btn-secondary-color-border_warning_hover: @color-transparent;
@btn-secondary-color-border_warning_active: @color-transparent;

@btn-secondary-color-text_danger: @color-danger-6;
@btn-secondary-color-text_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-secondary-color-text_danger_hover: @color-danger-6;
@btn-secondary-color-text_danger_active: @color-danger-6;
@btn-secondary-color-bg_danger: var(~'@{arco-cssvars-prefix}-color-danger-light-1');
@btn-secondary-color-bg_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-1');
@btn-secondary-color-bg_danger_hover: var(~'@{arco-cssvars-prefix}-color-danger-light-2');
@btn-secondary-color-bg_danger_active: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-secondary-color-border_danger: @color-transparent;
@btn-secondary-color-border_danger_disabled: @color-transparent;
@btn-secondary-color-border_danger_hover: @color-transparent;
@btn-secondary-color-border_danger_active: @color-transparent;

@btn-secondary-color-text_success: @color-success-6;
@btn-secondary-color-text_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-secondary-color-text_success_hover: @color-success-6;
@btn-secondary-color-text_success_active: @color-success-6;
@btn-secondary-color-bg_success: var(~'@{arco-cssvars-prefix}-color-success-light-1');
@btn-secondary-color-bg_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-1');
@btn-secondary-color-bg_success_hover: var(~'@{arco-cssvars-prefix}-color-success-light-2');
@btn-secondary-color-bg_success_active: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-secondary-color-border_success: @color-transparent;
@btn-secondary-color-border_success_disabled: @color-transparent;
@btn-secondary-color-border_success_hover: @color-transparent;
@btn-secondary-color-border_success_active: @color-transparent;
// border
@btn-secondary-border-style: @border-solid;

/***** Dashed *****/
@btn-dashed-color-text: var(~'@{arco-cssvars-prefix}-color-text-2');
@btn-dashed-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');
@btn-dashed-color-text_hover: var(~'@{arco-cssvars-prefix}-color-text-2');
@btn-dashed-color-text_active: var(~'@{arco-cssvars-prefix}-color-text-2');
@btn-dashed-color-bg: var(~'@{arco-cssvars-prefix}-color-fill-2');
@btn-dashed-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-fill-2');
@btn-dashed-color-bg_hover: var(~'@{arco-cssvars-prefix}-color-fill-3');
@btn-dashed-color-bg_active: var(~'@{arco-cssvars-prefix}-color-fill-4');
@btn-dashed-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@btn-dashed-color-border_disabled: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@btn-dashed-color-border_hover: var(~'@{arco-cssvars-prefix}-color-neutral-4');
@btn-dashed-color-border_active: var(~'@{arco-cssvars-prefix}-color-neutral-5');
// status
@btn-dashed-color-text_warning: @color-warning-6;
@btn-dashed-color-text_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-dashed-color-text_warning_hover: @color-warning-6;
@btn-dashed-color-text_warning_active: @color-warning-6;
@btn-dashed-color-bg_warning: var(~'@{arco-cssvars-prefix}-color-warning-light-1');
@btn-dashed-color-bg_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-1');
@btn-dashed-color-bg_warning_hover: var(~'@{arco-cssvars-prefix}-color-warning-light-2');
@btn-dashed-color-bg_warning_active: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-dashed-color-border_warning: var(~'@{arco-cssvars-prefix}-color-warning-light-2');
@btn-dashed-color-border_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-2');
@btn-dashed-color-border_warning_hover: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-dashed-color-border_warning_active: var(~'@{arco-cssvars-prefix}-color-warning-light-4');

@btn-dashed-color-text_danger: @color-danger-6;
@btn-dashed-color-text_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-dashed-color-text_danger_hover: @color-danger-6;
@btn-dashed-color-text_danger_active: @color-danger-6;
@btn-dashed-color-bg_danger: var(~'@{arco-cssvars-prefix}-color-danger-light-1');
@btn-dashed-color-bg_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-1');
@btn-dashed-color-bg_danger_hover: var(~'@{arco-cssvars-prefix}-color-danger-light-2');
@btn-dashed-color-bg_danger_active: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-dashed-color-border_danger: var(~'@{arco-cssvars-prefix}-color-danger-light-2');
@btn-dashed-color-border_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-2');
@btn-dashed-color-border_danger_hover: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-dashed-color-border_danger_active: var(~'@{arco-cssvars-prefix}-color-danger-light-4');

@btn-dashed-color-text_success: @color-success-6;
@btn-dashed-color-text_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-dashed-color-text_success_hover: @color-success-6;
@btn-dashed-color-text_success_active: @color-success-6;
@btn-dashed-color-bg_success: var(~'@{arco-cssvars-prefix}-color-success-light-1');
@btn-dashed-color-bg_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-1');
@btn-dashed-color-bg_success_hover: var(~'@{arco-cssvars-prefix}-color-success-light-2');
@btn-dashed-color-bg_success_active: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-dashed-color-border_success: var(~'@{arco-cssvars-prefix}-color-success-light-2');
@btn-dashed-color-border_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-2');
@btn-dashed-color-border_success_hover: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-dashed-color-border_success_active: var(~'@{arco-cssvars-prefix}-color-success-light-4');
// border
@btn-dashed-border-style: @border-dashed;

/***** Text *****/
@btn-text-color-text: @color-primary-6;
@btn-text-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-primary-light-3');
@btn-text-color-text_hover: @color-primary-6;
@btn-text-color-text_active: @color-primary-6;
@btn-text-color-bg: @color-transparent;
@btn-text-color-bg_disabled: @color-transparent;
@btn-text-color-bg_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@btn-text-color-bg_active: var(~'@{arco-cssvars-prefix}-color-fill-3');
@btn-text-color-border: @color-transparent;
@btn-text-color-border_disabled: @color-transparent;
@btn-text-color-border_hover: @color-transparent;
@btn-text-color-border_active: @color-transparent;
// status
@btn-text-color-text_warning: @color-warning-6;
@btn-text-color-text_warning_disabled: var(~'@{arco-cssvars-prefix}-color-warning-light-3');
@btn-text-color-text_warning_hover: @color-warning-6;
@btn-text-color-text_warning_active: @color-warning-6;
@btn-text-color-bg_warning: @color-transparent;
@btn-text-color-bg_warning_disabled: @color-transparent;
@btn-text-color-bg_warning_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@btn-text-color-bg_warning_active: var(~'@{arco-cssvars-prefix}-color-fill-3');
@btn-text-color-border_warning: @color-transparent;
@btn-text-color-border_warning_disabled: @color-transparent;
@btn-text-color-border_warning_hover: @color-transparent;
@btn-text-color-border_warning_active: @color-transparent;

@btn-text-color-text_danger: @color-danger-6;
@btn-text-color-text_danger_disabled: var(~'@{arco-cssvars-prefix}-color-danger-light-3');
@btn-text-color-text_danger_hover: @color-danger-6;
@btn-text-color-text_danger_active: @color-danger-6;
@btn-text-color-bg_danger: @color-transparent;
@btn-text-color-bg_danger_disabled: @color-transparent;
@btn-text-color-bg_danger_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@btn-text-color-bg_danger_active: var(~'@{arco-cssvars-prefix}-color-fill-3');
@btn-text-color-border_danger: @color-transparent;
@btn-text-color-border_danger_disabled: @color-transparent;
@btn-text-color-border_danger_hover: @color-transparent;
@btn-text-color-border_danger_active: @color-transparent;

@btn-text-color-text_success: @color-success-6;
@btn-text-color-text_success_disabled: var(~'@{arco-cssvars-prefix}-color-success-light-3');
@btn-text-color-text_success_hover: @color-success-6;
@btn-text-color-text_success_active: @color-success-6;
@btn-text-color-bg_success: @color-transparent;
@btn-text-color-bg_success_disabled: @color-transparent;
@btn-text-color-bg_success_hover: var(~'@{arco-cssvars-prefix}-color-fill-2');
@btn-text-color-bg_success_active: var(~'@{arco-cssvars-prefix}-color-fill-3');
@btn-text-color-border_success: @color-transparent;
@btn-text-color-border_success_disabled: @color-transparent;
@btn-text-color-border_success_hover: @color-transparent;
@btn-text-color-border_success_active: @color-transparent;
// border
@btn-text-border-style: @border-solid;

/***** focus-visible *****/
@btn-box-shadow-radius: 2px;
@btn-primary-color-box-shadow: @color-primary-3;
@btn-outline-color-box-shadow: @color-primary-3;
@btn-secondary-color-box-shadow: @color-secondary-active;
@btn-dashed-color-box-shadow: @color-secondary-active;
@btn-text-color-box-shadow: @color-secondary-active;
@btn-color-box-shadow_warning: @color-warning-3;
@btn-color-box-shadow_danger: @color-danger-3;
@btn-color-box-shadow_success: @color-success-3;
