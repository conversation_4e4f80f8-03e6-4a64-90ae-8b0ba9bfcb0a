apiVersion: v1
kind: ConfigMap
metadata:
  name: nginx-config
  namespace: ops
data:
  ptc-tools.ngdevops.cn.conf: |
    server {
        listen 80;
        server_name ptc-tools.ngdevops.cn;
        
        # 开启gzip压缩提高加载速度
        gzip on;
        gzip_min_length 1k;
        gzip_comp_level 6;
        gzip_types text/plain text/css text/javascript application/javascript application/json application/xml;
        gzip_vary on;
        
        # 增加缓存控制
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            root /usr/share/nginx/html;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
            access_log off;
        }
        
        # 处理静态资源和React Router
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ /index.html;
            
            # SPA路由刷新问题
            add_header Cache-Control "no-cache, no-store, must-revalidate";
            add_header Pragma "no-cache";
            add_header Expires 0;
            
            # 允许跨域
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        }
        
        # API代理设置 - 修改关键配置
        location /api/ {
            proxy_pass http://ngiq-point-desktop-upload-oss:8080;
            
            # 标准代理头
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 调试API访问问题 - 添加更详细的日志
            access_log /var/log/nginx/api_access.log;
            error_log /var/log/nginx/api_error.log debug;
            
            # CORS配置
            add_header 'Access-Control-Allow-Origin' '*' always;
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
            
            # 处理OPTIONS预检请求
            if ($request_method = 'OPTIONS') {
                add_header 'Access-Control-Allow-Origin' '*' always;
                add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS' always;
                add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;
                add_header 'Access-Control-Max-Age' 1728000;
                add_header 'Content-Type' 'text/plain charset=UTF-8';
                add_header 'Content-Length' 0;
                return 204;
            }
            
            # SSE 配置，确保流式响应工作正常
            proxy_http_version 1.1;
            proxy_set_header Connection "";
            proxy_buffering off;
            proxy_cache off;
            
            # 增加超时时间
            proxy_read_timeout 3600s;
            proxy_send_timeout 3600s;
            proxy_connect_timeout 3600s;
            
            # 确保立即刷新
            proxy_set_header X-Accel-Buffering no;
            
            # 支持大体积请求的上传
            client_max_body_size 200M;
        }

        # 健康检查
        location /health {
            access_log off;
            return 200 "healthy\n";
        }
        
        # 错误处理
        error_page 404 /index.html;
        
        # 确保大文件上传不会超时
        client_max_body_size 200M;
        client_body_timeout 300s;
        client_header_timeout 300s;
        keepalive_timeout 300s;
    }