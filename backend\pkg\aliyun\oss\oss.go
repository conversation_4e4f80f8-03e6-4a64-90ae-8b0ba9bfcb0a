package oss

import (
	"bytes"
	"fmt"
	"path/filepath"
	"strings"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/sirupsen/logrus"
)

// Client 是对OSS操作的封装
type Client struct {
	client     *oss.Client
	bucketName string
	bucket     *oss.Bucket
}

// NewClient 创建一个新的OSS客户端
func NewClient(endpoint, accessKeyID, accessKeySecret, bucketName string) (*Client, error) {
	logrus.WithFields(logrus.Fields{
		"endpoint":   endpoint,
		"bucketName": bucketName,
	}).Info("创建OSS客户端")

	client, err := oss.New(endpoint, accessKeyID, accessKeySecret)
	if err != nil {
		logrus.WithError(err).Error("创建OSS客户端失败")
		return nil, fmt.Errorf("创建OSS客户端失败: %w", err)
	}

	// 获取存储桶
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		logrus.WithError(err).WithField("bucketName", bucketName).Error("获取存储桶失败")
		return nil, fmt.Errorf("获取存储桶失败: %w", err)
	}

	logrus.WithField("bucketName", bucketName).Info("OSS客户端创建成功")
	return &Client{
		client:     client,
		bucketName: bucketName,
		bucket:     bucket,
	}, nil
}

// UploadFile 上传文件到OSS
func (c *Client) UploadFile(objectKey string, content []byte) error {
	return c.UploadFileWithACL(objectKey, content, oss.ACLDefault)
}

// UploadFileWithACL 上传文件到OSS，并指定权限
func (c *Client) UploadFileWithACL(objectKey string, content []byte, acl oss.ACLType) error {
	logrus.WithFields(logrus.Fields{
		"objectKey": objectKey,
		"size":      len(content),
		"acl":       acl,
	}).Info("开始上传文件到OSS")

	// 创建文件读取器
	reader := bytes.NewReader(content)

	// 上传文件，并设置权限
	options := []oss.Option{
		oss.ContentLength(int64(len(content))),
		oss.ObjectACL(acl),
	}

	err := c.bucket.PutObject(objectKey, reader, options...)
	if err != nil {
		logrus.WithError(err).WithFields(logrus.Fields{
			"objectKey": objectKey,
			"size":      len(content),
			"acl":       acl,
		}).Error("上传文件到OSS失败")
		return fmt.Errorf("上传文件到OSS失败: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"objectKey": objectKey,
		"size":      len(content),
		"acl":       acl,
	}).Info("文件上传到OSS成功")

	// 验证ACL是否设置成功，如果不符则尝试单独设置
	objectACL, err := c.bucket.GetObjectACL(objectKey)
	if err != nil {
		logrus.WithError(err).WithField("objectKey", objectKey).Warn("获取对象ACL失败")
	} else {
		logrus.WithFields(logrus.Fields{
			"objectKey":   objectKey,
			"expectedACL": acl,
			"actualACL":   objectACL.ACL,
		}).Info("验证对象ACL设置")

		if string(objectACL.ACL) != string(acl) {
			logrus.WithFields(logrus.Fields{
				"objectKey":   objectKey,
				"expectedACL": acl,
				"actualACL":   objectACL.ACL,
			}).Warn("对象ACL设置与预期不符，尝试单独设置ACL")

			// 尝试使用SetObjectACL单独设置ACL
			err = c.bucket.SetObjectACL(objectKey, acl)
			if err != nil {
				logrus.WithError(err).WithFields(logrus.Fields{
					"objectKey": objectKey,
					"acl":       acl,
				}).Error("单独设置对象ACL失败")
			} else {
				logrus.WithFields(logrus.Fields{
					"objectKey": objectKey,
					"acl":       acl,
				}).Info("单独设置对象ACL成功")

				// 再次验证ACL设置
				newObjectACL, err := c.bucket.GetObjectACL(objectKey)
				if err != nil {
					logrus.WithError(err).WithField("objectKey", objectKey).Warn("重新获取对象ACL失败")
				} else {
					logrus.WithFields(logrus.Fields{
						"objectKey":   objectKey,
						"expectedACL": acl,
						"finalACL":    newObjectACL.ACL,
					}).Info("最终ACL验证结果")
				}
			}
		}
	}

	return nil
}

// ListObjects 列出指定前缀的所有对象
func (c *Client) ListObjects(prefix string, maxKeys int) ([]string, error) {
	logrus.WithFields(logrus.Fields{
		"prefix":  prefix,
		"maxKeys": maxKeys,
	}).Info("列出OSS对象")

	lsRes, err := c.bucket.ListObjects(oss.Prefix(prefix), oss.MaxKeys(maxKeys))
	if err != nil {
		logrus.WithError(err).WithField("prefix", prefix).Error("列出OSS对象失败")
		return nil, fmt.Errorf("列出OSS对象失败: %w", err)
	}

	var keys []string
	for _, object := range lsRes.Objects {
		keys = append(keys, object.Key)
	}

	logrus.WithFields(logrus.Fields{
		"prefix": prefix,
		"count":  len(keys),
	}).Info("列出OSS对象成功")
	return keys, nil
}

// extractMainVersion 提取主版本号（去掉commit ID部分）
// 例如: "1.16.5-82a469fa" -> "1.16.5", "1.16.5" -> "1.16.5"
func extractMainVersion(version string) string {
	if idx := strings.Index(version, "-"); idx != -1 {
		return version[:idx]
	}
	return version
}

// GetDesktopOssObjectKey 根据文件名、环境和版本生成桌面客户端的OSS对象键
// 按照特定的格式生成路径，例如:
// app/ngiq-p-cloud-desktop/linux/latest-linux.yml
// app/ngiq-p-cloud-desktop/win/1.16.3.yml
// app/ngiq-p-cloud-desktop/mac/ngiq-p-cloud-desktop-469e9c00-app-cn-1.16.3.dmg
func GetDesktopOssObjectKey(fileName, env, version string) ([]string, error) {
	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"env":      env,
		"version":  version,
	}).Debug("生成桌面客户端OSS对象键")

	// 从文件名中提取基本信息
	var osType string
	var objectKeys []string

	// 根据文件名判断操作系统类型
	switch {
	case strings.Contains(fileName, "linux") || strings.Contains(fileName, "AppImage"):
		osType = "linux"
	case strings.Contains(fileName, "win") || strings.Contains(fileName, "exe"):
		osType = "win"
	case strings.Contains(fileName, "mac") || strings.Contains(fileName, "dmg") || strings.Contains(fileName, "zip"):
		osType = "mac"
	default:
		// 无法确定操作系统类型，使用通用路径
		logrus.WithField("fileName", fileName).Warn("无法确定操作系统类型，使用通用路径")
		osType = "common"
	}
	basePath := fmt.Sprintf("%s/ngiq-p-cloud-desktop/%s", env, osType)

	// 提取主版本号（用于yml文件命名）
	mainVersion := extractMainVersion(version)

	// 根据文件类型处理不同的命名规则
	switch {
	case strings.Contains(fileName, "latest-linux"):
		// Linux系统的更新文件需要保存两个版本
		objectKeys = append(objectKeys,
			fmt.Sprintf("%s/latest-linux.yml", basePath),
			fmt.Sprintf("%s/%s.yml", basePath, mainVersion))
	case strings.Contains(fileName, "latest-win"):
		// Windows系统的更新文件需要保存两个版本
		objectKeys = append(objectKeys,
			fmt.Sprintf("%s/latest.yml", basePath),
			fmt.Sprintf("%s/%s.yml", basePath, mainVersion))
	case strings.Contains(fileName, "latest-mac"):
		// Mac系统的更新文件需要保存两个版本
		objectKeys = append(objectKeys,
			fmt.Sprintf("%s/latest-mac.yml", basePath),
			fmt.Sprintf("%s/%s.yml", basePath, mainVersion))
	default:
		// 其他文件直接使用原文件名
		objectKeys = append(objectKeys, fmt.Sprintf("%s/%s", basePath, fileName))
	}

	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"env":      env,
		"version":  version,
		"osType":   osType,
		"ossPaths": objectKeys,
	}).Info("生成桌面客户端OSS对象键成功")

	return objectKeys, nil
}

// GetOssObjectKey 根据文件名、环境和版本生成OSS对象键
// 这个函数主要用于FC部署，生成类似 env/latest/filename 和 env/version/filename 的路径
func GetOssObjectKey(fileName, env, version string) ([]string, error) {
	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"env":      env,
		"version":  version,
	}).Debug("生成OSS对象键")

	// 对于FC部署相关的文件，通常是zip、jar等类型
	lowerFile := strings.ToLower(fileName)
	isAllowedExt := strings.HasSuffix(lowerFile, ".zip") ||
		strings.HasSuffix(lowerFile, ".jar") ||
		strings.HasSuffix(lowerFile, ".exe") ||
		strings.HasSuffix(lowerFile, ".msi")

	// 如果文件不是常见的部署文件类型，记录警告但仍继续处理
	if !isAllowedExt {
		logrus.WithField("fileName", fileName).Warn("不常见的部署文件类型，但仍会处理")
	}

	// 从文件名中提取基本名称（排除路径和扩展名）
	baseName := filepath.Base(fileName)
	ext := filepath.Ext(fileName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)
	logrus.WithFields(logrus.Fields{
		"baseName":       baseName,
		"ext":            ext,
		"nameWithoutExt": nameWithoutExt,
	}).Debug("处理文件名")

	// 提取版本信息，去掉文件名中可能包含的时间戳
	var objectKeys []string

	// 构建不同的OSS路径
	// 1. 最新版本路径: env/latest/filename
	latestPath := fmt.Sprintf("%s/latest/%s", env, baseName)
	objectKeys = append(objectKeys, latestPath)

	// 2. 版本特定路径: env/version/filename
	versionPath := fmt.Sprintf("%s/%s/%s", env, version, baseName)
	if latestPath != versionPath {
		objectKeys = append(objectKeys, versionPath)
	}

	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"env":      env,
		"version":  version,
		"ossPaths": objectKeys,
	}).Info("生成OSS对象键成功")
	return objectKeys, nil
}
