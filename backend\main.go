package main

// 引入必要的依赖包
import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"net/http"
	"os"
	"runtime"
	"strings"
	"sync"
	"time"

	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/aliyun/fc"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/aliyun/oss"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/auth"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/config"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/feishu"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/gitlab"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/utils"
	alioss "github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/sirupsen/logrus"
)

// 全局配置变量
var globalConfig config.Config

// 全局服务实例
var (
	keycloakService *auth.KeycloakService
	feishuService   *feishu.FeishuService
)

// 全局变量存储zipFile内容（用于dev环境部署）
var deploymentZipFileContent string

// isDevEnvironment 判断是否为dev环境
func isDevEnvironment(env string) bool {
	devEnvs := []string{"pisces", "leo", "virgo", "aries"}
	for _, devEnv := range devEnvs {
		if env == devEnv {
			return true
		}
	}
	return false
}

// 命令行参数
var (
	configPath     string
	logLevel       string
	buildTimestamp string // 构建时间
)

// UploadParams 定义上传请求参数
type UploadParams struct {
	Version   string `json:"version"`   // 要上传的版本
	TargetEnv string `json:"targetEnv"` // 目标环境，如dev, prod
}

// LambdaDeployParams 定义Lambda部署请求参数
type LambdaDeployParams struct {
	ServiceType  string `json:"serviceType"`  // 服务类型，如ngiq-lambda
	Version      string `json:"version"`      // 要部署的版本
	Env          string `json:"env"`          // 环境，如dev, staging, prod
	ServiceName  string `json:"serviceName"`  // FC服务名称
	FunctionName string `json:"functionName"` // FC函数名称
	OssObjectKey string `json:"ossObjectKey"` // 可选，自定义OSS路径
}

// TokenRequest 定义获取令牌的请求参数
type TokenRequest struct {
	Code        string `json:"code"`
	RedirectURI string `json:"redirect_uri"`
}

// DiagnosticsResponse 系统诊断信息响应
type DiagnosticsResponse struct {
	Timestamp        string           `json:"timestamp"`
	GitLabToken      bool             `json:"gitlabToken"`      // GitLab令牌是否设置
	OssAccessKey     bool             `json:"osAccessKey"`      // OSS访问密钥是否设置
	FcAccessKey      bool             `json:"fcAccessKey"`      // FC访问密钥是否设置
	ConfigLoaded     bool             `json:"configLoaded"`     // 配置是否加载成功
	Version          string           `json:"version"`          // 系统版本
	AuthEnabled      bool             `json:"authEnabled"`      // 认证是否启用
	FeishuEnabled    bool             `json:"feishuEnabled"`    // 飞书通知是否启用
	GitLabConnection ConnectionStatus `json:"gitlabConnection"` // GitLab连接状态
}

// ConnectionStatus 连接状态
type ConnectionStatus struct {
	Status  string `json:"status"`  // 连接状态，ok或error
	Message string `json:"message"` // 连接消息
}

// DesktopDiagnosticsResponse 桌面客户端系统诊断信息响应 (不包含FC密钥)
type DesktopDiagnosticsResponse struct {
	Timestamp        string           `json:"timestamp"`
	GitLabToken      bool             `json:"gitlabToken"`      // GitLab令牌是否设置
	OssAccessKey     bool             `json:"osAccessKey"`      // OSS访问密钥是否设置 (检查DEV密钥)
	ConfigLoaded     bool             `json:"configLoaded"`     // 配置是否加载成功
	Version          string           `json:"version"`          // 系统版本
	AuthEnabled      bool             `json:"authEnabled"`      // 认证是否启用
	FeishuEnabled    bool             `json:"feishuEnabled"`    // 飞书通知是否启用
	GitLabConnection ConnectionStatus `json:"gitlabConnection"` // GitLab连接状态
}

// 创建一个简单的请求缓存，用于防止短时间内重复处理相同的授权码
type tokenRequestCache struct {
	processedCodes map[string]tokenCacheEntry // 使用结构体保存更多信息
	mutex          sync.RWMutex
	ttl            time.Duration
}

// 缓存条目结构体，保存认证结果
type tokenCacheEntry struct {
	timestamp   time.Time           // 处理时间
	tokenResp   *auth.TokenResponse // 令牌响应
	userInfo    *auth.UserInfo      // 用户信息
	statusCode  int                 // HTTP状态码
	errorMsg    string              // 错误信息
	resultCache []byte              // 缓存的JSON响应
}

// 全局缓存实例
var tokenCache = tokenRequestCache{
	processedCodes: make(map[string]tokenCacheEntry),
	ttl:            10 * time.Minute, // 延长授权码缓存时间到10分钟
}

// 检查授权码并返回缓存结果
func (c *tokenRequestCache) checkAndGetCachedResult(code string) (bool, *tokenCacheEntry) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// 清理过期缓存
	now := time.Now()
	for k, entry := range c.processedCodes {
		if now.Sub(entry.timestamp) > c.ttl {
			delete(c.processedCodes, k)
		}
	}

	// 检查码是否已处理
	codeHash := fmt.Sprintf("%x", sha256.Sum256([]byte(code)))
	entry, exists := c.processedCodes[codeHash]

	if exists {
		// 更新时间戳以延长有效期
		entry.timestamp = now
		c.processedCodes[codeHash] = entry
		return true, &entry
	}

	return false, nil
}

// 记录授权结果
func (c *tokenRequestCache) recordResult(code string, statusCode int, errorMsg string, tokenResp *auth.TokenResponse, userInfo *auth.UserInfo, resultCache []byte) {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	codeHash := fmt.Sprintf("%x", sha256.Sum256([]byte(code)))
	c.processedCodes[codeHash] = tokenCacheEntry{
		timestamp:   time.Now(),
		tokenResp:   tokenResp,
		userInfo:    userInfo,
		statusCode:  statusCode,
		errorMsg:    errorMsg,
		resultCache: resultCache,
	}
}

// parseLogLevel 将字符串日志级别转换为logrus.Level
func parseLogLevel(level string) logrus.Level {
	switch strings.ToLower(level) {
	case "debug":
		return logrus.DebugLevel
	case "info":
		return logrus.InfoLevel
	case "warn", "warning":
		return logrus.WarnLevel
	case "error":
		return logrus.ErrorLevel
	case "fatal":
		return logrus.FatalLevel
	default:
		return logrus.InfoLevel // 默认为 Info 级别
	}
}

// 初始化函数，加载配置
func init() {
	// 定义并解析命令行参数
	flag.StringVar(&configPath, "config", "", "配置文件路径")
	flag.StringVar(&logLevel, "log-level", "", "日志级别：debug, info, warn, error, fatal")
	flag.Parse()

	// 临时设置基本日志格式，用于记录配置加载过程的日志
	logrus.SetFormatter(&logrus.TextFormatter{
		FullTimestamp:   true,
		TimestampFormat: "2006-01-02 15:04:05",
		DisableColors:   false,
	})

	// 确定配置文件路径
	if configPath == "" {
		// 从环境变量获取配置文件路径
		configPath = os.Getenv("CONFIG_PATH")
		if configPath == "" {
			configPath = "config.yaml" // 默认配置文件路径
		}
	}

	// 加载配置
	err := globalConfig.LoadConfig(configPath)
	if err != nil {
		// 配置加载失败时使用默认日志设置
		logrus.SetLevel(logrus.InfoLevel)
		logrus.Fatalf("加载配置失败: %v", err)
	}

	logrus.Info("配置加载成功")

	// 设置日志级别，命令行参数优先于配置文件
	var logLevelValue logrus.Level
	// 如果命令行指定了日志级别，则使用命令行指定的日志级别
	if logLevel != "" {
		logLevelValue = parseLogLevel(logLevel)
		if logLevelValue == logrus.InfoLevel && !strings.EqualFold(logLevel, "info") {
			// 如果返回了默认的Info级别，但输入不是"info"，说明输入的日志级别无效
			logrus.Warnf("无效的日志级别: %s，使用配置文件中的设置", logLevel)
			logLevel = "" // 重置命令行参数，使用配置文件的设置
		}
	}

	// 如果命令行未指定有效的日志级别，则使用配置文件中的设置
	if logLevel == "" {
		logLevelValue = parseLogLevel(globalConfig.LoggingConfig.Level)
	}
	logrus.SetLevel(logLevelValue)

	// 设置日志格式
	if strings.ToLower(globalConfig.LoggingConfig.Format) == "json" {
		logrus.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		// 默认使用文本格式
		logrus.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
			DisableColors:   false,
		})
	}

	// 设置日志输出
	if globalConfig.LoggingConfig.OutputFile && globalConfig.LoggingConfig.FilePath != "" {
		logFile, err := os.OpenFile(globalConfig.LoggingConfig.FilePath, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err == nil {
			logrus.SetOutput(io.MultiWriter(os.Stdout, logFile))
		} else {
			logrus.Warnf("无法打开日志文件: %v", err)
		}
	}

	// 记录最终使用的日志级别
	actualLevel := "info"
	if logLevel != "" {
		actualLevel = logLevel
	} else {
		actualLevel = globalConfig.LoggingConfig.Level
	}
	logrus.Info("日志系统初始化完成，日志级别: " + actualLevel)

	// 记录GitLab Base URL
	logrus.WithField("GitLabBaseURL", globalConfig.GitLabConfig.BaseUrl).Info("GitLab配置")

	// 检查关键环境变量
	checkEnvVar := func(name string) {
		if os.Getenv(name) == "" {
			logrus.Warnf("环境变量 %s 未设置", name)
		} else {
			logrus.Debugf("环境变量 %s 已设置", name)
		}
	}

	checkEnvVar("GITLAB_TOKEN")
	checkEnvVar("PROD_OSS_ACCESS_KEY_ID")
	checkEnvVar("PROD_OSS_ACCESS_KEY_SECRET")
	checkEnvVar("PROD_FC_ACCESS_KEY_ID")
	checkEnvVar("PROD_FC_ACCESS_KEY_SECRET")
	checkEnvVar("PROD_FC_ENDPOINT")
	checkEnvVar("DEV_OSS_ACCESS_KEY_ID")
	checkEnvVar("DEV_OSS_ACCESS_KEY_SECRET")
	checkEnvVar("DEV_FC_ACCESS_KEY_ID")
	checkEnvVar("DEV_FC_ACCESS_KEY_SECRET")
	checkEnvVar("DEV_FC_ENDPOINT")

	// 初始化认证服务
	keycloakService = auth.NewKeycloakService(globalConfig.AuthConfig)
	if globalConfig.AuthConfig.Enabled {
		logrus.Info("SSO认证服务已启用")
	} else {
		logrus.Info("SSO认证服务已禁用")
	}

	// 初始化飞书服务
	feishuService = feishu.NewFeishuService(globalConfig.FeishuConfig)
	if globalConfig.FeishuConfig.Enabled {
		logrus.Info("飞书通知服务已启用")
	} else {
		logrus.Info("飞书通知服务已禁用")
	}
}

// handleVersions 处理获取版本列表的HTTP请求
func handleVersions(w http.ResponseWriter, r *http.Request) {
	// 创建GitLab客户端
	gitlabClient, err := gitlab.NewClient(
		globalConfig.GitLabConfig.BaseUrl,
		os.Getenv("GITLAB_TOKEN"),
		globalConfig.DesktopConfig.GitLabConfig.ProjectId,
		globalConfig.DesktopConfig.GitLabConfig.ProjectName,
		globalConfig.DesktopConfig.GitLabConfig.PackageName,
	)
	if err != nil {
		http.Error(w, "创建GitLab客户端失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 获取版本列表
	versions, err := gitlabClient.GetVersions(20) // 获取最近20个版本
	if err != nil {
		http.Error(w, "获取版本列表失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回版本列表
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(versions)
}

// handleLambdaServices 处理获取可用的Lambda服务列表的HTTP请求
func handleLambdaServices(w http.ResponseWriter, r *http.Request) {
	// 提取服务基本信息
	services := make([]map[string]interface{}, 0, len(globalConfig.LambdaConfig.Services))
	for _, service := range globalConfig.LambdaConfig.Services {
		services = append(services, map[string]interface{}{
			"name": service.Name,
			"id":   service.ProjectID,
		})
	}

	// 返回服务列表
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(services)
}

// handleLambdaVersions 处理获取Lambda版本列表的HTTP请求
func handleLambdaVersions(w http.ResponseWriter, r *http.Request) {
	serviceName := r.URL.Query().Get("service")
	if serviceName == "" {
		// 默认使用第一个服务
		if len(globalConfig.LambdaConfig.Services) > 0 {
			serviceName = globalConfig.LambdaConfig.Services[0].Name
		} else {
			http.Error(w, "未配置Lambda服务", http.StatusInternalServerError)
			return
		}
	}

	// 查找服务配置
	var serviceConfig *config.LambdaService
	for _, service := range globalConfig.LambdaConfig.Services {
		if service.Name == serviceName {
			serviceConfig = &service
			break
		}
	}

	if serviceConfig == nil {
		http.Error(w, "未找到指定的服务: "+serviceName, http.StatusBadRequest)
		return
	}

	// 创建GitLab客户端
	gitlabClient, err := gitlab.NewClient(
		globalConfig.GitLabConfig.BaseUrl,
		os.Getenv("GITLAB_TOKEN"),
		serviceConfig.ProjectID,
		serviceConfig.ProjectName,
		serviceConfig.PackageName,
	)
	if err != nil {
		http.Error(w, "创建GitLab客户端失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 验证GitLab配置
	logrus.WithFields(logrus.Fields{
		"service":     serviceName,
		"projectID":   serviceConfig.ProjectID,
		"projectName": serviceConfig.ProjectName,
		"operation":   "handleLambdaVersions",
	}).Info("验证GitLab配置")
	if err := gitlabClient.ValidateConfig(); err != nil {
		logrus.WithError(err).Error("GitLab配置验证失败")
		http.Error(w, "GitLab配置验证失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 获取版本列表
	versions, err := gitlabClient.GetVersions(10)
	if err != nil {
		http.Error(w, "获取版本列表失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回版本列表
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(versions)
}

// sendProgressUpdate 发送进度更新到客户端
func sendProgressUpdate(w http.ResponseWriter, flusher http.Flusher, message string, progress int, messageType string) {
	update := map[string]interface{}{
		"message":  message,
		"progress": progress,
	}
	if messageType != "" {
		update["type"] = messageType
	}
	err := json.NewEncoder(w).Encode(update)
	if err != nil {
		// 如果发送失败，记录错误，但可能无法通知客户端
		logrus.WithError(err).Error("发送进度更新失败")
		return
	}
	// 确保数据被发送
	if flusher != nil {
		flusher.Flush()
	}
}

// sendFinalUpdate 发送最终的完成状态更新到客户端
func sendFinalUpdate(w http.ResponseWriter, flusher http.Flusher, message string, progress int, messageType string, summary map[string]interface{}) {
	update := map[string]interface{}{
		"message":  message,
		"progress": progress,
		"done":     true, // 标记流程结束
	}
	if messageType != "" {
		update["type"] = messageType
	}
	if summary != nil {
		update["summary"] = summary
	}
	err := json.NewEncoder(w).Encode(update)
	if err != nil {
		logrus.WithError(err).Error("发送最终更新失败")
		return
	}
	if flusher != nil {
		flusher.Flush()
	}
}

// handleUpload 处理上传请求
func handleUpload(w http.ResponseWriter, r *http.Request) {
	// 如果认证已启用，验证用户身份
	var userInfo *auth.UserInfo
	if globalConfig.AuthConfig.Enabled {
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "未授权: 缺少Authorization头", http.StatusUnauthorized)
			return // 注意：这里的错误无法通过SSE发送最终消息，因为还没设置SSE头
		}
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			http.Error(w, "未授权: 无效的Authorization头格式", http.StatusUnauthorized)
			return // 同上
		}
		var err error
		userInfo, err = keycloakService.GetUserInfo(tokenParts[1])
		if err != nil {
			http.Error(w, "未授权: 获取用户信息失败 - "+err.Error(), http.StatusUnauthorized)
			return // 同上
		}
		logrus.WithField("user", userInfo.PreferredUsername).Info("用户已认证")
	}

	// 解析请求体
	decoder := json.NewDecoder(r.Body)
	var uploadParams UploadParams
	err := decoder.Decode(&uploadParams)
	if err != nil {
		http.Error(w, "解析请求失败: "+err.Error(), http.StatusBadRequest)
		return // 同上
	}

	// 验证参数
	if uploadParams.Version == "" || uploadParams.TargetEnv == "" {
		http.Error(w, "版本号和目标环境不能为空", http.StatusBadRequest)
		return // 同上
	}

	// 设置响应头，使用SSE进行实时进度更新
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("X-Accel-Buffering", "no") // 禁用Nginx等代理的缓冲

	// 创建流式响应的刷新器
	flusher, ok := w.(http.Flusher)
	if !ok {
		// 如果不支持流式响应，记录错误并返回。无法发送最终消息。
		logrus.Error("服务器不支持流式响应")
		http.Error(w, "服务器不支持流式响应", http.StatusInternalServerError)
		return
	}

	// 包装发送函数
	_sendProgressUpdate := func(message string, progress int, messageType string) {
		sendProgressUpdate(w, flusher, message, progress, messageType)
	}
	_sendFinalUpdate := func(message string, progress int, messageType string, summary map[string]interface{}) {
		sendFinalUpdate(w, flusher, message, progress, messageType, summary)
	}

	// === 开始上传逻辑 ===
	_sendProgressUpdate("开始处理上传请求...", 0, "info")

	// 获取GitLab客户端
	_sendProgressUpdate("正在初始化GitLab连接...", 5, "info")
	gitlabClient, err := gitlab.NewClient(
		globalConfig.GitLabConfig.BaseUrl,
		os.Getenv("GITLAB_TOKEN"),
		globalConfig.DesktopConfig.GitLabConfig.ProjectId,
		globalConfig.DesktopConfig.GitLabConfig.ProjectName,
		globalConfig.DesktopConfig.GitLabConfig.PackageName,
	)
	if err != nil {
		errMsg := "创建GitLab客户端失败: " + err.Error()
		logrus.Error(errMsg)
		_sendFinalUpdate(errMsg, 0, "error", map[string]interface{}{
			"note":    "初始化GitLab连接失败",
			"env":     uploadParams.TargetEnv,
			"version": uploadParams.Version,
		})
		return
	}
	_sendProgressUpdate("GitLab连接初始化成功", 10, "info")

	// 获取OSS客户端
	_sendProgressUpdate("正在初始化OSS连接...", 15, "info")
	ossClient, err := oss.NewClient(
		globalConfig.DesktopConfig.OssConfig.Endpoint,
		os.Getenv("DEV_OSS_ACCESS_KEY_ID"),
		os.Getenv("DEV_OSS_ACCESS_KEY_SECRET"),
		globalConfig.DesktopConfig.OssConfig.BucketName,
	)
	if err != nil {
		errMsg := "创建OSS客户端失败: " + err.Error()
		logrus.Error(errMsg)
		_sendFinalUpdate(errMsg, 15, "error", map[string]interface{}{
			"note":    "初始化OSS连接失败",
			"env":     uploadParams.TargetEnv,
			"version": uploadParams.Version,
		})
		return
	}
	_sendProgressUpdate("OSS连接初始化成功", 20, "info")

	// 获取文件列表
	_sendProgressUpdate("正在从GitLab获取文件列表...", 25, "info")
	files, err := gitlabClient.GetPackageFiles(uploadParams.Version)
	if err != nil {
		errMsg := "获取文件列表失败: " + err.Error()
		logrus.Error(errMsg)
		_sendFinalUpdate(errMsg, 25, "error", map[string]interface{}{
			"note":    "无法从GitLab获取文件列表",
			"env":     uploadParams.TargetEnv,
			"version": uploadParams.Version,
		})
		return
	}

	// --- 文件过滤逻辑 ---
	_sendProgressUpdate(fmt.Sprintf("目标环境 '%s'，开始筛选相关文件", uploadParams.TargetEnv), 27, "info")

	var filteredFiles []gitlab.PackageFile

	// 第一步：收集所有yml配置文件（不考虑环境）
	for _, file := range files {
		if strings.HasSuffix(strings.ToLower(file.FileName), ".yml") {
			filteredFiles = append(filteredFiles, file)
			logrus.WithFields(logrus.Fields{"file": file.FileName, "type": "config"}).Debug("添加配置文件")
		}
	}

	// 第二步：收集非yml文件，优先当前环境，找不到用app环境
	nonYmlFiles := []gitlab.PackageFile{}
	for _, file := range files {
		if !strings.HasSuffix(strings.ToLower(file.FileName), ".yml") {
			nonYmlFiles = append(nonYmlFiles, file)
		}
	}

	// 先找当前环境的非yml文件
	currentEnvFiles := []gitlab.PackageFile{}
	for _, file := range nonYmlFiles {
		if strings.Contains(strings.ToLower(file.FileName), strings.ToLower(uploadParams.TargetEnv)) {
			currentEnvFiles = append(currentEnvFiles, file)
			logrus.WithFields(logrus.Fields{"file": file.FileName, "env": uploadParams.TargetEnv}).Debug("找到当前环境文件")
		}
	}

	// 如果找到当前环境文件就用当前环境的，否则用app环境的
	if len(currentEnvFiles) > 0 {
		filteredFiles = append(filteredFiles, currentEnvFiles...)
		logrus.WithFields(logrus.Fields{"env": uploadParams.TargetEnv, "count": len(currentEnvFiles)}).Info("使用当前环境的文件")
	} else {
		// 回退到app环境
		appEnvFiles := []gitlab.PackageFile{}
		for _, file := range nonYmlFiles {
			if strings.Contains(strings.ToLower(file.FileName), "app") {
				appEnvFiles = append(appEnvFiles, file)
				logrus.WithFields(logrus.Fields{"file": file.FileName, "env": "app"}).Debug("回退到app环境文件")
			}
		}
		filteredFiles = append(filteredFiles, appEnvFiles...)
		logrus.WithFields(logrus.Fields{"targetEnv": uploadParams.TargetEnv, "count": len(appEnvFiles)}).Info("未找到当前环境文件，回退到app环境")
	}

	// 更新使用过滤后的文件列表
	files = filteredFiles

	if len(files) == 0 {
		errMsg := fmt.Sprintf("在版本 %s 中未找到环境 '%s' 对应的文件", uploadParams.Version, uploadParams.TargetEnv)
		logrus.Warn(errMsg)
		_sendFinalUpdate(errMsg, 28, "warning", map[string]interface{}{
			"note":    fmt.Sprintf("版本 %s 在GitLab中没有找到相关文件", uploadParams.Version),
			"total":   0,
			"success": 0,
			"failed":  0,
			"env":     uploadParams.TargetEnv,
			"version": uploadParams.Version,
		})
		return
	}

	_sendProgressUpdate(fmt.Sprintf("找到 %d 个文件需要处理", len(files)), 30, "info")

	// 处理每个文件
	totalFiles := len(files) // 使用过滤后的文件数量
	successCount := 0
	failedFiles := []string{}
	failedFileDetails := map[string]string{}

	// 新增：收集详细的成功和失败信息
	successFiles := []string{}
	successDetails := map[string][]string{} // 文件名 -> OSS路径
	failedReasons := map[string]string{}    // 文件名 -> 失败原因

	for i, file := range files {
		progress := 30 + int(float64(i)/float64(totalFiles)*60) // 修改进度计算，预留10%给最终处理
		_sendProgressUpdate(fmt.Sprintf("开始处理文件: %s (%d/%d)", file.FileName, i+1, totalFiles), progress, "info")

		// 下载文件
		downloadMsg := fmt.Sprintf("正在从GitLab下载文件: %s", file.FileName)
		_sendProgressUpdate(downloadMsg, progress, "info")
		content, err := gitlabClient.DownloadFile(file.URL, file.FileName)
		if err != nil {
			errDetail := fmt.Sprintf("下载失败: %s", err.Error())
			failedFiles = append(failedFiles, file.FileName)
			failedFileDetails[file.FileName] = errDetail
			_sendProgressUpdate(fmt.Sprintf("%s - %s", downloadMsg, errDetail), progress, "error")
			continue
		}

		// 生成OSS对象键 - 仍然使用原始的 TargetEnv
		genKeyMsg := fmt.Sprintf("正在为文件 %s 生成目标环境 '%s' 的OSS路径", file.FileName, uploadParams.TargetEnv)
		_sendProgressUpdate(genKeyMsg, progress, "info")
		objectKeys, err := oss.GetDesktopOssObjectKey(file.FileName, uploadParams.TargetEnv, uploadParams.Version)
		if err != nil {
			errDetail := fmt.Sprintf("生成OSS路径失败: %s", err.Error())
			failedFiles = append(failedFiles, file.FileName)
			failedFileDetails[file.FileName] = errDetail
			_sendProgressUpdate(fmt.Sprintf("%s - %s", genKeyMsg, errDetail), progress, "error")
			continue
		}

		// 上传到OSS
		fileUploaded := true
		var uploadedPaths []string // 记录成功上传的路径

		for _, objectKey := range objectKeys {
			uploadMsg := fmt.Sprintf("正在上传到OSS: %s -> %s", file.FileName, objectKey)
			_sendProgressUpdate(uploadMsg, progress, "info")
			err = ossClient.UploadFileWithACL(objectKey, content, alioss.ACLPublicRead)
			if err != nil {
				fileUploaded = false
				errDetail := fmt.Sprintf("上传到 %s 失败: %s", objectKey, err.Error())
				// 记录失败原因到新的数据结构
				if _, exists := failedReasons[file.FileName]; exists {
					failedReasons[file.FileName] += "; " + errDetail
				} else {
					failedReasons[file.FileName] = errDetail
				}
				// 保持原有的failedFileDetails用于其他逻辑
				if _, exists := failedFileDetails[file.FileName]; exists {
					failedFileDetails[file.FileName] += "; " + errDetail
				} else {
					failedFileDetails[file.FileName] = errDetail
				}
				_sendProgressUpdate(fmt.Sprintf("%s - %s", uploadMsg, errDetail), progress, "error")
			} else {
				uploadedPaths = append(uploadedPaths, objectKey)
			}
		}

		// 文件处理结果记录
		if fileUploaded {
			successCount++
			successFiles = append(successFiles, file.FileName)
			successDetails[file.FileName] = uploadedPaths
			// 更新进度，每个文件完成后增加一点额外进度
			progress = 30 + int(float64(successCount)/float64(totalFiles)*65) // 调整成功后的进度计算
			_sendProgressUpdate(fmt.Sprintf("文件处理成功: %s (%d/%d)", file.FileName, successCount, totalFiles), progress, "info")
		} else {
			failedFiles = append(failedFiles, file.FileName)
			// 如果上传失败，确保在日志中记录
			logrus.WithFields(logrus.Fields{
				"file":    file.FileName,
				"env":     uploadParams.TargetEnv,
				"version": uploadParams.Version,
				"error":   failedFileDetails[file.FileName],
			}).Warn("文件处理失败")
		}
	}

	// === 文件处理循环结束 ===
	finalProgress := 95 // 文件处理完成后的基础进度

	// 准备最终的摘要信息
	summary := map[string]interface{}{
		"total":          totalFiles,
		"success":        successCount,
		"failed":         len(failedFiles),
		"successList":    successFiles,
		"successDetails": successDetails,
		"failedList":     failedFiles,
		"failedReasons":  failedReasons,
		"failList":       failedFiles,       // 保持向后兼容
		"failDetails":    failedFileDetails, // 保持向后兼容
		"env":            uploadParams.TargetEnv,
		"version":        uploadParams.Version,
	}

	// 发送飞书通知
	if globalConfig.FeishuConfig.Enabled && userInfo != nil {
		_sendProgressUpdate("正在发送飞书通知...", finalProgress, "info")
		// 调用 feishuService.NotifyDesktopUploadCard (使用消息卡片格式)
		if err := feishuService.NotifyDesktopUploadCard(userInfo, uploadParams.Version, uploadParams.TargetEnv, summary); err != nil {
			logrus.WithError(err).Warn("发送上传通知失败")
			_sendProgressUpdate("发送飞书通知失败: "+err.Error(), finalProgress, "warning")
		} else {
			_sendProgressUpdate("飞书通知发送成功", finalProgress, "info")
		}
	}

	// 根据处理结果发送最终消息 <--- 最终的SSE消息在这里发送
	var finalMsg string
	var finalType string
	if len(failedFiles) > 0 {
		// 有文件处理失败
		finalMsg = fmt.Sprintf("上传部分完成，%d/%d 个文件成功，%d 个文件失败",
			successCount, totalFiles, len(failedFiles))
		finalType = "warning"
		_sendFinalUpdate(finalMsg, 100, finalType, summary) // 部分成功
		logrus.WithFields(logrus.Fields(summary)).Warn(finalMsg)
	} else {
		// 所有文件都处理成功
		finalMsg = fmt.Sprintf("上传完成！所有 %d 个文件处理成功", totalFiles)
		finalType = "success"
		_sendFinalUpdate(finalMsg, 100, finalType, summary) // 完全成功
		logrus.WithFields(logrus.Fields(summary)).Info(finalMsg)
	}
}

// handleLambdaDeploy 处理Lambda部署请求
func handleLambdaDeploy(w http.ResponseWriter, r *http.Request) {
	// 如果认证已启用，验证用户身份
	var userInfo *auth.UserInfo
	if globalConfig.AuthConfig.Enabled {
		// 检查Authorization头
		authHeader := r.Header.Get("Authorization")
		if authHeader == "" {
			http.Error(w, "未授权: 缺少Authorization头", http.StatusUnauthorized)
			return
		}

		// 提取令牌
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			http.Error(w, "未授权: 无效的Authorization头格式", http.StatusUnauthorized)
			return
		}

		// 获取用户信息
		var err error
		userInfo, err = keycloakService.GetUserInfo(tokenParts[1])
		if err != nil {
			http.Error(w, "未授权: 获取用户信息失败", http.StatusUnauthorized)
			return
		}
	}

	// 添加defer语句捕获可能的panic
	defer func() {
		if r := recover(); r != nil {
			// 记录panic信息
			stack := make([]byte, 4096)
			stack = stack[:runtime.Stack(stack, false)]
			errMsg := fmt.Sprintf("Lambda部署过程发生严重错误: %v", r)
			logrus.Errorf("%s\n%s", errMsg, stack)

			// 尝试发送错误消息到客户端
			if w.Header().Get("Content-Type") != "" {
				// 头部已经发送，尝试写入错误消息
				encoder := json.NewEncoder(w)
				encoder.Encode(map[string]interface{}{
					"message":  errMsg,
					"type":     "error",
					"progress": 0,
				})
				w.(http.Flusher).Flush()
			} else {
				// 头部未发送，返回HTTP错误
				http.Error(w, errMsg, http.StatusInternalServerError)
			}
		}
	}()

	// 解析请求体
	decoder := json.NewDecoder(r.Body)
	var deployParams LambdaDeployParams
	err := decoder.Decode(&deployParams)
	if err != nil {
		http.Error(w, "解析请求失败: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 验证参数
	if deployParams.ServiceType == "" {
		http.Error(w, "服务类型不能为空", http.StatusBadRequest)
		return
	}
	if deployParams.Version == "" {
		http.Error(w, "版本号不能为空", http.StatusBadRequest)
		return
	}
	if deployParams.Env == "" {
		http.Error(w, "环境不能为空", http.StatusBadRequest)
		return
	}

	// 设置响应头，使用SSE进行实时进度更新
	w.Header().Set("Content-Type", "text/event-stream")
	w.Header().Set("Cache-Control", "no-cache")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("X-Accel-Buffering", "no")

	// 创建流式响应的刷新器
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.Error(w, "服务器不支持流式响应", http.StatusInternalServerError)
		return
	}

	// 发送初始进度更新
	sendProgressUpdate := func(message string, progress int, messageType string) {
		update := map[string]interface{}{
			"message":  message,
			"progress": progress,
		}
		if messageType != "" {
			update["type"] = messageType
		}
		logrus.WithFields(logrus.Fields{
			"progress": progress,
			"message":  message,
			"type":     messageType,
		}).Debug("发送进度更新")
		json.NewEncoder(w).Encode(update)
		flusher.Flush()
	}

	// 通知客户端部署开始
	logrus.WithFields(logrus.Fields{
		"serviceType": deployParams.ServiceType,
		"version":     deployParams.Version,
		"env":         deployParams.Env,
	}).Info("开始处理Lambda部署请求")
	sendProgressUpdate("开始部署处理...", 0, "info")

	// 获取GitLab客户端
	gitlabClient, err := func() (*gitlab.Client, error) {
		// 找到对应的Lambda服务配置
		for _, service := range globalConfig.LambdaConfig.Services {
			if service.Name == deployParams.ServiceType {
				logrus.WithFields(logrus.Fields{
					"serviceType": deployParams.ServiceType,
					"projectID":   service.ProjectID,
					"projectName": service.ProjectName,
				}).Debug("找到匹配的服务配置")
				return gitlab.NewClient(
					globalConfig.GitLabConfig.BaseUrl,
					os.Getenv("GITLAB_TOKEN"),
					service.ProjectID,
					service.ProjectName,
					service.PackageName,
				)
			}
		}
		return nil, fmt.Errorf("未找到服务类型: %s", deployParams.ServiceType)
	}()

	if err != nil {
		logrus.WithError(err).Error("创建GitLab客户端失败")
		sendProgressUpdate("创建GitLab客户端失败: "+err.Error(), 0, "error")
		return
	}

	// 获取版本列表
	logrus.Debug("正在获取版本信息...")
	sendProgressUpdate("正在获取版本信息...", 5, "info")
	versions, err := gitlabClient.GetVersions(10)
	if err != nil {
		logrus.WithError(err).Error("获取版本列表失败")
		sendProgressUpdate("获取版本列表失败: "+err.Error(), 0, "error")
		return
	}

	// 验证版本是否存在
	versionExists := false
	for _, v := range versions {
		if v.Version == deployParams.Version {
			versionExists = true
			break
		}
	}

	if !versionExists {
		logrus.WithField("version", deployParams.Version).Error("请求的版本不存在")
		sendProgressUpdate("请求的版本不存在: "+deployParams.Version, 0, "error")
		return
	}

	logrus.WithFields(logrus.Fields{
		"serviceType":  deployParams.ServiceType,
		"version":      deployParams.Version,
		"env":          deployParams.Env,
		"serviceName":  deployParams.ServiceName,
		"functionName": deployParams.FunctionName,
	}).Info("接收到Lambda部署请求")

	// 创建上下文，支持取消
	ctx, cancel := context.WithCancel(r.Context())
	defer cancel()

	// 创建进度通道
	progressChan := make(chan map[string]interface{}, 10)

	// 定义一个辅助函数来发送进度消息并同时记录日志
	sendProgress := func(msg map[string]interface{}) {
		// 记录到日志
		msgType, _ := msg["type"].(string)
		msgText, _ := msg["message"].(string)

		logFields := logrus.Fields{}
		for k, v := range msg {
			if k != "type" && k != "message" {
				logFields[k] = v
			}
		}

		switch msgType {
		case "error":
			logrus.WithFields(logFields).Error(msgText)
		case "warning":
			logrus.WithFields(logFields).Warn(msgText)
		case "success":
			logrus.WithFields(logFields).Info(msgText)
		default:
			logrus.WithFields(logFields).Info(msgText)
		}

		// 发送到前端
		select {
		case progressChan <- msg:
			// 消息已发送
		default:
			logrus.Warn("无法发送进度消息到前端: 通道已满或已关闭")
		}
	}

	// 执行部署操作
	go func() {
		// 捕获goroutine中的panic
		defer func() {
			if r := recover(); r != nil {
				stack := make([]byte, 4096)
				stack = stack[:runtime.Stack(stack, false)]
				errMsg := fmt.Sprintf("部署过程中发生意外错误: %v", r)
				logrus.Errorf("%s\n%s", errMsg, stack)

				// 尝试发送错误消息
				sendProgress(map[string]interface{}{
					"message":  errMsg,
					"type":     "error",
					"progress": 0,
				})
			}
			// 关闭channel
			defer close(progressChan)
		}()

		// 查找服务配置
		var serviceConfig *config.LambdaService
		for _, service := range globalConfig.LambdaConfig.Services {
			if service.Name == deployParams.ServiceType {
				serviceConfig = &service
				break
			}
		}

		if serviceConfig == nil {
			errMsg := "未找到指定的服务: " + deployParams.ServiceType
			logrus.WithField("serviceType", deployParams.ServiceType).Error(errMsg)
			sendProgress(map[string]interface{}{
				"message": errMsg,
				"type":    "error",
			})
			return
		}

		// 创建GitLab客户端
		gitlabClient, err := gitlab.NewClient(
			globalConfig.GitLabConfig.BaseUrl,
			os.Getenv("GITLAB_TOKEN"),
			serviceConfig.ProjectID,
			serviceConfig.ProjectName,
			serviceConfig.PackageName,
		)
		if err != nil {
			errMsg := "创建GitLab客户端失败: " + err.Error()
			logrus.WithError(err).Error(errMsg)
			sendProgress(map[string]interface{}{
				"message": errMsg,
				"type":    "error",
			})
			return
		}

		// 验证GitLab配置
		logrus.WithFields(logrus.Fields{
			"serviceType": deployParams.ServiceType,
			"projectID":   serviceConfig.ProjectID,
			"projectName": serviceConfig.ProjectName,
			"version":     deployParams.Version,
			"env":         deployParams.Env,
			"operation":   "handleLambdaDeploy",
		}).Info("验证GitLab配置")
		validateErr := gitlabClient.ValidateConfig()
		if validateErr != nil {
			logrus.WithError(validateErr).Error("GitLab配置验证失败")
			sendProgress(map[string]interface{}{
				"message": "GitLab配置验证失败: " + validateErr.Error(),
				"type":    "error",
			})
			return
		}

		// 获取软件包文件列表
		files, err := gitlabClient.GetPackageFiles(deployParams.Version)
		if err != nil {
			errMsg := "获取函数包列表失败: " + err.Error()
			logrus.WithError(err).Error(errMsg)
			sendProgress(map[string]interface{}{
				"message": errMsg,
				"type":    "error",
			})
			return
		}

		// 创建OSS客户端
		ossClient, err := oss.NewClient(
			globalConfig.LambdaConfig.OssConfig.Endpoint,
			os.Getenv("PROD_OSS_ACCESS_KEY_ID"),
			os.Getenv("PROD_OSS_ACCESS_KEY_SECRET"),
			globalConfig.LambdaConfig.OssConfig.BucketName,
		)
		if err != nil {
			errMsg := "创建OSS客户端失败: " + err.Error()
			logrus.WithError(err).Error(errMsg)
			sendProgress(map[string]interface{}{
				"message": errMsg,
				"type":    "error",
			})
			return
		}

		// 根据服务类型和环境处理文件
		switch serviceConfig.Name {
		case "ngiq-lambda":
			if isDevEnvironment(deployParams.Env) {
				// dev环境：下载文件到内存，准备zipFile部署
				logrus.WithField("env", deployParams.Env).Info("使用dev环境zipFile部署方式")

				for _, file := range files {
					// 下载文件
					sendProgress(map[string]interface{}{
						"message":  "正在下载文件到内存: " + file.FileName,
						"progress": 30,
						"type":     "info",
					})

					content, err := gitlabClient.DownloadFile(file.URL, file.FileName)
					if err != nil {
						errMsg := "下载文件失败: " + err.Error()
						logrus.WithError(err).WithField("fileName", file.FileName).Error(errMsg)
						sendProgress(map[string]interface{}{
							"message": errMsg,
							"type":    "error",
						})
						continue
					}

					// ngiq-lambda已经是zip包，直接进行base64编码
					sendProgress(map[string]interface{}{
						"message":  "正在对ZIP文件进行base64编码: " + file.FileName,
						"progress": 50,
						"type":     "info",
					})

					base64Content, err := utils.EncodeZipToBase64(content, file.FileName)
					if err != nil {
						errMsg := "ZIP文件base64编码失败: " + err.Error()
						logrus.WithError(err).WithField("fileName", file.FileName).Error(errMsg)
						sendProgress(map[string]interface{}{
							"message": errMsg,
							"type":    "error",
						})
						continue
					}

					// 存储base64内容供后续FC部署使用
					deploymentZipFileContent = base64Content
					logrus.WithFields(logrus.Fields{
						"fileName":     file.FileName,
						"originalSize": len(content),
						"base64Length": len(base64Content),
					}).Info("ZIP文件准备完成，等待FC部署")
				}
			} else {
				// prod环境：下载所有文件并上传到OSS指定路径
				logrus.WithField("env", deployParams.Env).Info("使用prod环境OSS部署方式")

				for _, file := range files {
					// 下载文件
					sendProgress(map[string]interface{}{
						"message":  "正在下载文件: " + file.FileName,
						"progress": 30,
						"type":     "info",
					})

					content, err := gitlabClient.DownloadFile(file.URL, file.FileName)
					if err != nil {
						errMsg := "下载文件失败: " + err.Error()
						logrus.WithError(err).WithField("fileName", file.FileName).Error(errMsg)
						sendProgress(map[string]interface{}{
							"message": errMsg,
							"type":    "error",
						})
						continue
					}

					// 构建OSS路径
					ossPath := fmt.Sprintf("%s/%s/%s",
						serviceConfig.OssBasePath,
						deployParams.Version,
						file.FileName)

					// 上传到OSS
					sendProgress(map[string]interface{}{
						"message":  "正在上传文件到OSS: " + ossPath,
						"progress": 50,
						"type":     "info",
					})

					err = ossClient.UploadFileWithACL(ossPath, content, alioss.ACLDefault)
					if err != nil {
						errMsg := "上传文件到OSS失败: " + err.Error()
						logrus.WithError(err).WithField("ossPath", ossPath).Error(errMsg)
						sendProgress(map[string]interface{}{
							"message": errMsg,
							"type":    "error",
						})
					}
				}
			}

		case "target-report-word-export", "plan-report-pdf-export":
			// 优先查找FC专用的ZIP文件，如果没有则查找JAR文件
			var targetFile *gitlab.PackageFile
			var isZipFile bool

			// 首先查找FC专用ZIP文件（带-fc后缀）
			for _, file := range files {
				if strings.Contains(file.FileName, deployParams.Version) &&
					strings.Contains(file.FileName, "-fc.zip") {
					targetFile = &file
					isZipFile = true
					break
				}
			}

			// 如果没有找到ZIP文件，则查找JAR文件
			if targetFile == nil {
				for _, file := range files {
					if strings.Contains(file.FileName, deployParams.Version) && strings.HasSuffix(file.FileName, ".jar") {
						targetFile = &file
						isZipFile = false
						break
					}
				}
			}

			if targetFile == nil {
				errMsg := "未找到符合条件的JAR或ZIP文件"
				logrus.WithFields(logrus.Fields{
					"serviceType": serviceConfig.Name,
					"version":     deployParams.Version,
				}).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			// 下载目标文件
			fileType := "JAR"
			if isZipFile {
				fileType = "ZIP"
			}
			sendProgress(map[string]interface{}{
				"message":  "正在下载" + fileType + "文件: " + targetFile.FileName,
				"progress": 30,
				"type":     "info",
			})

			content, err := gitlabClient.DownloadFile(targetFile.URL, targetFile.FileName)
			if err != nil {
				errMsg := "下载" + fileType + "文件失败: " + err.Error()
				logrus.WithError(err).WithField("fileName", targetFile.FileName).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			if isDevEnvironment(deployParams.Env) {
				// dev环境：处理文件并进行base64编码
				logrus.WithField("env", deployParams.Env).Info("使用dev环境zipFile部署方式处理文件")

				var zipContent []byte
				if isZipFile {
					// 如果已经是ZIP文件，直接使用
					sendProgress(map[string]interface{}{
						"message":  "使用现有ZIP文件: " + targetFile.FileName,
						"progress": 50,
						"type":     "info",
					})
					zipContent = content
				} else {
					// 如果是JAR文件，重新打包为标准ZIP格式
					sendProgress(map[string]interface{}{
						"message":  "正在将JAR文件重新打包为ZIP格式: " + targetFile.FileName,
						"progress": 50,
						"type":     "info",
					})

					zipContent, err = utils.ConvertJarToZip(content, targetFile.FileName)
					if err != nil {
						errMsg := "JAR文件重新打包为ZIP失败: " + err.Error()
						logrus.WithError(err).WithField("fileName", targetFile.FileName).Error(errMsg)
						sendProgress(map[string]interface{}{
							"message": errMsg,
							"type":    "error",
						})
						return
					}
				}

				sendProgress(map[string]interface{}{
					"message":  "正在对ZIP包进行base64编码: " + targetFile.FileName,
					"progress": 70,
					"type":     "info",
				})

				// 对ZIP包进行base64编码
				base64Content, err := utils.EncodeZipToBase64(zipContent, targetFile.FileName)
				if err != nil {
					errMsg := "ZIP包base64编码失败: " + err.Error()
					logrus.WithError(err).WithField("fileName", targetFile.FileName).Error(errMsg)
					sendProgress(map[string]interface{}{
						"message": errMsg,
						"type":    "error",
					})
					return
				}

				// 存储base64内容供后续FC部署使用
				deploymentZipFileContent = base64Content
				logrus.WithFields(logrus.Fields{
					"fileName":     targetFile.FileName,
					"originalSize": len(content),
					"zipSize":      len(zipContent),
					"base64Length": len(base64Content),
				}).Info("文件转换并编码完成，等待FC部署")
			} else {
				// prod环境：上传文件到OSS
				logrus.WithField("env", deployParams.Env).Info("使用prod环境OSS部署方式处理文件")

				// 构建OSS路径
				ossPath := fmt.Sprintf("%s/%s",
					serviceConfig.OssBasePath,
					targetFile.FileName)

				// 上传到OSS
				sendProgress(map[string]interface{}{
					"message":  "正在上传JAR文件到OSS: " + ossPath,
					"progress": 50,
					"type":     "info",
				})

				err = ossClient.UploadFileWithACL(ossPath, content, alioss.ACLDefault)
				if err != nil {
					errMsg := "上传JAR文件到OSS失败: " + err.Error()
					logrus.WithError(err).WithField("ossPath", ossPath).Error(errMsg)
					sendProgress(map[string]interface{}{
						"message": errMsg,
						"type":    "error",
					})
					return
				}
			}

		default:
			errMsg := "未知的服务类型: " + serviceConfig.Name
			logrus.WithField("serviceType", serviceConfig.Name).Error(errMsg)
			sendProgress(map[string]interface{}{
				"message": errMsg,
				"type":    "error",
			})
			return
		}

		// 查看是否需要部署到FC
		// 如果没有提供serviceName和functionName，则查找配置中该服务下的所有函数并部署
		if deployParams.ServiceName == "" && deployParams.FunctionName == "" {
			// 查找所有相关函数配置
			var functionsToDeploy []struct {
				BaseName     string
				ServiceName  string
				FunctionName string
				OssPath      string
			}

			// 从配置中获取所有相关函数
			for _, serviceConfig := range globalConfig.LambdaConfig.FunctionConfig {
				if serviceConfig.ServiceType == deployParams.ServiceType {
					for _, funcConfig := range serviceConfig.Functions {
						// 替换模板中的变量
						serviceName := strings.Replace(funcConfig.ServiceNameTemplate, "{env}", deployParams.Env, -1)
						functionName := strings.Replace(funcConfig.FunctionNameTemplate, "{env}", deployParams.Env, -1)
						ossPath := strings.Replace(funcConfig.OssPathTemplate, "{version}", deployParams.Version, -1)

						functionsToDeploy = append(functionsToDeploy, struct {
							BaseName     string
							ServiceName  string
							FunctionName string
							OssPath      string
						}{
							BaseName:     funcConfig.Name,
							ServiceName:  serviceName,
							FunctionName: functionName,
							OssPath:      ossPath,
						})
					}
				}
			}

			if len(functionsToDeploy) == 0 {
				errMsg := "未找到服务类型 " + deployParams.ServiceType + " 的函数配置"
				logrus.WithField("serviceType", deployParams.ServiceType).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			// 根据环境选择FC凭证和endpoint
			var fcAccessKeyID, fcAccessKeySecret, fcEndpoint string
			if isDevEnvironment(deployParams.Env) {
				fcAccessKeyID = os.Getenv("DEV_FC_ACCESS_KEY_ID")
				fcAccessKeySecret = os.Getenv("DEV_FC_ACCESS_KEY_SECRET")
				fcEndpoint = os.Getenv("DEV_FC_ENDPOINT")
				logrus.WithFields(logrus.Fields{
					"env":         deployParams.Env,
					"accessKeyID": fcAccessKeyID[:10] + "...", // 只显示前10位用于调试
					"endpoint":    fcEndpoint,
				}).Info("使用dev环境FC配置")
			} else {
				fcAccessKeyID = os.Getenv("PROD_FC_ACCESS_KEY_ID")
				fcAccessKeySecret = os.Getenv("PROD_FC_ACCESS_KEY_SECRET")
				fcEndpoint = os.Getenv("PROD_FC_ENDPOINT")
				logrus.WithFields(logrus.Fields{
					"env":         deployParams.Env,
					"accessKeyID": fcAccessKeyID[:10] + "...", // 只显示前10位用于调试
					"endpoint":    fcEndpoint,
				}).Info("使用prod环境FC配置")
			}

			// 检查凭证和endpoint是否为空
			if fcAccessKeyID == "" || fcAccessKeySecret == "" || fcEndpoint == "" {
				errMsg := fmt.Sprintf("FC配置为空: accessKeyID=%s, accessKeySecret=%s, endpoint=%s",
					fcAccessKeyID,
					func() string {
						if fcAccessKeySecret == "" {
							return "empty"
						} else {
							return "not_empty"
						}
					}(),
					fcEndpoint)
				logrus.WithField("env", deployParams.Env).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			logrus.WithFields(logrus.Fields{
				"endpoint": fcEndpoint,
				"env":      deployParams.Env,
			}).Info("正在创建FC客户端")

			// 创建FC客户端
			fcClient, err := fc.NewClient(
				fcEndpoint,
				fcAccessKeyID,
				fcAccessKeySecret,
			)

			if err != nil {
				errMsg := "创建FC客户端失败: " + err.Error()
				logrus.WithFields(logrus.Fields{
					"error":    err.Error(),
					"endpoint": globalConfig.LambdaConfig.FCConfig.Endpoint,
					"env":      deployParams.Env,
				}).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			// 依次部署所有函数
			totalFunctions := len(functionsToDeploy)
			successCount := 0
			failCount := 0
			isCancelled := false
			successList := make([]string, 0)
			failedList := make([]string, 0)
			failedReasons := make(map[string]string)

			msg := fmt.Sprintf("开始批量更新 %d 个函数...", totalFunctions)
			logrus.Info(msg)
			sendProgress(map[string]interface{}{
				"message":  msg,
				"progress": 60,
				"type":     "info",
			})

			for i, function := range functionsToDeploy {
				// 检查上下文是否已取消
				select {
				case <-ctx.Done():
					logrus.WithField("function", function.FunctionName).Info("部署已取消")
					isCancelled = true
					sendProgress(map[string]interface{}{
						"message":  "部署已取消",
						"progress": 60, // 使用当前阶段的基础进度
						"type":     "warning",
					})
				default:
					// 继续执行
				}

				// 如果已取消，跳出循环
				if isCancelled {
					break
				}

				// 更新进度
				currentProgress := 60 + int(float64(i)/float64(totalFunctions)*40)
				msg := fmt.Sprintf("正在更新函数 %d/%d: %s", i+1, totalFunctions, function.FunctionName)
				logrus.WithFields(logrus.Fields{
					"current":  i + 1,
					"total":    totalFunctions,
					"function": function.FunctionName,
					"progress": currentProgress,
				}).Info(msg)

				sendProgress(map[string]interface{}{
					"message":  msg,
					"progress": currentProgress,
					"type":     "info",
				})

				// 根据环境选择部署方式
				var deployInput *fc.DeployInput
				if isDevEnvironment(deployParams.Env) {
					// dev环境使用zipFile方式
					deployInput = &fc.DeployInput{
						ServiceName:  function.ServiceName,
						FunctionName: function.FunctionName,
						ZipFile:      deploymentZipFileContent,
						Env:          deployParams.Env,
					}
					logrus.WithFields(logrus.Fields{
						"serviceName":    function.ServiceName,
						"functionName":   function.FunctionName,
						"deploymentMode": "zipFile",
					}).Info("使用zipFile方式部署函数")
				} else {
					// prod环境使用OSS方式
					deployInput = &fc.DeployInput{
						ServiceName:   function.ServiceName,
						FunctionName:  function.FunctionName,
						OSSBucketName: globalConfig.LambdaConfig.OssConfig.BucketName,
						OSSObjectName: function.OssPath,
						Env:           deployParams.Env,
					}
					logrus.WithFields(logrus.Fields{
						"serviceName":    function.ServiceName,
						"functionName":   function.FunctionName,
						"deploymentMode": "OSS",
						"ossBucketName":  globalConfig.LambdaConfig.OssConfig.BucketName,
						"ossObjectName":  function.OssPath,
					}).Info("使用OSS方式部署函数")
				}

				// 部署函数
				err = fcClient.DeployFunction(ctx, deployInput)

				if err != nil {
					// 检查错误是否是因为取消导致的
					if err == context.Canceled {
						logrus.WithField("function", function.FunctionName).Info("部署已取消")
						isCancelled = true
						sendProgress(map[string]interface{}{
							"message":  "部署已取消",
							"progress": currentProgress,
							"type":     "warning",
						})
						break
					}

					errMsg := fmt.Sprintf("更新函数 %s 失败: %s", function.FunctionName, err.Error())
					logrus.WithError(err).WithField("function", function.FunctionName).Error(errMsg)
					sendProgress(map[string]interface{}{
						"message": errMsg,
						"type":    "error",
					})
					failCount++
					failedList = append(failedList, function.FunctionName)
					failedReasons[function.FunctionName] = err.Error()
				} else {
					successMsg := fmt.Sprintf("函数 %s 更新成功", function.FunctionName)
					logrus.WithField("function", function.FunctionName).Info(successMsg)
					sendProgress(map[string]interface{}{
						"message": successMsg,
						"type":    "success",
					})
					successCount++
					successList = append(successList, function.FunctionName)
				}
			}

			// 所有函数部署完成或已取消
			if isCancelled {
				cancelMsg := fmt.Sprintf("部署已取消: 已成功部署 %d 个函数, 失败 %d 个, 剩余函数未部署", successCount, failCount)
				logrus.WithFields(logrus.Fields{
					"success":   successCount,
					"failed":    failCount,
					"cancelled": true,
					"total":     totalFunctions,
				}).Info(cancelMsg)

				sendProgress(map[string]interface{}{
					"message":  cancelMsg,
					"progress": 100,
					"type":     "warning",
				})

				// 发送取消部署的飞书通知
				if globalConfig.FeishuConfig.Enabled && userInfo != nil {
					logrus.Debug("准备发送取消部署飞书通知")
					summary := map[string]interface{}{
						"total":         totalFunctions,
						"success":       successCount,
						"failed":        failCount,
						"successList":   successList,
						"failedList":    failedList,
						"failedReasons": failedReasons,
						"cancelled":     true,
					}
					if err := feishuService.NotifyLambdaDeploy(userInfo, deployParams.ServiceType, deployParams.Version, deployParams.Env, summary); err != nil {
						logrus.WithError(err).Warn("发送取消部署通知失败")
					} else {
						logrus.Debug("取消部署飞书通知发送成功")
					}
				}
			} else {
				// 正常完成的情况
				summaryMsg := fmt.Sprintf("批量更新函数完成: 成功 %d 个, 失败 %d 个", successCount, failCount)
				logrus.WithFields(logrus.Fields{
					"success": successCount,
					"failed":  failCount,
					"total":   totalFunctions,
				}).Info(summaryMsg)

				sendProgress(map[string]interface{}{
					"message":  summaryMsg,
					"progress": 100,
					"type":     "success",
				})

				// 发送飞书通知
				if globalConfig.FeishuConfig.Enabled && userInfo != nil {
					logrus.Debug("准备发送批量部署飞书通知")
					summary := map[string]interface{}{
						"total":         totalFunctions,
						"success":       successCount,
						"failed":        failCount,
						"successList":   successList,
						"failedList":    failedList,
						"failedReasons": failedReasons,
					}
					if err := feishuService.NotifyLambdaDeploy(userInfo, deployParams.ServiceType, deployParams.Version, deployParams.Env, summary); err != nil {
						logrus.WithError(err).Warn("发送批量部署通知失败")
					} else {
						logrus.Debug("批量部署飞书通知发送成功")
					}
				}
			}
			return
		} else if deployParams.FunctionName != "" && deployParams.ServiceName != "" {
			// 解析函数基础名称 - 移除环境前缀
			functionBaseName := deployParams.FunctionName
			if strings.HasPrefix(functionBaseName, deployParams.Env+"-") {
				functionBaseName = strings.TrimPrefix(functionBaseName, deployParams.Env+"-")
			}

			ossPath := deployParams.OssObjectKey
			if ossPath == "" {
				// 从配置获取部署信息
				deployInfo, err := fc.GetFCDeploymentInfo(
					deployParams.ServiceType,
					functionBaseName,
					deployParams.Env,
					deployParams.Version,
					globalConfig.LambdaConfig.FunctionConfig,
				)

				if err != nil {
					errMsg := "获取函数部署配置失败: " + err.Error()
					logrus.WithError(err).WithFields(logrus.Fields{
						"serviceType": deployParams.ServiceType,
						"function":    functionBaseName,
						"env":         deployParams.Env,
					}).Error(errMsg)
					sendProgress(map[string]interface{}{
						"message": errMsg,
						"type":    "error",
					})
					return
				}

				// 使用从配置获取的值
				deployParams.ServiceName = deployInfo.ServiceName
				deployParams.FunctionName = deployInfo.FunctionName
				deployParams.OssObjectKey = deployInfo.OSSObjectPath
			}

			// 创建FC客户端并部署函数
			deployMsg := fmt.Sprintf("正在部署函数到服务[%s]函数[%s]...", deployParams.ServiceName, deployParams.FunctionName)
			logrus.WithFields(logrus.Fields{
				"serviceName":  deployParams.ServiceName,
				"functionName": deployParams.FunctionName,
				"ossPath":      deployParams.OssObjectKey,
			}).Info(deployMsg)

			sendProgress(map[string]interface{}{
				"message":  deployMsg,
				"progress": 70,
				"type":     "info",
			})

			// 检查上下文是否已取消
			select {
			case <-ctx.Done():
				logrus.WithFields(logrus.Fields{
					"serviceName":  deployParams.ServiceName,
					"functionName": deployParams.FunctionName,
				}).Info("部署已取消")

				sendProgress(map[string]interface{}{
					"message":  "部署已取消",
					"progress": 70,
					"type":     "warning",
				})
				return
			default:
				// 继续执行
			}

			// 根据环境选择FC凭证和endpoint
			var fcAccessKeyID, fcAccessKeySecret, fcEndpoint string
			if isDevEnvironment(deployParams.Env) {
				fcAccessKeyID = os.Getenv("DEV_FC_ACCESS_KEY_ID")
				fcAccessKeySecret = os.Getenv("DEV_FC_ACCESS_KEY_SECRET")
				fcEndpoint = os.Getenv("DEV_FC_ENDPOINT")
				logrus.WithFields(logrus.Fields{
					"env":         deployParams.Env,
					"accessKeyID": fcAccessKeyID[:10] + "...", // 只显示前10位用于调试
					"endpoint":    fcEndpoint,
				}).Info("使用dev环境FC配置")
			} else {
				fcAccessKeyID = os.Getenv("PROD_FC_ACCESS_KEY_ID")
				fcAccessKeySecret = os.Getenv("PROD_FC_ACCESS_KEY_SECRET")
				fcEndpoint = os.Getenv("PROD_FC_ENDPOINT")
				logrus.WithFields(logrus.Fields{
					"env":         deployParams.Env,
					"accessKeyID": fcAccessKeyID[:10] + "...", // 只显示前10位用于调试
					"endpoint":    fcEndpoint,
				}).Info("使用prod环境FC配置")
			}

			// 检查凭证和endpoint是否为空
			if fcAccessKeyID == "" || fcAccessKeySecret == "" || fcEndpoint == "" {
				errMsg := fmt.Sprintf("FC配置为空: accessKeyID=%s, accessKeySecret=%s, endpoint=%s",
					fcAccessKeyID,
					func() string {
						if fcAccessKeySecret == "" {
							return "empty"
						} else {
							return "not_empty"
						}
					}(),
					fcEndpoint)
				logrus.WithField("env", deployParams.Env).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			logrus.WithFields(logrus.Fields{
				"endpoint": fcEndpoint,
				"env":      deployParams.Env,
			}).Info("正在创建FC客户端")

			fcClient, err := fc.NewClient(
				fcEndpoint,
				fcAccessKeyID,
				fcAccessKeySecret,
			)

			if err != nil {
				errMsg := "创建FC客户端失败: " + err.Error()
				logrus.WithFields(logrus.Fields{
					"error":    err.Error(),
					"endpoint": globalConfig.LambdaConfig.FCConfig.Endpoint,
					"env":      deployParams.Env,
				}).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			// 根据环境选择部署方式
			var deployInput *fc.DeployInput
			if isDevEnvironment(deployParams.Env) {
				// dev环境使用zipFile方式
				deployInput = &fc.DeployInput{
					ServiceName:  deployParams.ServiceName,
					FunctionName: deployParams.FunctionName,
					ZipFile:      deploymentZipFileContent,
					Env:          deployParams.Env,
				}
				logrus.WithFields(logrus.Fields{
					"serviceName":    deployParams.ServiceName,
					"functionName":   deployParams.FunctionName,
					"deploymentMode": "zipFile",
				}).Info("使用zipFile方式部署函数")
			} else {
				// prod环境使用OSS方式
				deployInput = &fc.DeployInput{
					ServiceName:   deployParams.ServiceName,
					FunctionName:  deployParams.FunctionName,
					OSSBucketName: globalConfig.LambdaConfig.OssConfig.BucketName,
					OSSObjectName: deployParams.OssObjectKey,
					Env:           deployParams.Env,
				}
				logrus.WithFields(logrus.Fields{
					"serviceName":    deployParams.ServiceName,
					"functionName":   deployParams.FunctionName,
					"deploymentMode": "OSS",
					"ossBucketName":  globalConfig.LambdaConfig.OssConfig.BucketName,
					"ossObjectName":  deployParams.OssObjectKey,
				}).Info("使用OSS方式部署函数")
			}

			// 部署函数
			err = fcClient.DeployFunction(ctx, deployInput)

			if err != nil {
				// 检查错误是否是因为取消导致的
				if err == context.Canceled {
					logrus.WithFields(logrus.Fields{
						"serviceName":  deployParams.ServiceName,
						"functionName": deployParams.FunctionName,
					}).Info("部署已取消")

					sendProgress(map[string]interface{}{
						"message":  "部署已取消",
						"progress": 70,
						"type":     "warning",
					})
					return
				}

				errMsg := "更新函数失败: " + err.Error()
				logrus.WithError(err).WithFields(logrus.Fields{
					"serviceName":  deployParams.ServiceName,
					"functionName": deployParams.FunctionName,
				}).Error(errMsg)
				sendProgress(map[string]interface{}{
					"message": errMsg,
					"type":    "error",
				})
				return
			}

			// 部署成功
			successMsg := fmt.Sprintf("函数[%s]更新成功", deployParams.FunctionName)
			logrus.WithFields(logrus.Fields{
				"serviceName":  deployParams.ServiceName,
				"functionName": deployParams.FunctionName,
			}).Info(successMsg)

			sendProgress(map[string]interface{}{
				"message":  successMsg,
				"progress": 100,
				"type":     "success",
			})
		} else {
			// 如果不需要部署到FC，直接返回上传成功
			successMsg := "文件上传成功"
			logrus.Info(successMsg)
			sendProgress(map[string]interface{}{
				"message":  successMsg,
				"progress": 100,
				"type":     "success",
			})
		}
	}()

	// 发送进度更新到客户端
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Transfer-Encoding", "chunked")

	encoder := json.NewEncoder(w)
	for update := range progressChan {
		select {
		case <-ctx.Done():
			// 客户端已断开连接
			logrus.Warn("客户端已断开连接，停止发送进度更新")
			return
		default:
			// 发送更新
			if err := encoder.Encode(update); err != nil {
				logrus.Errorf("发送进度更新失败: %v", err)
				return
			}
			w.(http.Flusher).Flush()
		}
	}

	logrus.WithFields(logrus.Fields{
		"serviceName":  deployParams.ServiceName,
		"functionName": deployParams.FunctionName,
		"ossPath":      deployParams.OssObjectKey,
	}).Info("部署到函数计算成功")

	// 如果启用了飞书通知，且有用户信息，发送部署通知
	if globalConfig.FeishuConfig.Enabled && userInfo != nil {
		logrus.Debug("准备发送飞书通知")
		sendProgressUpdate("正在发送飞书通知...", 90, "info")
		// 单个函数部署的摘要信息
		summary := map[string]interface{}{
			"total":         1,
			"success":       1,
			"failed":        0,
			"successList":   []string{deployParams.FunctionName},
			"failedList":    []string{},
			"failedReasons": map[string]string{},
		}
		if err := feishuService.NotifyLambdaDeploy(userInfo, deployParams.ServiceType, deployParams.Version, deployParams.Env, summary); err != nil {
			logrus.WithError(err).Warn("发送部署通知失败")
			sendProgressUpdate("发送飞书通知失败: "+err.Error(), 95, "warning")
		} else {
			logrus.Debug("飞书通知发送成功")
			sendProgressUpdate("飞书通知发送成功", 95, "info")
		}
	}

	// 发送部署完成通知
	logrus.WithFields(logrus.Fields{
		"serviceType": deployParams.ServiceType,
		"version":     deployParams.Version,
		"env":         deployParams.Env,
	}).Info("Lambda部署完成")
	sendProgressUpdate("部署完成！", 100, "success")
}

// handleAuth 处理认证相关的HTTP请求
func handleAuth(w http.ResponseWriter, r *http.Request) {
	// 检查是否启用了认证
	if !globalConfig.AuthConfig.Enabled {
		http.Error(w, "认证功能未启用", http.StatusServiceUnavailable)
		return
	}

	// 获取授权信息
	authURL := keycloakService.GetAuthURL("state123")

	// 返回认证URL
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"authUrl": authURL,
	})
}

// handleToken 处理令牌交换请求
func handleToken(w http.ResponseWriter, r *http.Request) {
	// 检查是否启用了认证
	if !globalConfig.AuthConfig.Enabled {
		http.Error(w, "认证功能未启用", http.StatusServiceUnavailable)
		return
	}

	// 只接受POST请求
	if r.Method != "POST" {
		http.Error(w, "只支持POST方法", http.StatusMethodNotAllowed)
		return
	}

	// 解析请求体
	var req TokenRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		logrus.WithError(err).Error("解析令牌请求失败")
		http.Error(w, "无法解析请求: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 验证请求参数
	if req.Code == "" {
		logrus.Error("请求中缺少授权码")
		http.Error(w, "请求参数不完整: 缺少授权码", http.StatusBadRequest)
		return
	}

	// 记录请求信息 (不记录完整code，避免泄露敏感信息)
	requestLogger := logrus.WithFields(logrus.Fields{
		"redirect_uri": req.RedirectURI,
		"code_length":  len(req.Code),
		"remote_addr":  r.RemoteAddr,
		"user_agent":   r.UserAgent(),
	})

	// 检查是否重复处理相同的授权码，并获取缓存结果
	isRepeatedCode, cachedEntry := tokenCache.checkAndGetCachedResult(req.Code)
	if isRepeatedCode && cachedEntry != nil {
		requestLogger.WithFields(logrus.Fields{
			"cached_status": cachedEntry.statusCode,
			"has_token":     cachedEntry.tokenResp != nil,
			"has_user":      cachedEntry.userInfo != nil,
		}).Info("返回缓存的认证结果")

		// 如果有缓存的认证结果，直接返回
		if cachedEntry.statusCode == http.StatusOK && cachedEntry.resultCache != nil {
			// 设置响应头
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("Cache-Control", "no-store")
			w.Header().Set("Pragma", "no-cache")
			w.Header().Set("X-Auth-Cached", "true") // 指示这是缓存结果

			// 返回缓存的结果
			w.Write(cachedEntry.resultCache)
			return
		} else if cachedEntry.statusCode != http.StatusOK {
			// 如果之前的请求失败，返回相同的错误
			w.Header().Set("Content-Type", "application/json")
			w.Header().Set("X-Auth-Cached", "true")
			w.WriteHeader(cachedEntry.statusCode)
			json.NewEncoder(w).Encode(map[string]string{
				"error": cachedEntry.errorMsg,
			})
			return
		}
	}

	requestLogger.Info("处理令牌交换请求")

	// 使用授权码交换令牌
	tokenResp, err := keycloakService.ExchangeCodeForToken(req.Code)
	if err != nil {
		// 记录详细错误信息
		requestLogger.WithField("error", err.Error()).Error("交换令牌失败")

		// 构建错误信息
		var statusCode int
		var errorMsg string

		// 根据错误类型返回适当的HTTP状态码和错误信息
		if strings.Contains(err.Error(), "获取令牌失败: 400") {
			// 400错误通常表示授权码无效或已过期
			statusCode = http.StatusBadRequest
			errorMsg = "授权码无效或已过期: " + err.Error()
		} else if strings.Contains(err.Error(), "发送请求失败") {
			// 网络连接错误
			statusCode = http.StatusServiceUnavailable
			errorMsg = "连接认证服务器失败: " + err.Error()
		} else {
			// 其他错误
			statusCode = http.StatusInternalServerError
			errorMsg = "获取令牌失败: " + err.Error()
		}

		// 缓存错误结果
		tokenCache.recordResult(req.Code, statusCode, errorMsg, nil, nil, nil)

		// 返回错误
		http.Error(w, errorMsg, statusCode)
		return
	}

	// 获取用户信息
	userInfo, err := keycloakService.GetUserInfo(tokenResp.AccessToken)
	if err != nil {
		requestLogger.WithError(err).Error("获取用户信息失败")

		// 缓存错误结果
		errorMsg := "获取用户信息失败: " + err.Error()
		tokenCache.recordResult(req.Code, http.StatusInternalServerError, errorMsg, tokenResp, nil, nil)

		http.Error(w, errorMsg, http.StatusInternalServerError)
		return
	}

	// 记录成功的认证
	requestLogger.WithFields(logrus.Fields{
		"username":   userInfo.PreferredUsername,
		"email":      userInfo.Email,
		"expires_in": tokenResp.ExpiresIn,
	}).Info("令牌交换成功")

	// 发送登录通知
	if globalConfig.FeishuConfig.Enabled {
		go func() {
			if err := feishuService.NotifyLogin(userInfo); err != nil {
				logrus.WithError(err).Warn("发送登录通知失败")
			}
		}()
	}

	// 准备返回的数据
	responseData := map[string]interface{}{
		"token": tokenResp,
		"user":  userInfo,
	}

	// 序列化响应数据
	resultJSON, err := json.Marshal(responseData)
	if err != nil {
		requestLogger.WithError(err).Error("序列化响应失败")
		http.Error(w, "序列化响应失败", http.StatusInternalServerError)
		return
	}

	// 缓存认证结果
	tokenCache.recordResult(req.Code, http.StatusOK, "", tokenResp, userInfo, resultJSON)

	// 返回令牌和用户信息
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Cache-Control", "no-store")
	w.Header().Set("Pragma", "no-cache")
	w.Write(resultJSON)
}

// handleLogout 处理登出请求
func handleLogout(w http.ResponseWriter, r *http.Request) {
	// 检查是否启用了认证
	if !globalConfig.AuthConfig.Enabled {
		http.Error(w, "认证功能未启用", http.StatusServiceUnavailable)
		return
	}

	// 从请求中获取ID令牌
	var req struct {
		IDToken string `json:"id_token"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "无法解析请求: "+err.Error(), http.StatusBadRequest)
		return
	}

	// 生成登出URL
	logoutURL := keycloakService.GetLogoutURL(req.IDToken)

	// 返回登出URL
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"logoutUrl": logoutURL,
	})
}

// handleUserInfo 获取用户信息
func handleUserInfo(w http.ResponseWriter, r *http.Request) {
	// 检查是否启用了认证
	if !globalConfig.AuthConfig.Enabled {
		http.Error(w, "认证功能未启用", http.StatusServiceUnavailable)
		return
	}

	// 从Authorization头获取令牌
	authHeader := r.Header.Get("Authorization")
	if authHeader == "" {
		http.Error(w, "未授权：缺少Authorization头", http.StatusUnauthorized)
		return
	}

	// 提取令牌
	tokenParts := strings.Split(authHeader, " ")
	if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
		http.Error(w, "未授权：无效的Authorization头格式", http.StatusUnauthorized)
		return
	}

	// 获取用户信息
	userInfo, err := keycloakService.GetUserInfo(tokenParts[1])
	if err != nil {
		http.Error(w, "获取用户信息失败: "+err.Error(), http.StatusInternalServerError)
		return
	}

	// 返回用户信息
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(userInfo)
}

// handleDiagnostics 获取系统诊断信息
func handleDiagnostics(w http.ResponseWriter, r *http.Request) {
	// 准备诊断信息
	// 确定版本号
	displayedVersion := buildTimestamp
	if displayedVersion == "" {
		displayedVersion = "v5.0.0" // 如果没有通过ldflags注入，则显示v5.0.0
	}
	diag := DiagnosticsResponse{
		Timestamp:     time.Now().Format(time.RFC3339),           // 1. 获取当前时间戳
		GitLabToken:   os.Getenv("GITLAB_TOKEN") != "",           // 2. 检查 GITLAB_TOKEN 环境变量是否设置
		OssAccessKey:  os.Getenv("PROD_OSS_ACCESS_KEY_ID") != "", // 3. 检查 PROD_OSS_ACCESS_KEY_ID 环境变量是否设置
		FcAccessKey:   os.Getenv("PROD_FC_ACCESS_KEY_ID") != "",  // 4. 检查 PROD_FC_ACCESS_KEY_ID 环境变量是否设置
		ConfigLoaded:  true,                                      // 5. 默认为 true，表示配置文件在启动时已加载（若失败服务会无法启动）
		Version:       displayedVersion,                          // 6. 使用构建时间戳或默认值
		AuthEnabled:   globalConfig.AuthConfig.Enabled,           // 7. 读取配置中认证是否启用
		FeishuEnabled: globalConfig.FeishuConfig.Enabled,         // 8. 读取配置中飞书通知是否启用
		GitLabConnection: ConnectionStatus{ // 9. GitLab 连接状态
			Status:  "ok",
			Message: "连接正常",
		},
	}

	// 尝试测试GitLab连接
	gitlabClient, err := gitlab.NewClient(
		globalConfig.GitLabConfig.BaseUrl,
		os.Getenv("GITLAB_TOKEN"),
		globalConfig.DesktopConfig.GitLabConfig.ProjectId,
		globalConfig.DesktopConfig.GitLabConfig.ProjectName,
		globalConfig.DesktopConfig.GitLabConfig.PackageName,
	)
	if err != nil {
		diag.GitLabConnection.Status = "error"
		diag.GitLabConnection.Message = "创建GitLab客户端失败: " + err.Error()
	} else {
		_, err := gitlabClient.GetVersions(1)
		if err != nil {
			diag.GitLabConnection.Status = "error"
			diag.GitLabConnection.Message = "获取版本列表失败: " + err.Error()
		}
	}

	// 返回诊断信息
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(diag)
}

// handleDesktopDiagnostics 获取桌面客户端相关的系统诊断信息
func handleDesktopDiagnostics(w http.ResponseWriter, r *http.Request) {
	// 确定版本号
	displayedVersion := buildTimestamp
	if displayedVersion == "" {
		displayedVersion = "v5.0.0" // 如果没有通过ldflags注入，则显示v5.0.0
	}

	// 准备诊断信息
	diag := DesktopDiagnosticsResponse{
		Timestamp:     time.Now().Format(time.RFC3339),
		GitLabToken:   os.Getenv("GITLAB_TOKEN") != "",
		OssAccessKey:  os.Getenv("DEV_OSS_ACCESS_KEY_ID") != "", // 检查 DEV 密钥
		ConfigLoaded:  true,
		Version:       displayedVersion,
		AuthEnabled:   globalConfig.AuthConfig.Enabled,
		FeishuEnabled: globalConfig.FeishuConfig.Enabled,
		GitLabConnection: ConnectionStatus{
			Status:  "ok",
			Message: "连接正常",
		},
	}

	// 尝试测试GitLab连接 (使用 Desktop 配置中的 GitLab 信息)
	gitlabClient, err := gitlab.NewClient(
		globalConfig.GitLabConfig.BaseUrl,
		os.Getenv("GITLAB_TOKEN"),
		globalConfig.DesktopConfig.GitLabConfig.ProjectId,
		globalConfig.DesktopConfig.GitLabConfig.ProjectName,
		globalConfig.DesktopConfig.GitLabConfig.PackageName,
	)
	if err != nil {
		diag.GitLabConnection.Status = "error"
		diag.GitLabConnection.Message = "创建GitLab客户端失败: " + err.Error()
	} else {
		_, err := gitlabClient.GetVersions(1)
		if err != nil {
			diag.GitLabConnection.Status = "error"
			diag.GitLabConnection.Message = "获取版本列表失败: " + err.Error()
		}
	}

	// 返回诊断信息
	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(diag)
}

func main() {
	// 创建HTTP处理器
	mux := http.NewServeMux()

	// 添加API路由
	mux.HandleFunc("/api/versions", handleVersions)
	mux.HandleFunc("/api/lambda/services", handleLambdaServices)
	mux.HandleFunc("/api/lambda/versions", handleLambdaVersions)
	mux.HandleFunc("/api/upload", handleUpload)
	mux.HandleFunc("/api/lambda/deploy", handleLambdaDeploy)
	mux.HandleFunc("/api/auth", handleAuth)
	mux.HandleFunc("/api/token", handleToken)
	mux.HandleFunc("/api/logout", handleLogout)
	mux.HandleFunc("/api/userinfo", handleUserInfo)
	mux.HandleFunc("/api/diagnostics", handleDiagnostics)
	mux.HandleFunc("/api/diagnostics/desktop", handleDesktopDiagnostics)

	// 创建带CORS支持的处理器
	corsHandler := func(h http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// 获取请求的Origin
			origin := r.Header.Get("Origin")
			if origin == "" {
				// 如果没有Origin头，使用Host作为后备
				origin = "https://" + r.Host
			}

			// 设置CORS头部
			w.Header().Set("Access-Control-Allow-Origin", origin)
			w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
			w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With")
			w.Header().Set("Access-Control-Allow-Credentials", "true") // 允许携带认证信息
			w.Header().Set("Access-Control-Max-Age", "3600")

			// 处理OPTIONS请求
			if r.Method == "OPTIONS" {
				w.WriteHeader(http.StatusOK)
				return
			}

			// 对于认证相关接口，增加安全性头部
			if strings.HasPrefix(r.URL.Path, "/api/auth") ||
				strings.HasPrefix(r.URL.Path, "/api/token") ||
				strings.HasPrefix(r.URL.Path, "/api/logout") ||
				strings.HasPrefix(r.URL.Path, "/api/userinfo") {
				w.Header().Set("X-Content-Type-Options", "nosniff")
				w.Header().Set("X-Frame-Options", "DENY")
				w.Header().Set("X-XSS-Protection", "1; mode=block")
			}

			// 调用原始处理器
			h.ServeHTTP(w, r)
		})
	}

	// 启动HTTP服务
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080" // 默认端口
	}

	logrus.Infof("服务启动在 :%s", port)
	if err := http.ListenAndServe(":"+port, corsHandler(mux)); err != nil {
		logrus.Fatalf("启动服务失败: %v", err)
	}
}
