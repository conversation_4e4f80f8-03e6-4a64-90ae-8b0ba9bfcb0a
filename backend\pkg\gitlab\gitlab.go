package gitlab

import (
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	gitlab "gitlab.com/gitlab-org/api/client-go"
)

// Client 是对GitLab操作的封装
type Client struct {
	client       *gitlab.Client
	baseURL      string
	projectID    int
	projectName  string
	packageName  string
	privateToken string
}

// VersionInfo 表示软件包版本信息
type VersionInfo struct {
	Version     string
	CreatedAt   string
	PackageName string
}

// PackageFile 表示软件包中的文件
type PackageFile struct {
	ID       int
	FileName string
	Size     int64
	URL      string
}

// NewClient 创建一个新的GitLab客户端
func NewClient(baseURL, privateToken string, projectID int, projectName string, packageName string) (*Client, error) {
	// 创建GitLab客户端
	client, err := gitlab.NewClient(privateToken, gitlab.WithBaseURL(baseURL))
	if err != nil {
		return nil, fmt.Errorf("创建GitLab客户端失败: %w", err)
	}

	// 如果未提供包名，默认使用项目名
	if packageName == "" {
		packageName = projectName
	}

	return &Client{
		client:       client,
		baseURL:      baseURL,
		projectID:    projectID,
		projectName:  projectName,
		packageName:  packageName,
		privateToken: privateToken,
	}, nil
}

// GetVersions 获取版本列表
func (c *Client) GetVersions(limit int) ([]VersionInfo, error) {
	// 获取最近的包，按创建时间倒序排序
	packages, _, err := c.client.Packages.ListProjectPackages(c.projectID,
		&gitlab.ListProjectPackagesOptions{
			ListOptions: gitlab.ListOptions{
				PerPage: limit,        // 设置每页显示的包数量
				OrderBy: "created_at", // 按创建时间排序
				Sort:    "desc",       // 按创建时间倒序排序
			},
		})
	if err != nil {
		return nil, fmt.Errorf("获取版本列表失败: %w", err)
	}

	// 提取版本信息
	versions := make([]VersionInfo, 0, len(packages))
	for _, pkg := range packages {
		if pkg.Version != "" {
			versions = append(versions, VersionInfo{
				Version:     pkg.Version,
				CreatedAt:   pkg.CreatedAt.String(),
				PackageName: pkg.Name,
			})
		}
	}

	return versions, nil
}

// GetPackageFiles 获取指定版本的软件包文件列表
func (c *Client) GetPackageFiles(version string) ([]PackageFile, error) {
	// 获取指定版本的软件包
	packages, _, err := c.client.Packages.ListProjectPackages(c.projectID, &gitlab.ListProjectPackagesOptions{
		PackageVersion: &version,
		PackageName:    &c.packageName,
	})
	if err != nil {
		return nil, fmt.Errorf("获取软件包失败: %w", err)
	}

	if len(packages) == 0 {
		return nil, fmt.Errorf("未找到版本为 %s 的软件包", version)
	}

	// 获取软件包内的文件
	pkg := packages[0]
	files, _, err := c.client.Packages.ListPackageFiles(c.projectID, pkg.ID, &gitlab.ListPackageFilesOptions{
		PerPage: 100,
	})
	if err != nil {
		return nil, fmt.Errorf("获取软件包文件失败: %w", err)
	}

	// 转换文件信息
	result := make([]PackageFile, 0, len(files))
	for _, file := range files {
		var downloadURL string

		// 根据包类型和文件名判断使用哪种URL格式
		if (strings.HasSuffix(file.FileName, ".jar") || strings.HasSuffix(file.FileName, ".zip")) &&
			(strings.Contains(c.packageName, "target-report-word-export") ||
				strings.Contains(c.packageName, "plan-report-pdf-export")) {
			// Maven包格式（支持JAR和ZIP文件）
			downloadURL = fmt.Sprintf("/projects/%d/packages/maven/%s/%s/%s",
				c.projectID,
				c.packageName, // artifactId
				version,
				file.FileName)
		} else {
			// 通用包格式
			downloadURL, _ = c.client.GenericPackages.FormatPackageURL(
				c.projectID,
				c.packageName,
				version,
				file.FileName,
			)
		}

		// 确保URL正确拼接，避免重复路径
		fullURL := c.normalizeURL(downloadURL)

		result = append(result, PackageFile{
			ID:       file.ID,
			FileName: file.FileName,
			Size:     int64(file.Size),
			URL:      fullURL,
		})
	}

	return result, nil
}

// normalizeURL 规范化URL，防止API路径重复
func (c *Client) normalizeURL(path string) string {
	// 如果path是完整URL（包含http或https），则直接返回
	if strings.HasPrefix(path, "http://") || strings.HasPrefix(path, "https://") {
		return path
	}

	baseURL := c.baseURL

	// 确保path以/开头
	if !strings.HasPrefix(path, "/") {
		path = "/" + path
	}

	// 检查baseURL和path是否存在重复的api/v4部分
	if strings.Contains(baseURL, "/api/v4") && strings.HasPrefix(path, "/api/v4") {
		path = strings.Replace(path, "/api/v4", "", 1)
	}

	// 避免双斜杠
	if strings.HasSuffix(baseURL, "/") && strings.HasPrefix(path, "/") {
		path = path[1:]
	}

	return baseURL + path
}

// DownloadFile 下载文件
func (c *Client) DownloadFile(fileURL, fileName string) ([]byte, error) {
	// 记录开始下载
	logrus.WithFields(logrus.Fields{
		"url":      fileURL,
		"fileName": fileName,
	}).Info("开始下载文件")

	// 创建下载请求
	req, err := http.NewRequest("GET", fileURL, nil)
	if err != nil {
		logrus.WithError(err).Error("创建下载请求失败")
		return nil, fmt.Errorf("创建下载请求失败: %w", err)
	}

	// 添加GitLab私有令牌
	req.Header.Add("PRIVATE-TOKEN", c.privateToken)

	// 记录请求头部（注意不要记录token）
	logrus.WithFields(logrus.Fields{
		"method":  req.Method,
		"url":     req.URL.String(),
		"headers": "已设置PRIVATE-TOKEN",
	}).Debug("发送下载请求")

	// 发送请求
	client := &http.Client{
		Timeout: 300 * time.Second, // 延长超时至5分钟，以处理大文件下载
	}
	resp, err := client.Do(req)
	if err != nil {
		logrus.WithError(err).Error("发送下载请求失败")
		return nil, fmt.Errorf("下载文件失败: %w", err)
	}
	defer resp.Body.Close()

	// 记录响应状态
	logrus.WithFields(logrus.Fields{
		"statusCode": resp.StatusCode,
		"status":     resp.Status,
		"url":        fileURL,
		"fileName":   fileName,
	}).Info("收到下载响应")

	// 检查状态码
	if resp.StatusCode != 200 {
		// 读取错误响应体
		errBody, _ := io.ReadAll(resp.Body)
		errMsg := fmt.Sprintf("下载文件失败，状态码: %d，URL: %s，文件名: %s",
			resp.StatusCode, fileURL, fileName)

		// 如果有响应体，添加到错误信息中
		if len(errBody) > 0 {
			errMsg += fmt.Sprintf("，响应: %s", string(errBody))
		}

		logrus.WithFields(logrus.Fields{
			"statusCode": resp.StatusCode,
			"url":        fileURL,
			"fileName":   fileName,
			"response":   string(errBody),
		}).Error("下载文件失败")

		return nil, fmt.Errorf(errMsg)
	}

	// 读取响应内容
	content, err := io.ReadAll(resp.Body)
	if err != nil {
		logrus.WithError(err).Error("读取响应内容失败")
		return nil, fmt.Errorf("读取响应内容失败: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"size":     len(content),
	}).Info("下载文件成功")

	return content, nil
}

// ValidateConfig 验证GitLab配置是否有效
func (c *Client) ValidateConfig() error {
	// 使用Debug级别记录详细信息，避免与main.go中的Info记录重复
	logrus.WithFields(logrus.Fields{
		"projectID":   c.projectID,
		"projectName": c.projectName,
		"baseURL":     c.baseURL,
	}).Debug("执行GitLab配置验证")

	// 检查Token
	if c.privateToken == "" {
		logrus.WithField("projectID", c.projectID).Error("GitLab私有令牌为空")
		return fmt.Errorf("GitLab私有令牌为空")
	}

	// 尝试获取一下用户信息
	logrus.WithField("baseURL", c.baseURL).Debug("验证GitLab令牌...")
	req, err := http.NewRequest("GET", fmt.Sprintf("%s/user", c.baseURL), nil)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"baseURL": c.baseURL,
			"error":   err.Error(),
		}).Error("创建验证请求失败")
		return fmt.Errorf("创建验证请求失败: %w", err)
	}

	req.Header.Add("PRIVATE-TOKEN", c.privateToken)

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"baseURL": c.baseURL,
			"error":   err.Error(),
		}).Error("发送验证请求失败")
		return fmt.Errorf("发送验证请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		errBody, _ := io.ReadAll(resp.Body)
		logrus.WithFields(logrus.Fields{
			"baseURL":    c.baseURL,
			"statusCode": resp.StatusCode,
			"response":   string(errBody),
		}).Error("GitLab Token验证失败")
		return fmt.Errorf("GitLab Token验证失败，状态码: %d，响应: %s",
			resp.StatusCode, string(errBody))
	}

	// 验证项目是否存在
	logrus.WithFields(logrus.Fields{
		"projectID":   c.projectID,
		"projectName": c.projectName,
	}).Debug("验证GitLab项目...")
	projectURL := fmt.Sprintf("%s/projects/%d", c.baseURL, c.projectID)
	req, _ = http.NewRequest("GET", projectURL, nil)
	req.Header.Add("PRIVATE-TOKEN", c.privateToken)

	resp, err = client.Do(req)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"projectID":   c.projectID,
			"projectName": c.projectName,
			"error":       err.Error(),
		}).Error("验证项目失败")
		return fmt.Errorf("验证项目失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != 200 {
		errBody, _ := io.ReadAll(resp.Body)
		logrus.WithFields(logrus.Fields{
			"projectID":   c.projectID,
			"projectName": c.projectName,
			"statusCode":  resp.StatusCode,
			"response":    string(errBody),
		}).Error("GitLab项目验证失败")
		return fmt.Errorf("GitLab项目验证失败，项目ID: %d，状态码: %d，响应: %s",
			c.projectID, resp.StatusCode, string(errBody))
	}

	// 使用Info级别记录最终成功的结果
	logrus.WithFields(logrus.Fields{
		"projectID":   c.projectID,
		"projectName": c.projectName,
	}).Info("GitLab配置验证成功")
	return nil
}
