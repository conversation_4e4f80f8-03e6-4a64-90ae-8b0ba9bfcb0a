import{r as d,b as $t,R as K,d as br,u as Hr,e as Kr,O as Gr,N as qr,B as Et,f as At,h as ee}from"./vendor-4d8e7009.js";import{I as ar,_ as ze,a as or,L as Ze,T as ae,S as B,E as Me,D as Yr,B as be,b as Jr,c as Xr,M as vr,d as Ot,G as Ue,C as ke,P as Rt,A as It,e as W,f as le,g as Qe,h as M,i as er,j as ne,k as Ke,R as Zr,l as _t,z as Pt}from"./arco-5703e380.js";(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))n(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const i of o.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&n(i)}).observe(document,{childList:!0,subtree:!0});function s(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(a){if(a.ep)return;a.ep=!0;const o=s(a);fetch(a.href,o)}})();var Qr={exports:{}},Ve={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Lt=d,Dt=Symbol.for("react.element"),Mt=Symbol.for("react.fragment"),Ft=Object.prototype.hasOwnProperty,zt=Lt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Ut={key:!0,ref:!0,__self:!0,__source:!0};function et(e,r,s){var n,a={},o=null,i=null;s!==void 0&&(o=""+s),r.key!==void 0&&(o=""+r.key),r.ref!==void 0&&(i=r.ref);for(n in r)Ft.call(r,n)&&!Ut.hasOwnProperty(n)&&(a[n]=r[n]);if(e&&e.defaultProps)for(n in r=e.defaultProps,r)a[n]===void 0&&(a[n]=r[n]);return{$$typeof:Dt,type:e,key:o,ref:i,props:a,_owner:zt.current}}Ve.Fragment=Mt;Ve.jsx=et;Ve.jsxs=et;Qr.exports=Ve;var t=Qr.exports,rr={},jr=$t;rr.createRoot=jr.createRoot,rr.hydrateRoot=jr.hydrateRoot;function Sr(e,r){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),s.push.apply(s,n)}return s}function kr(e){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Sr(Object(s),!0).forEach(function(n){or(e,n,s[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):Sr(Object(s)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(s,n))})}return e}function Vt(e,r){var s=d.useContext(ar),n=s.prefixCls,a=n===void 0?"arco":n,o=e.spin,i=e.className,c=kr(kr({"aria-hidden":!0,focusable:!1,ref:r},e),{},{className:"".concat(i?i+" ":"").concat(a,"-icon ").concat(a,"-icon-poweroff")});return o&&(c.className="".concat(c.className," ").concat(a,"-icon-loading")),delete c.spin,delete c.isIcon,K.createElement("svg",ze({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},c),K.createElement("path",{d:"M15.5 9.274C10.419 12.214 7 17.708 7 24c0 9.389 7.611 17 17 17s17-7.611 17-17c0-6.292-3.419-11.786-8.5-14.726M24 5v22"}))}var ir=K.forwardRef(Vt);ir.defaultProps={isIcon:!0};ir.displayName="IconPoweroff";const Nt=ir;function Cr(e,r){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),s.push.apply(s,n)}return s}function Tr(e){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?Cr(Object(s),!0).forEach(function(n){or(e,n,s[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):Cr(Object(s)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(s,n))})}return e}function Bt(e,r){var s=d.useContext(ar),n=s.prefixCls,a=n===void 0?"arco":n,o=e.spin,i=e.className,c=Tr(Tr({"aria-hidden":!0,focusable:!1,ref:r},e),{},{className:"".concat(i?i+" ":"").concat(a,"-icon ").concat(a,"-icon-refresh")});return o&&(c.className="".concat(c.className," ").concat(a,"-icon-loading")),delete c.spin,delete c.isIcon,K.createElement("svg",ze({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},c),K.createElement("path",{d:"M38.837 18C36.463 12.136 30.715 8 24 8 15.163 8 8 15.163 8 24s7.163 16 16 16c7.455 0 13.72-5.1 15.496-12M40 8v10H30"}))}var cr=K.forwardRef(Bt);cr.defaultProps={isIcon:!0};cr.displayName="IconRefresh";const Wt=cr;function $r(e,r){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);r&&(n=n.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),s.push.apply(s,n)}return s}function Er(e){for(var r=1;r<arguments.length;r++){var s=arguments[r]!=null?arguments[r]:{};r%2?$r(Object(s),!0).forEach(function(n){or(e,n,s[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(s)):$r(Object(s)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(s,n))})}return e}function Ht(e,r){var s=d.useContext(ar),n=s.prefixCls,a=n===void 0?"arco":n,o=e.spin,i=e.className,c=Er(Er({"aria-hidden":!0,focusable:!1,ref:r},e),{},{className:"".concat(i?i+" ":"").concat(a,"-icon ").concat(a,"-icon-notification")});return o&&(c.className="".concat(c.className," ").concat(a,"-icon-loading")),delete c.spin,delete c.isIcon,K.createElement("svg",ze({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},c),K.createElement("path",{d:"M24 9c7.18 0 13 5.82 13 13v13H11V22c0-7.18 5.82-13 13-13Zm0 0V4M6 35h36m-25 7h14"}))}var lr=K.forwardRef(Ht);lr.defaultProps={isIcon:!0};lr.displayName="IconNotification";const Kt=lr;var Gt=!1;function qt(e){if(e.sheet)return e.sheet;for(var r=0;r<document.styleSheets.length;r++)if(document.styleSheets[r].ownerNode===e)return document.styleSheets[r]}function Yt(e){var r=document.createElement("style");return r.setAttribute("data-emotion",e.key),e.nonce!==void 0&&r.setAttribute("nonce",e.nonce),r.appendChild(document.createTextNode("")),r.setAttribute("data-s",""),r}var Jt=function(){function e(s){var n=this;this._insertTag=function(a){var o;n.tags.length===0?n.insertionPoint?o=n.insertionPoint.nextSibling:n.prepend?o=n.container.firstChild:o=n.before:o=n.tags[n.tags.length-1].nextSibling,n.container.insertBefore(a,o),n.tags.push(a)},this.isSpeedy=s.speedy===void 0?!Gt:s.speedy,this.tags=[],this.ctr=0,this.nonce=s.nonce,this.key=s.key,this.container=s.container,this.prepend=s.prepend,this.insertionPoint=s.insertionPoint,this.before=null}var r=e.prototype;return r.hydrate=function(n){n.forEach(this._insertTag)},r.insert=function(n){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(Yt(this));var a=this.tags[this.tags.length-1];if(this.isSpeedy){var o=qt(a);try{o.insertRule(n,o.cssRules.length)}catch{}}else a.appendChild(document.createTextNode(n));this.ctr++},r.flush=function(){this.tags.forEach(function(n){var a;return(a=n.parentNode)==null?void 0:a.removeChild(n)}),this.tags=[],this.ctr=0},e}(),z="-ms-",Fe="-moz-",I="-webkit-",rt="comm",dr="rule",ur="decl",Xt="@import",tt="@keyframes",Zt="@layer",Qt=Math.abs,Ne=String.fromCharCode,es=Object.assign;function rs(e,r){return F(e,0)^45?(((r<<2^F(e,0))<<2^F(e,1))<<2^F(e,2))<<2^F(e,3):0}function st(e){return e.trim()}function ts(e,r){return(e=r.exec(e))?e[0]:e}function _(e,r,s){return e.replace(r,s)}function tr(e,r){return e.indexOf(r)}function F(e,r){return e.charCodeAt(r)|0}function ve(e,r,s){return e.slice(r,s)}function G(e){return e.length}function pr(e){return e.length}function $e(e,r){return r.push(e),e}function ss(e,r){return e.map(r).join("")}var Be=1,ue=1,nt=0,V=0,D=0,pe="";function We(e,r,s,n,a,o,i){return{value:e,root:r,parent:s,type:n,props:a,children:o,line:Be,column:ue,length:i,return:""}}function he(e,r){return es(We("",null,null,"",null,null,0),e,{length:-e.length},r)}function ns(){return D}function as(){return D=V>0?F(pe,--V):0,ue--,D===10&&(ue=1,Be--),D}function N(){return D=V<nt?F(pe,V++):0,ue++,D===10&&(ue=1,Be++),D}function Y(){return F(pe,V)}function Pe(){return V}function Ce(e,r){return ve(pe,e,r)}function je(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function at(e){return Be=ue=1,nt=G(pe=e),V=0,[]}function ot(e){return pe="",e}function Le(e){return st(Ce(V-1,sr(e===91?e+2:e===40?e+1:e)))}function os(e){for(;(D=Y())&&D<33;)N();return je(e)>2||je(D)>3?"":" "}function is(e,r){for(;--r&&N()&&!(D<48||D>102||D>57&&D<65||D>70&&D<97););return Ce(e,Pe()+(r<6&&Y()==32&&N()==32))}function sr(e){for(;N();)switch(D){case e:return V;case 34:case 39:e!==34&&e!==39&&sr(D);break;case 40:e===41&&sr(e);break;case 92:N();break}return V}function cs(e,r){for(;N()&&e+D!==47+10;)if(e+D===42+42&&Y()===47)break;return"/*"+Ce(r,V-1)+"*"+Ne(e===47?e:N())}function ls(e){for(;!je(Y());)N();return Ce(e,V)}function ds(e){return ot(De("",null,null,null,[""],e=at(e),0,[0],e))}function De(e,r,s,n,a,o,i,c,l){for(var u=0,p=0,h=i,j=0,A=0,S=0,f=1,y=1,m=1,T=0,w="",v=a,C=o,$=n,b=w;y;)switch(S=T,T=N()){case 40:if(S!=108&&F(b,h-1)==58){tr(b+=_(Le(T),"&","&\f"),"&\f")!=-1&&(m=-1);break}case 34:case 39:case 91:b+=Le(T);break;case 9:case 10:case 13:case 32:b+=os(S);break;case 92:b+=is(Pe()-1,7);continue;case 47:switch(Y()){case 42:case 47:$e(us(cs(N(),Pe()),r,s),l);break;default:b+="/"}break;case 123*f:c[u++]=G(b)*m;case 125*f:case 59:case 0:switch(T){case 0:case 125:y=0;case 59+p:m==-1&&(b=_(b,/\f/g,"")),A>0&&G(b)-h&&$e(A>32?Or(b+";",n,s,h-1):Or(_(b," ","")+";",n,s,h-2),l);break;case 59:b+=";";default:if($e($=Ar(b,r,s,u,p,a,c,w,v=[],C=[],h),o),T===123)if(p===0)De(b,r,$,$,v,o,h,c,C);else switch(j===99&&F(b,3)===110?100:j){case 100:case 108:case 109:case 115:De(e,$,$,n&&$e(Ar(e,$,$,0,0,a,c,w,a,v=[],h),C),a,C,h,c,n?v:C);break;default:De(b,$,$,$,[""],C,0,c,C)}}u=p=A=0,f=m=1,w=b="",h=i;break;case 58:h=1+G(b),A=S;default:if(f<1){if(T==123)--f;else if(T==125&&f++==0&&as()==125)continue}switch(b+=Ne(T),T*f){case 38:m=p>0?1:(b+="\f",-1);break;case 44:c[u++]=(G(b)-1)*m,m=1;break;case 64:Y()===45&&(b+=Le(N())),j=Y(),p=h=G(w=b+=ls(Pe())),T++;break;case 45:S===45&&G(b)==2&&(f=0)}}return o}function Ar(e,r,s,n,a,o,i,c,l,u,p){for(var h=a-1,j=a===0?o:[""],A=pr(j),S=0,f=0,y=0;S<n;++S)for(var m=0,T=ve(e,h+1,h=Qt(f=i[S])),w=e;m<A;++m)(w=st(f>0?j[m]+" "+T:_(T,/&\f/g,j[m])))&&(l[y++]=w);return We(e,r,s,a===0?dr:c,l,u,p)}function us(e,r,s){return We(e,r,s,rt,Ne(ns()),ve(e,2,-2),0)}function Or(e,r,s,n){return We(e,r,s,ur,ve(e,0,n),ve(e,n+1,-1),n)}function de(e,r){for(var s="",n=pr(e),a=0;a<n;a++)s+=r(e[a],a,e,r)||"";return s}function ps(e,r,s,n){switch(e.type){case Zt:if(e.children.length)break;case Xt:case ur:return e.return=e.return||e.value;case rt:return"";case tt:return e.return=e.value+"{"+de(e.children,n)+"}";case dr:e.value=e.props.join(",")}return G(s=de(e.children,n))?e.return=e.value+"{"+s+"}":""}function fs(e){var r=pr(e);return function(s,n,a,o){for(var i="",c=0;c<r;c++)i+=e[c](s,n,a,o)||"";return i}}function hs(e){return function(r){r.root||(r=r.return)&&e(r)}}function it(e){var r=Object.create(null);return function(s){return r[s]===void 0&&(r[s]=e(s)),r[s]}}var gs=function(r,s,n){for(var a=0,o=0;a=o,o=Y(),a===38&&o===12&&(s[n]=1),!je(o);)N();return Ce(r,V)},ms=function(r,s){var n=-1,a=44;do switch(je(a)){case 0:a===38&&Y()===12&&(s[n]=1),r[n]+=gs(V-1,s,n);break;case 2:r[n]+=Le(a);break;case 4:if(a===44){r[++n]=Y()===58?"&\f":"",s[n]=r[n].length;break}default:r[n]+=Ne(a)}while(a=N());return r},xs=function(r,s){return ot(ms(at(r),s))},Rr=new WeakMap,ys=function(r){if(!(r.type!=="rule"||!r.parent||r.length<1)){for(var s=r.value,n=r.parent,a=r.column===n.column&&r.line===n.line;n.type!=="rule";)if(n=n.parent,!n)return;if(!(r.props.length===1&&s.charCodeAt(0)!==58&&!Rr.get(n))&&!a){Rr.set(r,!0);for(var o=[],i=xs(s,o),c=n.props,l=0,u=0;l<i.length;l++)for(var p=0;p<c.length;p++,u++)r.props[u]=o[l]?i[l].replace(/&\f/g,c[p]):c[p]+" "+i[l]}}},ws=function(r){if(r.type==="decl"){var s=r.value;s.charCodeAt(0)===108&&s.charCodeAt(2)===98&&(r.return="",r.value="")}};function ct(e,r){switch(rs(e,r)){case 5103:return I+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return I+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return I+e+Fe+e+z+e+e;case 6828:case 4268:return I+e+z+e+e;case 6165:return I+e+z+"flex-"+e+e;case 5187:return I+e+_(e,/(\w+).+(:[^]+)/,I+"box-$1$2"+z+"flex-$1$2")+e;case 5443:return I+e+z+"flex-item-"+_(e,/flex-|-self/,"")+e;case 4675:return I+e+z+"flex-line-pack"+_(e,/align-content|flex-|-self/,"")+e;case 5548:return I+e+z+_(e,"shrink","negative")+e;case 5292:return I+e+z+_(e,"basis","preferred-size")+e;case 6060:return I+"box-"+_(e,"-grow","")+I+e+z+_(e,"grow","positive")+e;case 4554:return I+_(e,/([^-])(transform)/g,"$1"+I+"$2")+e;case 6187:return _(_(_(e,/(zoom-|grab)/,I+"$1"),/(image-set)/,I+"$1"),e,"")+e;case 5495:case 3959:return _(e,/(image-set\([^]*)/,I+"$1$`$1");case 4968:return _(_(e,/(.+:)(flex-)?(.*)/,I+"box-pack:$3"+z+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+I+e+e;case 4095:case 3583:case 4068:case 2532:return _(e,/(.+)-inline(.+)/,I+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(G(e)-1-r>6)switch(F(e,r+1)){case 109:if(F(e,r+4)!==45)break;case 102:return _(e,/(.+:)(.+)-([^]+)/,"$1"+I+"$2-$3$1"+Fe+(F(e,r+3)==108?"$3":"$2-$3"))+e;case 115:return~tr(e,"stretch")?ct(_(e,"stretch","fill-available"),r)+e:e}break;case 4949:if(F(e,r+1)!==115)break;case 6444:switch(F(e,G(e)-3-(~tr(e,"!important")&&10))){case 107:return _(e,":",":"+I)+e;case 101:return _(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+I+(F(e,14)===45?"inline-":"")+"box$3$1"+I+"$2$3$1"+z+"$2box$3")+e}break;case 5936:switch(F(e,r+11)){case 114:return I+e+z+_(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return I+e+z+_(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return I+e+z+_(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return I+e+z+e+e}return e}var bs=function(r,s,n,a){if(r.length>-1&&!r.return)switch(r.type){case ur:r.return=ct(r.value,r.length);break;case tt:return de([he(r,{value:_(r.value,"@","@"+I)})],a);case dr:if(r.length)return ss(r.props,function(o){switch(ts(o,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return de([he(r,{props:[_(o,/:(read-\w+)/,":"+Fe+"$1")]})],a);case"::placeholder":return de([he(r,{props:[_(o,/:(plac\w+)/,":"+I+"input-$1")]}),he(r,{props:[_(o,/:(plac\w+)/,":"+Fe+"$1")]}),he(r,{props:[_(o,/:(plac\w+)/,z+"input-$1")]})],a)}return""})}},vs=[bs],js=function(r){var s=r.key;if(s==="css"){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,function(f){var y=f.getAttribute("data-emotion");y.indexOf(" ")!==-1&&(document.head.appendChild(f),f.setAttribute("data-s",""))})}var a=r.stylisPlugins||vs,o={},i,c=[];i=r.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+s+' "]'),function(f){for(var y=f.getAttribute("data-emotion").split(" "),m=1;m<y.length;m++)o[y[m]]=!0;c.push(f)});var l,u=[ys,ws];{var p,h=[ps,hs(function(f){p.insert(f)})],j=fs(u.concat(a,h)),A=function(y){return de(ds(y),j)};l=function(y,m,T,w){p=T,A(y?y+"{"+m.styles+"}":m.styles),w&&(S.inserted[m.name]=!0)}}var S={key:s,sheet:new Jt({key:s,container:i,nonce:r.nonce,speedy:r.speedy,prepend:r.prepend,insertionPoint:r.insertionPoint}),nonce:r.nonce,inserted:o,registered:{},insert:l};return S.sheet.hydrate(c),S},Ss=!0;function ks(e,r,s){var n="";return s.split(" ").forEach(function(a){e[a]!==void 0?r.push(e[a]+";"):a&&(n+=a+" ")}),n}var lt=function(r,s,n){var a=r.key+"-"+s.name;(n===!1||Ss===!1)&&r.registered[a]===void 0&&(r.registered[a]=s.styles)},Cs=function(r,s,n){lt(r,s,n);var a=r.key+"-"+s.name;if(r.inserted[s.name]===void 0){var o=s;do r.insert(s===o?"."+a:"",o,r.sheet,!0),o=o.next;while(o!==void 0)}};function Ts(e){for(var r=0,s,n=0,a=e.length;a>=4;++n,a-=4)s=e.charCodeAt(n)&255|(e.charCodeAt(++n)&255)<<8|(e.charCodeAt(++n)&255)<<16|(e.charCodeAt(++n)&255)<<24,s=(s&65535)*1540483477+((s>>>16)*59797<<16),s^=s>>>24,r=(s&65535)*1540483477+((s>>>16)*59797<<16)^(r&65535)*1540483477+((r>>>16)*59797<<16);switch(a){case 3:r^=(e.charCodeAt(n+2)&255)<<16;case 2:r^=(e.charCodeAt(n+1)&255)<<8;case 1:r^=e.charCodeAt(n)&255,r=(r&65535)*1540483477+((r>>>16)*59797<<16)}return r^=r>>>13,r=(r&65535)*1540483477+((r>>>16)*59797<<16),((r^r>>>15)>>>0).toString(36)}var $s={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},Es=!1,As=/[A-Z]|^ms/g,Os=/_EMO_([^_]+?)_([^]*?)_EMO_/g,dt=function(r){return r.charCodeAt(1)===45},Ir=function(r){return r!=null&&typeof r!="boolean"},Ge=it(function(e){return dt(e)?e:e.replace(As,"-$&").toLowerCase()}),_r=function(r,s){switch(r){case"animation":case"animationName":if(typeof s=="string")return s.replace(Os,function(n,a,o){return q={name:a,styles:o,next:q},a})}return $s[r]!==1&&!dt(r)&&typeof s=="number"&&s!==0?s+"px":s},Rs="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function Se(e,r,s){if(s==null)return"";var n=s;if(n.__emotion_styles!==void 0)return n;switch(typeof s){case"boolean":return"";case"object":{var a=s;if(a.anim===1)return q={name:a.name,styles:a.styles,next:q},a.name;var o=s;if(o.styles!==void 0){var i=o.next;if(i!==void 0)for(;i!==void 0;)q={name:i.name,styles:i.styles,next:q},i=i.next;var c=o.styles+";";return c}return Is(e,r,s)}case"function":{if(e!==void 0){var l=q,u=s(e);return q=l,Se(e,r,u)}break}}var p=s;if(r==null)return p;var h=r[p];return h!==void 0?h:p}function Is(e,r,s){var n="";if(Array.isArray(s))for(var a=0;a<s.length;a++)n+=Se(e,r,s[a])+";";else for(var o in s){var i=s[o];if(typeof i!="object"){var c=i;r!=null&&r[c]!==void 0?n+=o+"{"+r[c]+"}":Ir(c)&&(n+=Ge(o)+":"+_r(o,c)+";")}else{if(o==="NO_COMPONENT_SELECTOR"&&Es)throw new Error(Rs);if(Array.isArray(i)&&typeof i[0]=="string"&&(r==null||r[i[0]]===void 0))for(var l=0;l<i.length;l++)Ir(i[l])&&(n+=Ge(o)+":"+_r(o,i[l])+";");else{var u=Se(e,r,i);switch(o){case"animation":case"animationName":{n+=Ge(o)+":"+u+";";break}default:n+=o+"{"+u+"}"}}}}return n}var Pr=/label:\s*([^\s;{]+)\s*(;|$)/g,q;function _s(e,r,s){if(e.length===1&&typeof e[0]=="object"&&e[0]!==null&&e[0].styles!==void 0)return e[0];var n=!0,a="";q=void 0;var o=e[0];if(o==null||o.raw===void 0)n=!1,a+=Se(s,r,o);else{var i=o;a+=i[0]}for(var c=1;c<e.length;c++)if(a+=Se(s,r,e[c]),n){var l=o;a+=l[c]}Pr.lastIndex=0;for(var u="",p;(p=Pr.exec(a))!==null;)u+="-"+p[1];var h=Ts(a)+u;return{name:h,styles:a,next:q}}var Ps=function(r){return r()},Ls=br["useInsertionEffect"]?br["useInsertionEffect"]:!1,Ds=Ls||Ps,ut=d.createContext(typeof HTMLElement<"u"?js({key:"css"}):null);ut.Provider;var Ms=function(r){return d.forwardRef(function(s,n){var a=d.useContext(ut);return r(s,a,n)})},Fs=d.createContext({}),zs=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,Us=it(function(e){return zs.test(e)||e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)<91}),Vs=!1,Ns=Us,Bs=function(r){return r!=="theme"},Lr=function(r){return typeof r=="string"&&r.charCodeAt(0)>96?Ns:Bs},Dr=function(r,s,n){var a;if(s){var o=s.shouldForwardProp;a=r.__emotion_forwardProp&&o?function(i){return r.__emotion_forwardProp(i)&&o(i)}:o}return typeof a!="function"&&n&&(a=r.__emotion_forwardProp),a},Ws=function(r){var s=r.cache,n=r.serialized,a=r.isStringTag;return lt(s,n,a),Ds(function(){return Cs(s,n,a)}),null},Hs=function e(r,s){var n=r.__emotion_real===r,a=n&&r.__emotion_base||r,o,i;s!==void 0&&(o=s.label,i=s.target);var c=Dr(r,s,n),l=c||Lr(a),u=!l("as");return function(){var p=arguments,h=n&&r.__emotion_styles!==void 0?r.__emotion_styles.slice(0):[];if(o!==void 0&&h.push("label:"+o+";"),p[0]==null||p[0].raw===void 0)h.push.apply(h,p);else{var j=p[0];h.push(j[0]);for(var A=p.length,S=1;S<A;S++)h.push(p[S],j[S])}var f=Ms(function(y,m,T){var w=u&&y.as||a,v="",C=[],$=y;if(y.theme==null){$={};for(var b in y)$[b]=y[b];$.theme=d.useContext(Fs)}typeof y.className=="string"?v=ks(m.registered,C,y.className):y.className!=null&&(v=y.className+" ");var R=_s(h.concat(C),m.registered,$);v+=m.key+"-"+R.name,i!==void 0&&(v+=" "+i);var x=u&&c===void 0?Lr(w):l,g={};for(var P in y)u&&P==="as"||x(P)&&(g[P]=y[P]);return g.className=v,T&&(g.ref=T),d.createElement(d.Fragment,null,d.createElement(Ws,{cache:m,serialized:R,isStringTag:typeof w=="string"}),d.createElement(w,g))});return f.displayName=o!==void 0?o:"Styled("+(typeof a=="string"?a:a.displayName||a.name||"Component")+")",f.defaultProps=r.defaultProps,f.__emotion_real=f,f.__emotion_base=a,f.__emotion_styles=h,f.__emotion_forwardProp=c,Object.defineProperty(f,"toString",{value:function(){return i===void 0&&Vs?"NO_COMPONENT_SELECTOR":"."+i}}),f.withComponent=function(y,m){var T=e(y,ze({},s,m,{shouldForwardProp:Dr(f,m,!0)}));return T.apply(void 0,h)},f}},Ks=["a","abbr","address","area","article","aside","audio","b","base","bdi","bdo","big","blockquote","body","br","button","canvas","caption","cite","code","col","colgroup","data","datalist","dd","del","details","dfn","dialog","div","dl","dt","em","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","img","input","ins","kbd","keygen","label","legend","li","link","main","map","mark","marquee","menu","menuitem","meta","meter","nav","noscript","object","ol","optgroup","option","output","p","param","picture","pre","progress","q","rp","rt","ruby","s","samp","script","section","select","small","source","span","strong","style","sub","summary","sup","table","tbody","td","textarea","tfoot","th","thead","time","title","tr","track","u","ul","var","video","wbr","circle","clipPath","defs","ellipse","foreignObject","g","image","line","linearGradient","mask","path","pattern","polygon","polyline","radialGradient","rect","stop","svg","text","tspan"],k=Hs.bind(null);Ks.forEach(function(e){k[e]=k(e)});let nr=null;const Gs=e=>{nr=e},E=(e,r)=>{},qs=async()=>{try{const e=await fetch("/api/auth");if(!e.ok)throw new Error(`请求失败 (${e.status}): ${await e.text()}`);return(await e.json()).authUrl}catch(e){throw new Error(`获取授权URL失败: ${e.message}`)}},Ys=async e=>{try{const r=new AbortController,s=setTimeout(()=>r.abort(),15e3),n=window.location.host,a=window.location.protocol,o=n.includes("ngdevops.cn")||n.includes("neuralgalaxy.com")||!n.includes("localhost");let i;try{const h=await(await fetch("/api/auth")).json();h&&h.redirect_uri?(i=h.redirect_uri,E(`使用后端配置的redirect_uri: ${i}`)):(i=o?"https://ptc-tools.ngdevops.cn/callback":`${a}//${n}/callback`,E(`未找到后端配置的redirect_uri，使用生成的: ${i}`))}catch(p){E("获取后端配置失败，使用默认redirect_uri",p),i=o?"https://ptc-tools.ngdevops.cn/callback":`${a}//${n}/callback`}E(`最终使用的redirect_uri: ${i}`);const c=await fetch("/api/token",{method:"POST",headers:{"Content-Type":"application/json","X-Requested-With":"XMLHttpRequest"},body:JSON.stringify({code:e,redirect_uri:i}),signal:r.signal,credentials:"include"});if(clearTimeout(s),!c.ok){const p=await c.text();throw E(`令牌交换失败 (${c.status}): ${p}`),c.status===429?new Error(`认证请求过于频繁: ${p||"请勿重复提交相同的认证请求"}`):c.status===500?new Error(`获取令牌失败 (服务器错误): ${p||"服务器内部错误"}`):c.status===401||c.status===403?new Error(`认证失败: ${p||"授权码无效或已过期"}`):c.status===400?new Error(`认证请求无效: ${p||"错误的授权请求，可能是redirect_uri不匹配"}`):new Error(`请求失败 (${c.status}): ${p||"未知错误"}`)}c.headers.get("X-Auth-Cached")==="true"&&E("使用缓存的认证结果");let u;try{u=await c.json()}catch(p){throw E("解析响应数据失败",p),new Error("服务器返回了无效的JSON数据")}if(!u.token||!u.user)throw new Error("服务器返回的认证数据不完整");return E("令牌交换成功",{username:u.user.preferred_username,expires_in:u.token.expires_in}),u}catch(r){throw r instanceof TypeError&&r.message.includes("fetch")?new Error("网络连接错误: 无法连接到认证服务器，请检查网络连接"):r.name==="AbortError"?new Error("请求超时: 认证服务器响应时间过长，请稍后重试"):r.message.includes("认证请求过于频繁")?new Error("认证已处理: 您的认证请求正在进行中或已完成，请稍候..."):r.message.includes("redirect_uri不匹配")?new Error("认证配置错误: 请求使用的回调地址与服务器配置不匹配，请联系管理员"):new Error(`交换令牌失败: ${r.message}`)}},Js=async e=>{try{const r=await fetch("/api/userinfo",{headers:{Authorization:`Bearer ${e}`}});if(!r.ok)throw new Error(`请求失败 (${r.status}): ${await r.text()}`);return await r.json()}catch(r){throw new Error(`获取用户信息失败: ${r.message}`)}},Te=async(e,r={},s=3,n=1e3)=>{let a;for(let o=0;o<s;o++)try{E(`Fetching ${e} attempt ${o+1}/${s}`);const i=await fetch(e,r);if(!i.ok){const c=await i.text();throw E(`Request failed with status ${i.status}`,c),new Error(`请求失败 (${i.status}): ${c}`)}return i}catch(i){a=i,E(`Fetch error: ${a.message}`),o<s-1&&(await new Promise(c=>setTimeout(c,n)),n*=2)}throw a},pt=(e={},r)=>{if(!r)return e;const s={...e.headers,Authorization:`Bearer ${r}`};return{...e,headers:s}},Xs=async e=>{try{const s=await(await Te("/api/versions",pt({},e))).json();if(E("版本列表获取成功",s),!Array.isArray(s)){if(E("警告：返回数据不是数组",s),s&&typeof s=="object"){if(Array.isArray(s.versions))return s.versions;if(s.data&&Array.isArray(s.data))return s.data;{const n=Object.values(s).filter(a=>typeof a=="string");if(n.length>0)return n.map(a=>({Version:a,CreatedAt:new Date().toISOString(),PackageName:"ngiq-point-desktop"}))}}throw new Error("服务器返回的版本列表格式不正确")}return s.length>0&&typeof s[0]=="object"&&"Version"in s[0]?s:s.length>0&&typeof s[0]=="string"?s.map(n=>({Version:n,CreatedAt:new Date().toISOString(),PackageName:"ngiq-point-desktop"})):s}catch(r){throw new Error(`获取版本列表失败: ${r.message}`)}},Zs=async(e,r,s,n,a,o)=>{var A;let c=Date.now(),l,u=null,p;const h=()=>{p&&(clearInterval(p),p=void 0),l&&(clearTimeout(l),l=void 0)},j=()=>{l&&clearTimeout(l),l=window.setTimeout(()=>{n({message:"上传已超时，自动标记为完成状态",progress:100,type:"warning",summary:u||{total:"未知",success:"未知",failed:"未知",env:e,version:r,note:"上传过程超时，状态不确定"}}),o==null||o("上传超时",`版本 ${r} 上传超时，自动标记为完成状态`,"warning")},6e4-(Date.now()-c))};j();try{const S=await fetch("/api/upload",pt({method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({targetEnv:e,version:r}),signal:s},a));if(!S.ok){const w=await S.text();E(`上传请求失败 ${S.status}`,w);const v=qe(S.statusText,w);throw o==null||o("上传失败",`上传版本 ${r} 失败: ${v}`,"error"),l&&clearTimeout(l),new Error(v)}const f=(A=S.body)==null?void 0:A.getReader();if(!f)throw l&&clearTimeout(l),o==null||o("上传失败","无法读取响应流","error"),new Error("无法读取响应流");E("开始读取上传进度流");let y=Date.now();const m=15e3;let T=0;p=setInterval(()=>{const w=Date.now()-y;E(`空闲时间检查: ${w}ms`),w>m&&T>90&&(E("检测到上传可能已完成但流未关闭，准备关闭"),p&&(clearInterval(p),p=void 0),l&&(clearTimeout(l),l=void 0),n({message:"上传可能已完成，但未收到明确完成信号",progress:100,type:"warning",summary:u||{note:"未收到服务器的完成确认，状态不确定",env:e,version:r}}),o==null||o("上传状态未知",`版本 ${r} 上传流已中断，状态不确定`,"warning"),f.cancel("上传流空闲超时").catch(v=>E("取消读取器时出错",v)))},1e3);try{for(;;){const{done:w,value:v}=await f.read();if(w){E("上传流已关闭"),h(),u?o==null||o("上传完成",u.failed>0?`版本 ${r} 上传部分完成: ${u.success}/${u.total} 文件成功`:`版本 ${r} 已成功上传到 ${e} 环境`,u.failed>0?"warning":"success"):(n({message:"上传流已关闭，但未收到完成状态",progress:T,type:"warning",summary:{note:"服务器流已关闭，但未收到明确完成信号",env:e,version:r}}),o==null||o("上传状态未知",`版本 ${r} 上传流已关闭，但未收到完成确认`,"warning"));break}y=Date.now(),j();const $=new TextDecoder().decode(v).split(`
`).filter(Boolean);for(const b of $)try{E("接收进度更新",b);const R=JSON.parse(b);if(typeof R.progress=="number"&&(T=R.progress),R.type==="error"&&(R.message=qe(R.message||"上传过程中发生错误"),o==null||o("上传出错",R.message,"error")),R.done===!0){E("收到后端发送的明确结束信号，准备关闭流"),u=R.summary||{env:e,version:r};const x=R.type||(R.progress===100?"success":u&&u.failed>0?"warning":"info");n({message:R.message||"上传操作已完成",progress:R.progress||100,type:x,summary:u});const g=x==="success"?"上传完成":"上传部分完成",P=R.message||(u&&u.failed>0?`版本 ${r} 上传部分完成: ${u.success}/${u.total} 文件成功`:`版本 ${r} 已成功上传到 ${e} 环境`);o==null||o(g,P,x),h(),f.cancel("后端已发送结束信号").catch(H=>E("取消读取器时出错",H));break}n(R)}catch{E("解析进度更新失败",b),console.error("Failed to parse update:",b)}}}catch(w){throw h(),w}finally{h()}}catch(S){if(h(),S.name==="AbortError")throw o==null||o("上传取消",`版本 ${r} 的上传已被取消`,"warning"),S;const f=qe(S.message);throw o==null||o("上传失败",`上传版本 ${r} 失败: ${f}`,"error"),new Error(f)}},qe=(e,r)=>{let s=e.replace(/上传失败: /g,"");if(r){if(r.includes("未授权")||r.includes("Unauthorized"))return r.includes("获取用户信息失败")?"认证失败: 获取用户信息失败，请尝试重新登录":"认证失败: 请检查您的登录状态";s.endsWith(")")&&r.startsWith("(")&&(r=r.substring(1,r.length-1)),s.includes(r)||(s=`${s}: ${r}`)}return s},Qs=async()=>{try{const r=await(await Te("/api/lambda/services")).json();if(E("Lambda服务列表获取成功",r),!Array.isArray(r)){if(E("警告：返回数据不是数组",r),r&&typeof r=="object"){if(Array.isArray(r.services))return r.services;if(r.data&&Array.isArray(r.data))return r.data}throw new Error("服务器返回的服务列表格式不正确")}return r}catch(e){throw new Error(`获取Lambda服务列表失败: ${e.message}`)}},en=async e=>{try{const s=await(await Te(`/api/lambda/versions?service=${encodeURIComponent(e)}`)).json();if(E("Lambda版本列表获取成功",s),!Array.isArray(s)){if(E("警告：返回数据不是数组",s),s&&typeof s=="object"){if(Array.isArray(s.versions))return s.versions;if(s.data&&Array.isArray(s.data))return s.data;{const n=Object.values(s).filter(a=>typeof a=="string");if(n.length>0)return n.map(a=>({Version:a,CreatedAt:new Date().toISOString(),PackageName:e}))}}throw new Error("服务器返回的版本列表格式不正确")}return s.length>0&&typeof s[0]=="object"&&"Version"in s[0]?s:s.length>0&&typeof s[0]=="string"?s.map(n=>({Version:n,CreatedAt:new Date().toISOString(),PackageName:e})):s}catch(r){throw new Error(`获取Lambda版本列表失败: ${r.message}`)}},rn=async()=>{try{const r=await(await Te("/api/diagnostics")).json();return E("系统诊断信息获取成功",r),r}catch(e){throw new Error(`获取系统诊断信息失败: ${e.message}`)}},tn=async()=>{try{const r=await(await Te("/api/diagnostics/desktop")).json();return E("桌面客户端系统诊断信息获取成功",r),r}catch(e){throw new Error(`获取桌面客户端系统诊断信息失败: ${e.message}`)}},sn=async(e,r,s,n)=>{var c;let a=!1,o=!1;const i=e.serviceName||e.serviceType;n==null||n("开始部署",`开始部署 ${i} 版本 ${e.version} 到 ${e.env} 环境`,"info");try{let l="";if(nr)l=nr()||"";else try{const j=localStorage.getItem("auth_token");j&&(l=JSON.parse(j).access_token)}catch(j){E("获取认证令牌失败",j)}const u={"Content-Type":"application/json"};l&&(u.Authorization=`Bearer ${l}`);const p=await fetch("/api/lambda/deploy",{method:"POST",headers:u,body:JSON.stringify(e),signal:r});if(!p.ok){const j=await p.text();throw E(`部署请求失败 ${p.status}`,j),n==null||n("部署失败",`部署 ${i} 失败: ${p.statusText}`,"error"),new Error(`部署失败: ${p.statusText} (${j})`)}const h=(c=p.body)==null?void 0:c.getReader();if(!h)throw n==null||n("部署失败","无法读取响应流","error"),new Error("无法读取响应流");for(E("开始读取部署进度流");;)try{const{done:j,value:A}=await h.read();if(j){E("部署流程正常结束"),a=!o,a&&(n==null||n("部署完成",`${i} 版本 ${e.version} 已成功部署到 ${e.env} 环境`,"success"));break}const f=new TextDecoder().decode(A).split(`
`).filter(Boolean);for(const y of f)try{E("接收进度更新",y);const m=JSON.parse(y);s(m),m.progress===25?n==null||n("部署进行中",`${i} 部署进度: 25%`,"info"):m.progress===50?n==null||n("部署进行中",`${i} 部署进度: 50%`,"info"):m.progress===75?n==null||n("部署进行中",`${i} 部署进度: 75%`,"info"):m.progress===100&&m.type==="success"&&(n==null||n("部署完成",`${i} 版本 ${e.version} 已成功部署到 ${e.env} 环境`,"success")),m.type==="error"&&(E("收到错误消息",m.message),o=!0,n==null||n("部署出错",m.message||"部署过程中发生错误","error"))}catch{E("解析进度更新失败",y),console.error("Failed to parse update:",y)}}catch(j){if(j.name!=="AbortError")throw E("读取流时出错",j),o=!0,n==null||n("部署中断",`${i} 部署中断: ${j.message}`,"error"),new Error(`部署中断: ${j.message}`);break}return a}catch(l){throw l.name==="AbortError"?(n==null||n("部署取消",`${i} 的部署已被取消`,"warning"),l):(n==null||n("部署失败",`部署 ${i} 失败: ${l.message}`,"error"),new Error(`部署失败: ${l.message}`))}},fr="auth_token",hr="auth_user",gr="token_expiry",mr="login_timestamp",Mr=e=>{try{if(localStorage.setItem(fr,JSON.stringify(e.token)),localStorage.setItem(hr,JSON.stringify(e.user)),e.token.expires_in){const r=new Date(Date.now()+e.token.expires_in*1e3);localStorage.setItem(gr,r.toISOString())}localStorage.setItem(mr,Date.now().toString())}catch(r){throw console.error("保存认证信息失败:",r),we(),r}},Ye=()=>{try{const e=localStorage.getItem(fr),r=localStorage.getItem(hr);if(!e||!r)return null;const s=JSON.parse(e),n=JSON.parse(r);return{token:s,user:n}}catch(e){return console.error("读取认证信息失败:",e),we(),null}},we=()=>{localStorage.removeItem(fr),localStorage.removeItem(hr),localStorage.removeItem(gr),localStorage.removeItem(mr)},Fr=()=>{try{const e=localStorage.getItem(gr);if(!e)return!0;const r=new Date(e).getTime();return Date.now()>=r}catch(e){return console.error("检查令牌过期失败:",e),!0}},zr=()=>{try{const e=localStorage.getItem(mr);return e?parseInt(e,10):0}catch(e){return console.error("获取上次登录时间失败:",e),0}},ft=d.createContext({isAuthenticated:!1,isLoading:!0,isLoginInProgress:!1,user:null,token:null,login:()=>!1,logout:()=>{},getAccessToken:()=>null,refreshUserInfo:async()=>{},setLoginInProgress:()=>{}}),fe=()=>d.useContext(ft),Ur=5*60*1e3,nn=2e3,Vr=3e4,an=({children:e})=>{const[r,s]=d.useState(!1),[n,a]=d.useState(!0),[o,i]=d.useState(!1),[c,l]=d.useState(null),[u,p]=d.useState(null),h=d.useRef(0),j=d.useRef(0),A=d.useRef(!1),S=d.useRef(!1),f=d.useCallback(()=>{p(null),l(null),s(!1),we(),console.log("用户已登出")},[]),y=d.useCallback(async()=>{if(!(u!=null&&u.access_token))return;const v=Date.now();if(v-j.current<Vr){console.log("忽略重复刷新请求，时间间隔过短");return}if(A.current){console.log("已有刷新请求正在进行中，跳过");return}A.current=!0,j.current=v;try{console.log("刷新用户信息...");const C=await Js(u.access_token),$=Ye();$&&(JSON.stringify(C)!==JSON.stringify(c)?(Mr({token:$.token,user:C}),l(C),console.log("用户信息已更新")):console.log("用户信息未变化，无需更新"))}catch(C){console.error("刷新用户信息失败:",C),(C.message.includes("未授权")||C.message.includes("Unauthorized"))&&(console.warn("认证失败，可能是令牌已过期"),f())}finally{A.current=!1}},[u,c,f]);d.useEffect(()=>{if(S.current)return;(async()=>{try{const C=Ye(),$=zr();if($>0&&(h.current=$),C)if(Fr())console.warn("令牌已过期，清理存储"),we();else{p(C.token),l(C.user),s(!0),console.log("令牌有效，完成认证初始化");const b=Date.now();new Date(C.token.expires_in*1e3+$).getTime()-b<Ur&&(console.log("令牌即将过期，稍后将尝试刷新用户信息"),setTimeout(()=>{A.current||y().catch(g=>console.error("初始化后刷新用户信息失败:",g))},1e3))}}catch(C){console.error("初始化认证状态失败:",C),we()}finally{a(!1),S.current=!0}})()},[]),d.useEffect(()=>{const v=()=>{document.visibilityState==="visible"&&r&&!A.current&&y().catch(C=>console.error("页面可见性变化后刷新用户信息失败:",C))};return document.addEventListener("visibilitychange",v),()=>{document.removeEventListener("visibilitychange",v)}},[r,y]);const m=d.useCallback((v,C)=>{const $=Date.now();if($-h.current<nn)return console.log("忽略重复登录请求，时间间隔过短"),!1;if(o)return console.log("登录正在进行中，忽略重复请求"),!1;i(!0);try{return h.current=$,Mr({token:v,user:C}),p(v),l(C),s(!0),console.log(`用户 ${C.preferred_username} 登录成功`),!0}catch(b){return console.error("保存登录状态失败:",b),!1}finally{i(!1)}},[o]),T=d.useCallback(()=>{if(u!=null&&u.access_token){if(Fr())return console.warn("令牌已过期，需要重新登录"),f(),null;const v=Ye();if(v&&!A.current){const C=Date.now(),$=zr(),R=new Date(v.token.expires_in*1e3+$).getTime()-C;R<Ur&&R>0&&C-j.current>Vr&&(console.log("令牌即将过期，安排后台刷新用户信息"),setTimeout(()=>{A.current||y().catch(x=>console.error("后台刷新用户信息失败:",x))},0))}}return(u==null?void 0:u.access_token)||null},[u,f,y]),w=v=>{i(v)};return t.jsx(ft.Provider,{value:{isAuthenticated:r,isLoading:n,isLoginInProgress:o,user:c,token:u,login:m,logout:f,getAccessToken:T,refreshUserInfo:y,setLoginInProgress:w},children:e})},{Text:ye,Title:on}=ae,cn=k.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--color-fill-2);
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: var(--color-fill-3);
  }
`,ln=k.div`
  min-width: 300px;
  max-width: 400px;
  max-height: 500px;
  overflow: auto;
`,dn=k.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 16px;
  border-bottom: 1px solid var(--color-border);
`,un=k.div`
  padding: 32px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
`,pn=k(Ze.Item)`
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  background-color: ${e=>e.read?"transparent":"rgba(22, 93, 255, 0.05)"};
  
  &:hover {
    background-color: var(--color-fill-2);
  }
`,fn=k(ye)`
  font-weight: ${e=>e.read?"normal":"bold"};
  margin: 0;
  line-height: 1.4;
`,hn=({notifications:e,onMarkAsRead:r,onMarkAllAsRead:s,onClear:n})=>{const[a,o]=d.useState(0);d.useEffect(()=>{o(e.filter(l=>!l.read).length)},[e]);const i=l=>{const p=new Date().getTime()-l.getTime();return p<60*1e3?"刚刚":p<60*60*1e3?`${Math.floor(p/(60*1e3))}分钟前`:p<24*60*60*1e3?`${Math.floor(p/(60*60*1e3))}小时前`:l.toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})},c=t.jsxs(ln,{children:[t.jsxs(dn,{children:[t.jsxs(on,{heading:6,style:{margin:0},children:["通知中心 ",a>0&&`(${a})`]}),t.jsxs(B,{children:[a>0&&t.jsx(ye,{style:{cursor:"pointer",fontSize:"12px",color:"var(--color-text-3)"},onClick:s,children:"全部已读"}),t.jsx(ye,{style:{cursor:"pointer",fontSize:"12px",color:"var(--color-text-3)"},onClick:n,children:"清空"})]})]}),e.length>0?t.jsx(Ze,{dataSource:e.sort((l,u)=>u.priority-l.priority),render:(l,u)=>t.jsx(pn,{read:l.read,onClick:()=>r(l.id),children:t.jsx(Ze.Item.Meta,{title:t.jsx(fn,{read:l.read,children:l.title}),description:t.jsxs(t.Fragment,{children:[t.jsx(ye,{style:{fontSize:"13px"},children:l.message}),t.jsx("br",{}),t.jsx(ye,{style:{fontSize:"12px",color:"var(--color-text-3)"},children:i(l.timestamp)})]})})},l.id)}):t.jsx(un,{children:t.jsx(Me,{description:"暂无通知"})})]});return t.jsx(Yr,{droplist:c,position:"br",trigger:"click",children:t.jsx(be,{count:a,dot:a>0,children:t.jsx(cn,{children:t.jsx(Kt,{style:{fontSize:20}})})})})},ht=d.createContext({notifications:[],addNotification:()=>{},markAsRead:()=>{},markAllAsRead:()=>{},clearNotifications:()=>{}}),gt="notifications",gn=()=>`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,mn=e=>{switch(e){case"error":return 3;case"success":return 2;default:return 1}},Ee=e=>{try{localStorage.setItem(gt,JSON.stringify(e))}catch(r){console.error("保存通知到本地存储失败:",r)}},xn=()=>{try{const e=localStorage.getItem(gt);if(e)return JSON.parse(e).map(s=>({...s,timestamp:new Date(s.timestamp)}))}catch(e){console.error("从本地存储加载通知失败:",e)}return[]},yn=(e,r)=>{switch(r.type){case"ADD_NOTIFICATION":const s=[r.payload,...e].slice(0,50);return Ee(s),s;case"MARK_AS_READ":const n=e.map(o=>o.id===r.payload.id?{...o,read:!0}:o);return Ee(n),n;case"MARK_ALL_AS_READ":const a=e.map(o=>({...o,read:!0}));return Ee(a),a;case"CLEAR_NOTIFICATIONS":return Ee([]),[];default:return e}},xr=()=>d.useContext(ht),wn=({children:e})=>{const[r,s]=d.useReducer(yn,[],()=>xn()),n=c=>{s({type:"ADD_NOTIFICATION",payload:{...c,id:gn(),timestamp:new Date,read:!1,priority:mn(c.type)}})},a=c=>{s({type:"MARK_AS_READ",payload:{id:c}})},o=()=>{s({type:"MARK_ALL_AS_READ"})},i=()=>{s({type:"CLEAR_NOTIFICATIONS"})};return t.jsx(ht.Provider,{value:{notifications:r,addNotification:n,markAsRead:a,markAllAsRead:o,clearNotifications:i},children:e})},{Title:bn}=ae,{Header:vn,Content:jn,Footer:Sn}=Xr,{Row:kn,Col:Cn}=Ue,Nr=Jr.TabPane,Tn=k(Xr)`
  min-height: 100vh;
  background-color: var(--color-bg-2);
`,$n=k(vn)`
  background-color: var(--color-bg-2);
  padding: 10px 24px;
  border-bottom: 1px solid var(--color-border);
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
`,En=k(jn)`
  padding: 24px;
  display: flex;
  justify-content: center;
  background-color: var(--color-fill-2);
`,An=k(Sn)`
  text-align: center;
  color: var(--color-text-3);
  padding: 20px;
`,On=k(bn)`
  margin: 0;
  color: var(--color-text-1);
  font-size: 28px;
  font-weight: 600;
  display: flex;
  align-items: center;
`,Rn=k.img`
  width: 48px;
  height: 48px;
  margin-right: 16px;
  object-fit: contain;
`,In=k(Jr)`
  margin-left: 48px;
  
  .arco-tabs-header-title {
    font-size: 16px;
    
    &.arco-tabs-header-title-active {
      color: rgb(22, 93, 255);
      font-weight: 500;
    }
    
    &:not(.arco-tabs-header-title-active) {
      opacity: 0.6;
    }
  }
`,_n=k.div`
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 12px;
`,Pn=k.div`
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: var(--color-fill-2);
  }
`,Ln=k.span`
  margin: 0 8px;
  color: var(--color-text-1);
  font-weight: 500;
`,Dn=k.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #165DFF;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 500;
  overflow: hidden;
  
  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
`,Mn=()=>{const e=Hr(),r=Kr(),[s,n]=d.useState(r.pathname==="/"?"/desktop":r.pathname),{user:a,logout:o}=fe(),{notifications:i,markAsRead:c,markAllAsRead:l,clearNotifications:u}=xr(),p=j=>{n(j),e(j)},h=async()=>{await o(),e("/login")};return t.jsxs(Tn,{children:[t.jsxs($n,{children:[t.jsxs(On,{heading:4,children:[t.jsx(Rn,{src:"/logo.jpg",alt:"PTC Logo"}),"PTC运维工具"]}),t.jsxs(In,{activeTab:s,onChange:p,children:[t.jsx(Nr,{title:"Lambda部署"},"/lambda"),t.jsx(Nr,{title:"客户端发布"},"/desktop")]}),t.jsxs(_n,{children:[t.jsx(hn,{notifications:i,onMarkAsRead:c,onMarkAllAsRead:l,onClear:u}),t.jsx(Yr,{droplist:t.jsx(vr,{children:t.jsxs(vr.Item,{onClick:h,children:[t.jsx(Nt,{style:{marginRight:8}}),"登出"]},"logout")}),position:"br",children:t.jsxs(Pn,{children:[t.jsx(Dn,{children:a!=null&&a.picture?t.jsx("img",{src:a.picture,alt:"用户头像"}):a!=null&&a.name?a.name.slice(0,1).toUpperCase():"U"}),t.jsx(Ln,{children:(a==null?void 0:a.preferred_username)||(a==null?void 0:a.name)||"用户"}),t.jsx(Ot,{})]})})]})]}),t.jsx(En,{children:t.jsx(kn,{style:{width:"100%",maxWidth:"1200px"},children:t.jsx(Cn,{span:24,children:t.jsx(Gr,{})})})}),t.jsxs(An,{children:["PTC运维工具 © ",new Date().getFullYear()]})]})},{Row:Fn,Col:Br}=Ue,{Text:zn}=ae,Un=k(ke)`
  width: 100%;
  margin-top: 24px;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
  
  &:hover {
    transform: translateY(-3px);
  }
`,Vn=k(Rt)`
  .arco-progress-text {
    font-weight: 500;
  }
  
  .arco-progress-line-text {
    font-size: 14px;
  }
  
  &.arco-progress-status-success .arco-progress-line-bar {
    background-color: rgb(var(--green-6));
  }
  
  &.arco-progress-line {
    .arco-progress-line-bar {
      background: linear-gradient(90deg, #3491FA 0%, #722ED1 100%);
    }
  }
`,mt=({status:e})=>{const r=n=>{switch(n){case"success":return"success";case"error":return"error";case"warning":return"warning";default:return"info"}},s=n=>{switch(n){case"success":return"操作成功";case"error":return"操作失败";case"warning":return"操作警告";default:return"操作信息"}};return e?t.jsx(Un,{children:t.jsxs(B,{direction:"vertical",style:{width:"100%"},size:"large",children:[t.jsx(It,{type:r(e.type),title:s(e.type),content:e.message,showIcon:!0,closable:!0}),t.jsxs(Fn,{align:"center",style:{marginTop:8},children:[t.jsx(Br,{flex:"auto",children:t.jsx(Vn,{percent:Math.round(e.progress),status:e.type==="error"?"error":e.progress===100?"success":"normal",animation:e.progress<100&&e.type!=="error"&&e.type!=="warning",showText:!0,width:"100%",strokeWidth:8})}),t.jsx(Br,{flex:"120px",style:{paddingLeft:16},children:t.jsx(zn,{style:{fontSize:"14px",color:"var(--color-text-2)"},children:e.type==="warning"?"已取消":e.progress<100&&e.type!=="error"?"处理中...":e.type==="error"?"已终止":"已完成"})})]})]})}):null},Nn=k.span`
  background-color: #ff7875;
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  font-size: 11px;
  font-weight: 400;
  margin-left: 6px;
  display: inline-block;
  white-space: nowrap;
  line-height: 1.2;
`,xt=({createdAt:e,className:r})=>{const s=n=>{const a=typeof n=="string"?new Date(n):n,o=a.getFullYear(),i=String(a.getMonth()+1).padStart(2,"0"),c=String(a.getDate()).padStart(2,"0"),l=String(a.getHours()).padStart(2,"0"),u=String(a.getMinutes()).padStart(2,"0");return`${o}/${i}/${c} ${l}:${u}`};return t.jsx(Nn,{className:r,children:s(e)})},{Title:Bn,Text:re}=ae,Wn=k(ke)`
  width: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  margin-top: 20px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  }
`,Ae=k.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid var(--color-border-2);
  
  &:last-child {
    border-bottom: none;
  }
`,ie=k(be)`
  .arco-badge-status-dot {
    width: 10px;
    height: 10px;
  }
`,Hn=k(W)`
  margin-left: 10px;
`,yt=({apiType:e="lambda"})=>{const[r,s]=d.useState(null),[n,a]=d.useState(!1),[o,i]=d.useState(null),c=async()=>{a(!0),i(null);try{let h;e==="desktop"?h=await tn():h=await rn(),s(h)}catch(h){i(`获取诊断信息失败: ${h.message}`),M.error(`获取诊断信息失败: ${h.message}`)}finally{a(!1)}};d.useEffect(()=>{c()},[e]);const l=()=>{c()},u=(h,j)=>t.jsxs(Ae,{children:[t.jsx(re,{children:j}),t.jsx(B,{children:h?t.jsx(ie,{status:"success",text:"正常"}):t.jsx(ie,{status:"error",text:"异常"})})]}),p=()=>{if(!r)return null;const{gitlabConnection:h}=r;return t.jsxs(Ae,{children:[t.jsx(re,{children:"GitLab 连接"}),t.jsx(B,{children:h.status==="ok"?t.jsx(ie,{status:"success",text:"连接正常"}):t.jsx(ie,{status:"error",text:`连接失败: ${h.message||"未知错误"}`})})]})};return t.jsx(Wn,{title:t.jsxs(B,{children:[t.jsx(Bn,{heading:5,style:{margin:0},children:"系统诊断"}),t.jsx(Hn,{type:"text",icon:t.jsx(Wt,{}),onClick:l,loading:n})]}),bordered:!1,hoverable:!0,children:n&&!r?t.jsx("div",{style:{padding:"20px 0",textAlign:"center"},children:t.jsx(le,{tip:"正在获取诊断信息..."})}):o?t.jsx("div",{style:{color:"var(--color-danger-6)",padding:"12px 0"},children:o}):r?t.jsxs("div",{children:[t.jsxs(re,{type:"secondary",children:["最后更新时间: ",new Date(r.timestamp).toLocaleString("zh-CN")]}),t.jsx(Qe,{style:{margin:"16px 0"}}),t.jsxs("div",{children:[u(r.gitlabToken,"GitLab Token"),u(r.osAccessKey,"对象存储访问密钥"),e==="lambda"&&"fcAccessKey"in r&&u(r.fcAccessKey,"函数计算访问密钥"),u(r.configLoaded,"配置加载"),p(),t.jsxs(Ae,{children:[t.jsx(re,{children:"认证启用状态"}),t.jsx(B,{children:t.jsx(ie,{status:"success",text:r.authEnabled?"已启用":"未启用"})})]}),t.jsxs(Ae,{children:[t.jsx(re,{children:"飞书通知启用状态"}),t.jsx(B,{children:t.jsx(ie,{status:"success",text:r.feishuEnabled?"已启用":"未启用"})})]})]}),t.jsx(Qe,{style:{margin:"16px 0"}}),t.jsxs("div",{style:{display:"flex",justifyContent:"space-between"},children:[t.jsx(re,{children:"系统版本"}),t.jsx(re,{code:!0,children:r.version})]})]}):null})},{Row:Oe,Col:ge}=Ue,{Title:Je,Text:Re}=ae,{Option:te}=ne,Kn=k(ke)`
  width: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  }
`,Wr=k.div`
  margin-bottom: 24px;
  
  .label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: var(--color-text-1);
    font-size: 14px;
  }
  
  .note {
    font-size: 12px;
    color: var(--color-text-3);
    margin-top: 4px;
  }
`,Gn=k.div`
  padding: 16px;
  background-color: var(--color-fill-2);
  border-radius: 4px;
  margin-bottom: 16px;
  color: var(--color-text-2);
  border-left: 4px solid var(--color-danger-light-4);
`,qn=k.div`
  margin-top: 16px;
  padding: 12px;
  background-color: var(--color-fill-2);
  border-radius: 4px;
  font-size: 13px;
`,ce=k.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  
  &:last-child {
    margin-bottom: 0;
  }
  
  .label {
    font-weight: 500;
    color: var(--color-text-2);
  }
  
  .value {
    color: var(--color-text-1);
  }
`,Yn=k.ul`
  margin: 8px 0 0 0;
  padding-left: 16px;
  font-size: 12px;
  color: var(--color-text-2);
  max-height: 100px;
  overflow-y: auto;
`,Jn=()=>{const[e,r]=d.useState([]),[s,n]=d.useState("staging2"),[a,o]=d.useState(""),[i,c]=d.useState(null),[l,u]=d.useState(!1),[p,h]=d.useState(!0),[j,A]=d.useState(null),S=d.useRef(null),{getAccessToken:f}=fe(),{addNotification:y}=xr(),m=d.useCallback((x=!1)=>{u(!1),S.current&&(S.current=null),x&&c(null)},[]),T=(x,g)=>{};d.useEffect(()=>{w()},[]);const w=async()=>{h(!0),A(null);try{const x=f();T("开始获取版本列表");const g=await Xs(x||void 0);T("获取到版本数据",g),r(g),g.length>0?(o(g[0].Version),T("已设置默认版本",g[0].Version)):(T("没有找到任何版本信息"),A("没有找到任何版本信息"))}catch(x){const g=`获取版本列表失败: ${x.message}`;A(g),M.error(g),y({title:"获取版本失败",message:g,type:"error"}),c({message:g,progress:0,type:"error"})}finally{h(!1)}},v=()=>{S.current&&(M.warning("上传已取消"),c({message:"上传已取消",progress:0,type:"warning",summary:{note:"操作已被用户取消",env:s,version:a}}),y({title:"上传已取消",message:`${a} 的上传操作已取消`,type:"warning"}),S.current.abort(),m())},C=async()=>{if(!a){M.error("请选择一个版本");return}u(!0),M.info("开始上传..."),c({message:"开始上传...",progress:0,type:"info"}),S.current=new AbortController;try{const x=f();await Zs(s,a,S.current.signal,g=>{c({message:g.message,progress:g.progress,type:g.type||"info",summary:g.summary}),g.progress===100&&g.type==="success"&&m()},x||void 0,(g,P,H)=>{y({title:g,message:P,type:H})})}catch(x){if(x.name==="AbortError")return;const g=`上传失败: ${x.message}`;M.error(g),c({message:g,progress:0,type:"error",summary:{note:"请查看错误详情",env:s,version:a,error:x.message}}),m()}},$=()=>{if(!(i!=null&&i.summary))return null;const x=i.summary;return t.jsx(qn,{children:t.jsxs(B,{direction:"vertical",style:{width:"100%"},children:[t.jsx(Je,{heading:6,style:{marginTop:0,marginBottom:8},children:"上传详情"}),x.env&&t.jsxs(ce,{children:[t.jsx("span",{className:"label",children:"目标环境:"}),t.jsx("span",{className:"value",children:x.env})]}),x.version&&t.jsxs(ce,{children:[t.jsx("span",{className:"label",children:"版本:"}),t.jsx("span",{className:"value",children:x.version})]}),x.total!==void 0&&t.jsxs(ce,{children:[t.jsx("span",{className:"label",children:"总文件数:"}),t.jsx("span",{className:"value",children:x.total})]}),x.success!==void 0&&t.jsxs(ce,{children:[t.jsx("span",{className:"label",children:"成功文件数:"}),t.jsxs("span",{className:"value",children:[x.success,x.total&&` (${Math.round(x.success/x.total*100)}%)`]})]}),x.failed!==void 0&&x.failed>0&&t.jsxs(t.Fragment,{children:[t.jsxs(ce,{children:[t.jsx("span",{className:"label",children:"失败文件数:"}),t.jsxs("span",{className:"value",style:{color:"var(--color-danger-6)"},children:[x.failed,x.total&&` (${Math.round(x.failed/x.total*100)}%)`]})]}),x.failList&&x.failList.length>0&&t.jsxs(t.Fragment,{children:[t.jsx(Re,{type:"secondary",style:{fontSize:"12px",marginTop:"4px"},children:"失败文件列表:"}),t.jsx(Yn,{children:x.failList.map((g,P)=>t.jsx("li",{children:g},P))})]})]}),x.note&&t.jsxs(ce,{children:[t.jsx("span",{className:"label",children:"备注:"}),t.jsx("span",{className:"value",style:{color:x.note.includes("未收到")||x.note.includes("状态不确定")?"var(--color-warning-6)":"var(--color-text-2)"},children:x.note})]})]})})},b=()=>t.jsxs(Re,{children:["共",e.length,"个版本"]}),R=()=>e.map(x=>t.jsxs(te,{value:x.Version,children:[x.Version,t.jsx(xt,{createdAt:x.CreatedAt})]},x.Version));return t.jsxs("div",{style:{padding:"24px"},children:[t.jsx(Je,{heading:4,children:"客户端发布"}),t.jsx(Re,{type:"secondary",style:{marginBottom:"24px",display:"block"},children:"选择要发布的客户端版本和目标环境"}),t.jsx(Oe,{gutter:[24,24],children:t.jsx(ge,{span:24,children:t.jsx(Kn,{children:p?t.jsx(er,{loading:!0,text:{rows:3},animation:!0}):j?t.jsxs(Gn,{children:[t.jsx(Je,{heading:6,children:"获取版本列表失败"}),t.jsx(Re,{type:"secondary",children:j})]}):e.length===0?t.jsx(Me,{description:"没有找到可用的版本"}):t.jsxs(t.Fragment,{children:[t.jsxs(Oe,{gutter:[24,0],children:[t.jsx(ge,{span:12,children:t.jsxs(Wr,{children:[t.jsx("label",{className:"label",children:"环境选择"}),t.jsxs(ne,{value:s,onChange:n,disabled:l,style:{width:"100%"},children:[t.jsx(te,{value:"staging2",children:"staging2"}),t.jsx(te,{value:"app",children:"app"}),t.jsx(te,{value:"pisces",children:"pisces"}),t.jsx(te,{value:"leo",children:"leo"}),t.jsx(te,{value:"aries",children:"aries"}),t.jsx(te,{value:"virgo",children:"virgo"})]}),t.jsx("div",{className:"note",children:"选择要部署的目标环境"})]})}),t.jsx(ge,{span:12,children:t.jsxs(Wr,{children:[t.jsx("label",{className:"label",children:"版本选择"}),t.jsx(ne,{value:a,onChange:o,disabled:l,style:{width:"100%"},showSearch:!0,children:R()}),t.jsxs("div",{className:"note",children:["获取最近的版本，按创建时间倒序排列 ",b()]})]})})]}),t.jsx(Oe,{justify:"end",children:t.jsx(ge,{children:t.jsxs(B,{children:[l&&t.jsx(W,{type:"secondary",onClick:v,status:"warning",children:"取消上传"}),t.jsx(W,{type:"primary",loading:l,disabled:!a,onClick:C,children:l?"上传中...":"开始上传"})]})})})]})})})}),i&&t.jsxs(t.Fragment,{children:[t.jsx(mt,{status:i}),$()]}),t.jsx(Oe,{gutter:[16,16],style:{marginTop:"24px"},children:t.jsx(ge,{span:24,children:t.jsx(yt,{apiType:"desktop"})})})]})},{Row:me,Col:se}=Ue,{Title:Xn,Text:Zn}=ae,{Option:xe}=ne,Qn=k(ke)`
  width: 100%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  }
`,Ie=k.div`
  margin-bottom: 24px;
  
  .label {
    font-weight: 500;
    margin-bottom: 8px;
    display: block;
    color: var(--color-text-1);
    font-size: 14px;
  }
  
  .note {
    font-size: 12px;
    color: var(--color-text-3);
    margin-top: 4px;
  }
`;k(Qe)`
  margin: 24px 0;
`;const ea=k(W)`
  color: ${e=>e.type==="text"&&e.children&&e.children.toString().startsWith("+")?"rgb(22, 93, 255)":"var(--color-text-2)"};
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    color: rgb(22, 93, 255);
  }
`,ra=k(be)`
  margin-left: 8px;
`,ta=k.div`
  padding: 16px;
  background-color: var(--color-fill-2);
  border-radius: 4px;
  margin-bottom: 16px;
  color: var(--color-text-2);
  border-left: 4px solid var(--color-danger-light-4);
`,sa=()=>{const[e,r]=d.useState([]),[s,n]=d.useState([]),[a,o]=d.useState(""),[i,c]=d.useState("staging"),[l,u]=d.useState(""),[p,h]=d.useState(""),[j,A]=d.useState(""),[S,f]=d.useState(""),[y,m]=d.useState(!1),[T,w]=d.useState(null),[v,C]=d.useState(!1),[$,b]=d.useState(!0),[R,x]=d.useState(!1),[g,P]=d.useState(null),[H,X]=d.useState(null),oe=d.useRef(null),{getAccessToken:He}=fe(),{addNotification:Z}=xr(),J=(O,L)=>{};d.useEffect(()=>{yr()},[]),d.useEffect(()=>{a&&wr(a)},[a]);const yr=async()=>{b(!0),P(null);try{const O=He();J("获取Lambda服务列表");const L=await Qs();J("获取到服务列表",L),r(L),L.length>0?(o(L[0].name),J("已设置默认服务",L[0].name)):(J("没有找到任何服务"),P("没有找到任何服务"),Z({title:"获取服务失败",message:"没有找到任何可用的Lambda服务",type:"error"}))}catch(O){const L=`获取服务列表失败: ${O.message}`;P(L),M.error(L),Z({title:"获取服务失败",message:L,type:"error"}),w({message:L,progress:0,type:"error"})}finally{b(!1)}},wr=async O=>{x(!0),X(null);try{const L=He();J(`获取服务 ${O} 的版本列表`);const U=await en(O);J("获取到版本数据",U),n(U),U.length>0?(u(U[0].Version),J("已设置默认版本",U[0].Version)):(J("没有找到任何版本信息"),X(`没有找到服务 ${O} 的任何版本`),Z({title:"获取版本失败",message:`没有找到服务 ${O} 的任何版本`,type:"warning"}))}catch(L){const U=`获取版本列表失败: ${L.message}`;X(U),M.error(U),Z({title:"获取版本失败",message:U,type:"error"}),w({message:U,progress:0,type:"error"})}finally{x(!1)}},wt=()=>{oe.current&&(oe.current.abort(),oe.current=null,M.warning("部署已取消"),w({message:"部署已取消",progress:0,type:"warning"}),C(!1),Z({title:"部署已取消",message:`${a} 的部署操作已取消`,type:"warning"}))},bt=async()=>{if(!a||!l||!i){M.error("请选择服务、版本和环境");return}C(!0),M.info("开始部署..."),w({message:"开始部署...",progress:0,type:"info"}),oe.current=new AbortController;const O={serviceType:a,version:l,env:i,...S&&{ossObjectKey:S}};try{const L=He();await sn(O,oe.current.signal,Q=>{w({message:Q.message,progress:Q.progress,type:Q.type||"info"}),Q.type==="error"&&M.error(Q.message)},(Q,Ct,Tt)=>{Z({title:Q,message:Ct,type:Tt})})?(M.success("部署完成！"),w({message:"部署完成！",progress:100,type:"success"})):(M.warning("部署可能未完全成功，请检查详细日志"),w({message:"部署可能未完全成功，请检查详细日志",progress:100,type:"warning"}),Z({title:"部署部分成功",message:`${a} 的部署可能未完全成功，请检查详细日志`,type:"warning"}))}catch(L){if(L.name==="AbortError")return;const U="部署失败: "+L.message;M.error(U),w({message:U,progress:0,type:"error"})}finally{C(!1),oe.current=null}},vt=()=>t.jsxs(B,{children:[t.jsx(be,{count:e.length,text:`共${e.length}个服务`,dot:!1}),!R&&s.length>0&&t.jsx(be,{count:s.length,text:`${a}有${s.length}个版本`,dot:!1})]}),jt=()=>{m(!y)},St=()=>s.length===0?t.jsx(xe,{value:"",disabled:!0,children:"没有可用的版本"}):s.map((O,L)=>t.jsxs(xe,{value:O.Version,children:[O.Version,L<3&&t.jsx(ra,{count:"NEW",color:"#165DFF"}),t.jsx(xt,{createdAt:O.CreatedAt})]},O.Version)),kt=()=>{if(!a)return[];switch(a){case"ngiq-lambda":return["surf2surf","vertex-fc","vertex-fc-ptc","surfparcel-fc","surfparcel-fc-ptc","dicom-anony","archive-files","ptc-surf2surf","ng2bids"];case"target-report-word-export":return["target-report-word-export"];case"plan-report-pdf-export":return["plan-report-pdf-export-v2"];default:return[]}};return t.jsxs("div",{style:{width:"100%"},children:[t.jsxs(Qn,{title:t.jsx(Xn,{heading:5,style:{margin:0},children:"Lambda函数部署"}),extra:vt(),bordered:!1,hoverable:!0,children:[g&&t.jsxs(ta,{children:[t.jsx("div",{style:{marginBottom:8,fontWeight:500},children:"错误"}),t.jsx("div",{children:g}),t.jsx(W,{type:"text",style:{marginTop:8,padding:0},onClick:yr,children:"重试"})]}),t.jsxs(me,{gutter:24,children:[t.jsx(se,{span:8,children:t.jsxs(Ie,{children:[t.jsx("span",{className:"label",children:"服务选择"}),$?t.jsx(er,{text:{rows:1,width:"100%"},animation:!0}):e.length===0&&!g?t.jsx(Me,{description:"没有找到任何服务",style:{margin:"20px 0"}}):t.jsx(ne,{style:{width:"100%"},value:a,onChange:o,disabled:v,size:"large",placeholder:"请选择服务",loading:$,showSearch:!0,filterOption:(O,L)=>L.props.children.toLowerCase().indexOf(O.toLowerCase())>=0,children:e.map(O=>t.jsx(xe,{value:O.name,children:O.name},O.name))})]})}),t.jsx(se,{span:8,children:t.jsxs(Ie,{children:[t.jsx("span",{className:"label",children:"环境选择"}),t.jsxs(ne,{style:{width:"100%"},value:i,onChange:c,disabled:v,size:"large",placeholder:"请选择环境",children:[t.jsx(xe,{value:"staging",children:"staging"}),t.jsx(xe,{value:"app",children:"app"})]})]})}),t.jsx(se,{span:8,children:t.jsxs(Ie,{children:[t.jsx("span",{className:"label",children:"版本选择"}),R?t.jsx(er,{text:{rows:1,width:"100%"},animation:!0}):s.length===0&&!H?t.jsx(Me,{description:"没有找到任何版本",style:{margin:"20px 0"}}):H?t.jsxs("div",{style:{color:"var(--color-danger-6)"},children:[H,t.jsx(W,{type:"text",style:{marginLeft:8,padding:0},onClick:()=>wr(a),children:"重试"})]}):t.jsx(ne,{style:{width:"100%"},value:l,onChange:u,disabled:v,size:"large",placeholder:"请选择版本",loading:R,showSearch:!0,filterOption:(O,L)=>L.props.value.toLowerCase().indexOf(O.toLowerCase())>=0,children:St()}),t.jsx("div",{className:"note",children:"获取最近的版本，按创建时间倒序排序"})]})})]}),t.jsx(me,{children:t.jsx(se,{span:24,children:t.jsx(ea,{type:"text",onClick:jt,disabled:v,style:{marginLeft:"auto"},children:y?"- 取消部署到函数计算":"+ 部署到函数计算 (自动部署所有函数)"})})}),y&&t.jsxs("div",{style:{marginTop:"16px"},children:[t.jsx(me,{gutter:24,children:t.jsx(se,{span:24,children:t.jsxs("div",{style:{backgroundColor:"var(--color-fill-2)",padding:"16px",borderRadius:"4px",marginBottom:"16px"},children:[t.jsxs(Zn,{style:{fontWeight:"bold"},children:["将部署以下",i,"环境的函数："]}),t.jsx("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fill, minmax(180px, 1fr))",gap:"8px",marginTop:"8px"},children:kt().map(O=>t.jsx("div",{style:{backgroundColor:"var(--color-fill-3)",padding:"6px 12px",borderRadius:"4px",fontSize:"13px"},children:O},O))}),t.jsxs("div",{style:{marginTop:"8px",fontSize:"12px",color:"var(--color-text-3)"},children:["注意：将自动部署",a,"服务在",i,"环境的所有函数"]})]})})}),t.jsx(me,{gutter:24,children:t.jsx(se,{span:24,children:t.jsxs(Ie,{children:[t.jsx("span",{className:"label",children:"自定义OSS路径 (可选)"}),t.jsx(Ke,{placeholder:"可选，留空则使用默认OSS路径",value:S,onChange:O=>f(O),disabled:v,size:"large"}),t.jsx("span",{className:"note",children:"如果指定自定义OSS路径，将覆盖默认设置"})]})})}),t.jsxs("div",{style:{display:"none"},children:[t.jsx(Ke,{value:p,onChange:O=>h(O)}),t.jsx(Ke,{value:j,onChange:O=>A(O)})]})]}),t.jsx(me,{children:t.jsx(se,{span:24,style:{textAlign:"right",marginTop:24},children:t.jsxs(B,{size:"large",children:[t.jsx(W,{type:"primary",onClick:bt,loading:v,disabled:v||!l||!a,size:"large",style:{backgroundColor:"rgb(22, 93, 255)",padding:"0 24px",fontWeight:500},children:"开始部署"}),t.jsx(W,{onClick:wt,disabled:!v,size:"large",style:{fontWeight:500},children:"取消部署"})]})})})]}),t.jsx(mt,{status:T}),t.jsx(yt,{})]})},{Title:na,Text:aa}=ae,Xe=k.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-bg-1);
`,oa=k(ke)`
  width: 400px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  border-radius: 8px;
  text-align: center;
`,ia=k.div`
  margin-bottom: 24px;
  
  img {
    height: 64px;
  }
`,ca=k(W)`
  width: 100%;
  height: 42px;
  font-size: 16px;
  margin-top: 24px;
`,la=()=>{const[e,r]=d.useState(!1),[s,n]=d.useState(null),[a,o]=d.useState(null);d.useEffect(()=>{i()},[]);const i=async()=>{r(!0),n(null);try{const l=await qs();o(l)}catch(l){const u=`获取认证信息失败: ${l.message}`;n(u)}finally{r(!1)}},c=()=>{a&&(window.location.href=a)};return e?t.jsx(Xe,{children:t.jsx(le,{size:36,tip:"加载中..."})}):s?t.jsx(Xe,{children:t.jsx(Zr,{status:"error",title:"认证服务暂时不可用",subTitle:s,extra:t.jsx(W,{type:"primary",onClick:i,children:"重试"})})}):t.jsx(Xe,{children:t.jsxs(oa,{children:[t.jsx(ia,{children:t.jsx("img",{src:"/logo.jpg",alt:"Logo"})}),t.jsx(na,{heading:3,children:"NGiQ 发布工具平台"}),t.jsx(aa,{type:"secondary",children:"使用单点登录访问系统"}),t.jsx(B,{direction:"vertical",size:"large",style:{width:"100%"},children:t.jsx(ca,{type:"primary",onClick:c,disabled:!a,children:"飞书账号登录"})})]})})},_e=k.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-bg-1);
  text-align: center;
  padding: 20px;
`,da=k.div`
  margin-top: 20px;
  padding: 16px;
  background-color: rgba(var(--red-2), 0.1);
  border-radius: 4px;
  max-width: 600px;
  text-align: left;
  font-size: 14px;
  color: var(--color-text-2);
  white-space: pre-wrap;
  word-break: break-word;
`,ua=()=>{const e=Kr(),r=Hr(),{login:s,isAuthenticated:n,isLoginInProgress:a}=fe(),[o,i]=d.useState(!0),[c,l]=d.useState(null),[u,p]=d.useState(null),[h,j]=d.useState(0),[A,S]=d.useState(!1),[f,y]=d.useState(!1),m=d.useRef(!1),T=d.useRef(0),w=d.useRef(!1),v=d.useRef(0),C=2,$=3e4,b=d.useCallback(()=>{console.log("尝试导航到主页...");try{v.current+=1,console.log("重定向前状态:",{isAuthenticated:n,authSuccessful:f,navigationAttempt:v.current}),v.current===1?(console.log("使用react-router导航"),r("/"),setTimeout(()=>{window.location.pathname.includes("/callback")&&(console.warn("react-router导航未生效，使用window.location.replace"),window.location.replace("/"))},300)):(console.warn(`第${v.current}次尝试，使用window.location.href`),window.location.href="/")}catch(g){console.error("导航失败:",g),console.warn("导航出错，使用window.location.href强制跳转"),window.location.href="/"}},[r]);d.useEffect(()=>{(f||n)&&!w.current&&(console.log("检测到认证成功状态，准备重定向"),w.current=!0,b())},[f,n,b]),d.useEffect(()=>{let g;return o&&T.current>0&&!f&&!n&&(g=window.setTimeout(()=>{Date.now()-T.current>$&&o&&(console.warn("认证请求超时"),i(!1),l("认证请求超时，服务器响应时间过长"),m.current=!1)},$)),()=>{g&&clearTimeout(g)}},[o,f,n]);const R=d.useCallback(()=>{const g=new URLSearchParams(e.search),P=g.get("code"),H=g.get("state"),X=g.get("session_state");return console.log("授权回调参数:",{codeExists:!!P,codeLength:(P==null?void 0:P.length)||0,state:H,sessionState:(X==null?void 0:X.substring(0,8))+"..."}),P},[e.search]),x=d.useCallback(async()=>{if(m.current||f||a||w.current){console.log("跳过认证处理: 已在处理中或已完成",{isProcessingAuth:m.current,authSuccessful:f,isLoginInProgress:a,authCompleted:w.current}),(f||n)&&!w.current&&(console.log("检测到认证成功但未完成跳转，强制导航"),w.current=!0,b());return}T.current=Date.now(),m.current=!0;try{i(!0),l(null),p(null);const g=R();if(!g)throw new Error("未找到授权码");console.log("开始处理认证...");const P=await Ys(g);if(!P||!P.token||!P.user)throw new Error("服务器返回的认证信息不完整");console.log("令牌交换成功，保存用户信息"),M.success({content:`欢迎回来，${P.user.name||P.user.preferred_username}`,duration:2e3}),s(P.token,P.user),y(!0)}catch(g){console.error("认证失败:",g);const P=`认证失败: ${g.message}`;if(l(P),g.message.includes("认证已处理")||g.message.includes("认证请求正在进行中")||g.message.includes("正在进行中")){console.log("检测到认证已在处理中，等待完成..."),y(!0),M.info({content:"认证请求正在处理中，请稍候...",duration:2e3});return}p(g.stack||g.toString()),h<C&&!f&&(g.message.includes("网络连接错误")||g.message.includes("服务器错误")||g.message.includes("服务器内部错误")||g.message.includes("timeout"))?(S(!0),j(H=>H+1),setTimeout(()=>{S(!1),m.current=!1,x()},2e3)):(m.current=!1,w.current=!1)}finally{A||(i(!1),!f&&!n&&(m.current=!1))}},[e.search,s,h,A,f,n,a,b,R]);return d.useEffect(()=>{w.current||f||n||a?(console.log("认证状态已确认，跳过回调处理",{authCompleted:w.current,authSuccessful:f,isAuthenticated:n,isLoginInProgress:a}),(n||f)&&!w.current&&(console.log("已认证但未完成跳转，准备导航"),w.current=!0,b())):x()},[]),f||n?t.jsx(_e,{children:t.jsx(le,{size:36,tip:"认证成功，正在跳转..."})}):o?t.jsxs(_e,{children:[t.jsx(le,{size:36,tip:A?`认证请求失败，正在重试 (${h}/${C})...`:"正在验证您的身份，请稍候..."}),c&&t.jsx("p",{style:{color:"var(--color-text-3)",marginTop:16},children:c})]}):c?t.jsxs(_e,{children:[t.jsx(Zr,{status:"error",title:"认证失败",subTitle:c,extra:t.jsxs(t.Fragment,{children:[t.jsx(W,{type:"primary",onClick:()=>r("/login"),children:"返回登录"}),t.jsx(W,{style:{marginLeft:16},onClick:()=>{j(0),m.current=!1,w.current=!1,x()},disabled:A||m.current,children:"重试"})]})}),u&&t.jsxs(da,{children:[t.jsx("strong",{children:"错误详情（用于技术支持）:"}),t.jsx("pre",{children:u}),t.jsx("p",{children:"如果问题持续存在，请联系系统管理员并提供上述错误信息。"}),t.jsx("p",{children:"可能的解决方法:"}),t.jsxs("ul",{children:[t.jsx("li",{children:"检查网络连接是否稳定"}),t.jsx("li",{children:"确认SSO服务是否可用"}),t.jsx("li",{children:"清除浏览器缓存后重试"}),t.jsx("li",{children:"使用其他浏览器尝试登录"})]})]})]}):t.jsx(_e,{children:t.jsx(le,{size:36,tip:"正在验证您的身份，请稍候..."})})},pa=k.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: var(--color-bg-1);
`,fa=()=>{const{isAuthenticated:e,isLoading:r}=fe();return r?t.jsx(pa,{children:t.jsx(le,{size:36,tip:"加载中..."})}):e?t.jsx(Gr,{}):t.jsx(qr,{to:"/login"})},ha="/",ga=()=>{const{getAccessToken:e}=fe();return K.useEffect(()=>{Gs(()=>e())},[e]),t.jsx(Et,{basename:ha,children:t.jsxs(At,{children:[t.jsx(ee,{path:"/login",element:t.jsx(la,{})}),t.jsx(ee,{path:"/callback",element:t.jsx(ua,{})}),t.jsx(ee,{element:t.jsx(fa,{}),children:t.jsxs(ee,{path:"/",element:t.jsx(Mn,{}),children:[t.jsx(ee,{index:!0,element:t.jsx(qr,{to:"/desktop",replace:!0})}),t.jsx(ee,{path:"desktop",element:t.jsx(Jn,{})}),t.jsx(ee,{path:"lambda",element:t.jsx(sa,{})})]})})]})})},ma=()=>t.jsx(an,{children:t.jsx(wn,{children:t.jsx(ga,{})})});rr.createRoot(document.getElementById("root")).render(t.jsx(K.StrictMode,{children:t.jsx(_t,{locale:Pt,children:t.jsx(ma,{})})}));
