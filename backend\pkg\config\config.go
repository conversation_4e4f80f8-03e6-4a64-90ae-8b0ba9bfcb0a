package config

import (
	"os"

	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v2"
)

// Config 是配置文件的结构体
type Config struct {
	DesktopConfig Desktop `yaml:"desktop"`
	LambdaConfig  Lambda  `yaml:"lambda"`
	GitLabConfig  GitLab  `yaml:"gitlab"`
	LoggingConfig Logging `yaml:"logging"` // 日志配置
	AuthConfig    Auth    `yaml:"auth"`    // 新增：认证配置
	FeishuConfig  Feishu  `yaml:"feishu"`  // 新增：飞书配置
}

// Logging 是日志配置
type Logging struct {
	Level      string `yaml:"level"`       // 日志级别：debug, info, warn, error, fatal
	OutputFile bool   `yaml:"output_file"` // 是否输出到文件
	FilePath   string `yaml:"file_path"`   // 日志文件路径
	Format     string `yaml:"format"`      // 日志格式：text, json
}

// Auth 是SSO认证配置
type Auth struct {
	Enabled               bool   `yaml:"enabled"`                  // 是否启用认证
	SsoURL                string `yaml:"sso_url"`                  // Keycloak服务地址
	Realm                 string `yaml:"realm"`                    // Keycloak Realm
	ClientID              string `yaml:"client_id"`                // Keycloak Client ID
	ClientSecret          string `yaml:"client_secret"`            // Keycloak Client Secret
	AuthEndpoint          string `yaml:"auth_endpoint"`            // 认证端点
	TokenEndpoint         string `yaml:"token_endpoint"`           // Token端点
	UserinfoEndpoint      string `yaml:"userinfo_endpoint"`        // 用户信息端点
	LogoutEndpoint        string `yaml:"logout_endpoint"`          // 登出端点
	RedirectURI           string `yaml:"redirect_uri"`             // 登录回调地址
	PostLogoutRedirectURI string `yaml:"post_logout_redirect_uri"` // 登出后重定向地址
}

// Feishu 是飞书webhook配置
type Feishu struct {
	Enabled      bool          `yaml:"enabled"`      // 是否启用飞书通知
	WebhookURL   string        `yaml:"webhook_url"`  // 飞书webhook地址
	Notification Notifications `yaml:"notification"` // 通知配置
}

// Notifications 是通知配置
type Notifications struct {
	Login         NotificationConfig `yaml:"login"`          // 登录通知
	DesktopUpload NotificationConfig `yaml:"desktop_upload"` // 桌面客户端上传通知
	LambdaDeploy  NotificationConfig `yaml:"lambda_deploy"`  // Lambda部署通知
}

// NotificationConfig 是单个通知的配置
type NotificationConfig struct {
	Enabled  bool   `yaml:"enabled"`  // 是否启用该通知
	Template string `yaml:"template"` // 通知模板
}

// Desktop 是桌面客户端相关配置
type Desktop struct {
	GitLabConfig GitLab `yaml:"gitlab"`
	OssConfig    Oss    `yaml:"oss"`
}

// Lambda 是Lambda部署相关配置
type Lambda struct {
	GitLabConfig   GitLab                  `yaml:"gitlab"`
	OssConfig      Oss                     `yaml:"oss"`
	FCConfig       FC                      `yaml:"fc"`
	Services       []LambdaService         `yaml:"services"`        // 支持的Lambda服务列表
	FunctionConfig []ServiceFunctionConfig `yaml:"function_config"` // 新增：函数配置列表
}

// LambdaService 定义Lambda服务配置
type LambdaService struct {
	Name        string `yaml:"name"`          // 服务名称
	ProjectName string `yaml:"project_name"`  // GitLab项目名称
	PackageName string `yaml:"package_name"`  // GitLab包名称
	ProjectID   int    `yaml:"project_id"`    // GitLab项目ID
	OssBasePath string `yaml:"oss_base_path"` // OSS基础路径
}

// GitLab 是GitLab的配置
type GitLab struct {
	ProjectName string `yaml:"project_name"`
	PackageName string `yaml:"package_name"`
	ProjectId   int    `yaml:"project_id"`
	BaseUrl     string `yaml:"base_url"`
}

// Oss 是OSS的配置
type Oss struct {
	BucketName string `yaml:"bucket_name"`
	Endpoint   string `yaml:"endpoint"`
}

// FC 是函数计算的配置
type FC struct {
	Endpoint string `yaml:"endpoint"`
}

// ServiceFunctionConfig 定义服务及其函数的配置
type ServiceFunctionConfig struct {
	ServiceType string           `yaml:"service_type"` // 服务类型
	Functions   []FunctionConfig `yaml:"functions"`    // 函数配置列表
}

// FunctionConfig 定义单个函数的配置信息
type FunctionConfig struct {
	Name                 string `yaml:"name"`                   // 函数基础名称
	ServiceNameTemplate  string `yaml:"service_name_template"`  // 服务名模板
	FunctionNameTemplate string `yaml:"function_name_template"` // 函数名模板
	OssPathTemplate      string `yaml:"oss_path_template"`      // OSS路径模板
}

// FCDeploymentInfo 包含FC部署所需的所有信息
type FCDeploymentInfo struct {
	ServiceName   string // 完整的服务名
	FunctionName  string // 完整的函数名
	OSSObjectPath string // OSS对象路径
}

// LoadConfig 加载配置文件
func (c *Config) LoadConfig(path string) error {
	// 读取配置文件
	yamlFile, err := os.ReadFile(path)
	if err != nil {
		return err
	}
	// 解析配置文件
	err = yaml.Unmarshal(yamlFile, &c)
	if err != nil {
		return err
	}
	logrus.Info("Config loaded successfully")
	return nil
}
