@keyframes ~'@{prefix}-carousel-slide-x-in' {
  from {
    transform: translateX(100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-x-out' {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(-100%);
  }
}

@keyframes ~'@{prefix}-carousel-slide-x-in-reverse' {
  from {
    transform: translateX(-100%);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-x-out-reverse' {
  from {
    transform: translateX(0);
  }

  to {
    transform: translateX(100%);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-in' {
  from {
    transform: translateY(100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-out' {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(-100%);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-in-reverse' {
  from {
    transform: translateY(-100%);
  }

  to {
    transform: translateY(0);
  }
}

@keyframes ~'@{prefix}-carousel-slide-y-out-reverse' {
  from {
    transform: translateY(0);
  }

  to {
    transform: translateY(100%);
  }
}

// 卡片翻页动画

@keyframes ~'@{prefix}-carousel-card-bottom-to-middle' {
  from {
    opacity: 0;
    transform: translateX(0%) translateZ(-400px);
  }

  to {
    opacity: 0.4;
    transform: translateX(0%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-bottom' {
  from {
    opacity: 0.4;
    transform: translateX(-100%) translateZ(-200px);
  }

  to {
    opacity: 0;
    transform: translateX(-100%) translateZ(-400px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-bottom-rtl' {
  from {
    opacity: 0.4;
    transform: translateX(100%) translateZ(-200px);
  }

  to {
    opacity: 0;
    transform: translateX(100%) translateZ(-400px);
  }
}

@keyframes ~'@{prefix}-carousel-card-top-to-middle' {
  from {
    opacity: 1;
    transform: translateX(-50%) translateZ(0);
  }

  to {
    opacity: 0.4;
    transform: translateX(-100%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-top-to-middle-rtl' {
  from {
    opacity: 1;
    transform: translateX(50%) translateZ(0);
  }

  to {
    opacity: 0.4;
    transform: translateX(100%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-top' {
  from {
    opacity: 0.4;
    transform: translateX(0) translateZ(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateZ(0);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-top-rtl' {
  from {
    opacity: 0.4;
    transform: translateX(0) translateZ(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(50%) translateZ(0);
  }
}

@keyframes ~'@{prefix}-carousel-card-bottom-to-middle-reverse' {
  from {
    opacity: 0;
    transform: translateX(-100%) translateZ(-400px);
  }

  to {
    opacity: 0.4;
    transform: translateX(-100%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-bottom-to-middle-reverse-rtl' {
  from {
    opacity: 0;
    transform: translateX(100%) translateZ(-400px);
  }

  to {
    opacity: 0.4;
    transform: translateX(100%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-bottom-reverse' {
  from {
    opacity: 0.4;
    transform: translateX(0%) translateZ(-200px);
  }

  to {
    opacity: 0;
    transform: translateX(0%) translateZ(-400px);
  }
}

@keyframes ~'@{prefix}-carousel-card-top-to-middle-reverse' {
  from {
    opacity: 1;
    transform: translateX(-50%) translateZ(0);
  }

  to {
    opacity: 0.4;
    transform: translateX(0%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-top-to-middle-reverse-rtl' {
  from {
    opacity: 1;
    transform: translateX(50%) translateZ(0);
  }

  to {
    opacity: 0.4;
    transform: translateX(0%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-top-reverse' {
  from {
    opacity: 0.4;
    transform: translateX(-100%) translateZ(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateZ(0);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-top-reverse-rtl' {
  from {
    opacity: 0.4;
    transform: translateX(100%) translateZ(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(50%) translateZ(0);
  }
}

@keyframes ~'@{prefix}-carousel-card-right-to-middle' {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(0%) translateZ(-400px);
  }

  to {
    opacity: 0.4;
    transform: translateX(-50%) translateY(0%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-right' {
  from {
    opacity: 0.4;
    transform: translateX(-50%) translateY(-100%) translateZ(-200px);
  }

  to {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) translateZ(-400px);
  }
}

@keyframes ~'@{prefix}-carousel-card-left-to-middle' {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(-50%) translateZ(0);
  }

  to {
    opacity: 0.4;
    transform: translateX(-50%) translateY(-100%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-left' {
  from {
    opacity: 0.4;
    transform: translateX(-50%) translateY(0) translateZ(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-50%) translateZ(0);
  }
}

@keyframes ~'@{prefix}-carousel-card-right-to-middle-reverse' {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) translateZ(-400px);
  }

  to {
    opacity: 0.4;
    transform: translateX(-50%) translateY(-100%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-right-reverse' {
  from {
    opacity: 0.4;
    transform: translateX(-50%) translateY(0%) translateZ(-200px);
  }

  to {
    opacity: 0;
    transform: translateX(-50%) translateY(0%) translateZ(-400px);
  }
}

@keyframes ~'@{prefix}-carousel-card-left-to-middle-reverse' {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(-50%) translateZ(0);
  }

  to {
    opacity: 0.4;
    transform: translateX(-50%) translateY(0%) translateZ(-200px);
  }
}

@keyframes ~'@{prefix}-carousel-card-middle-to-left-reverse' {
  from {
    opacity: 0.4;
    transform: translateX(-50%) translateY(-100%) translateZ(-200px);
  }

  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-50%) translateZ(0);
  }
}
