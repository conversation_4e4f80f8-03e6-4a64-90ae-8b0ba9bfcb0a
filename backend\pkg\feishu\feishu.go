package feishu

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"sync"
	"time"

	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/auth"
	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/config"
	"github.com/sirupsen/logrus"
)

// notificationRecord 定义通知记录
type notificationRecord struct {
	userID     string    // 用户ID
	notifyType string    // 通知类型，如login, desktop_upload, lambda_deploy
	timestamp  time.Time // 通知发送时间
}

// 通知缓存，用于存储最近发送的通知记录
type notificationCache struct {
	records map[string]notificationRecord // 通知记录映射
	mutex   sync.RWMutex                  // 用于保证并发安全的锁
}

// 生成通知记录的键
func generateNotificationKey(userID, notifyType string) string {
	return fmt.Sprintf("%s:%s", userID, notifyType)
}

// FeishuService 处理飞书Webhook通知的服务
type FeishuService struct {
	Config   config.Feishu
	cache    notificationCache // 通知缓存
	cacheTTL time.Duration     // 缓存过期时间
}

// FeishuMessage 飞书消息结构体
type FeishuMessage struct {
	MsgType string                 `json:"msg_type"`
	Content map[string]interface{} `json:"content,omitempty"`
	Card    map[string]interface{} `json:"card,omitempty"`
}

// NewFeishuService 创建飞书服务实例
func NewFeishuService(config config.Feishu) *FeishuService {
	service := &FeishuService{
		Config:   config,
		cacheTTL: 10 * time.Minute, // 增加到10分钟的去重时间
		cache: notificationCache{
			records: make(map[string]notificationRecord),
		},
	}

	// 启动清理过期缓存的后台任务
	go service.cleanupCache()

	// 日志记录服务启动信息
	logrus.WithFields(logrus.Fields{
		"enabled":   config.Enabled,
		"cache_ttl": service.cacheTTL.String(),
	}).Debug("飞书通知服务已初始化")

	return service
}

// cleanupCache 定期清理过期的通知缓存
func (s *FeishuService) cleanupCache() {
	ticker := time.NewTicker(s.cacheTTL / 2) // 每过半个TTL清理一次
	defer ticker.Stop()

	for range ticker.C {
		s.cache.mutex.Lock()

		// 当前时间
		now := time.Now()
		expiredKeys := []string{}

		// 查找过期的记录
		for key, record := range s.cache.records {
			if now.Sub(record.timestamp) > s.cacheTTL {
				expiredKeys = append(expiredKeys, key)
			}
		}

		// 删除过期记录
		for _, key := range expiredKeys {
			delete(s.cache.records, key)
		}

		s.cache.mutex.Unlock()

		if len(expiredKeys) > 0 {
			logrus.WithField("expiredCount", len(expiredKeys)).Debug("已清理过期的通知缓存")
		}
	}
}

// 检查是否是重复通知
func (s *FeishuService) isDuplicateNotification(userID, notifyType string) bool {
	s.cache.mutex.RLock()
	defer s.cache.mutex.RUnlock()

	// 生成缓存键
	key := generateNotificationKey(userID, notifyType)

	// 检查是否存在记录
	record, exists := s.cache.records[key]
	if !exists {
		return false
	}

	// 检查记录是否在TTL内
	if time.Since(record.timestamp) > s.cacheTTL {
		// 记录已过期
		return false
	}

	// 记录有效，检查用户ID、类型和时间戳
	logrus.WithFields(logrus.Fields{
		"user":       userID,
		"notifyType": notifyType,
		"lastTime":   record.timestamp.Format(time.RFC3339),
		"ttl":        s.cacheTTL.String(),
	}).Debug("找到通知缓存记录")

	return true
}

// 记录通知到缓存
func (s *FeishuService) recordNotification(userID, notifyType string) {
	s.cache.mutex.Lock()
	defer s.cache.mutex.Unlock()

	// 记录当前通知
	key := generateNotificationKey(userID, notifyType)
	s.cache.records[key] = notificationRecord{
		userID:     userID,
		notifyType: notifyType,
		timestamp:  time.Now(),
	}

	logrus.WithFields(logrus.Fields{
		"user":       userID,
		"notifyType": notifyType,
		"key":        key,
	}).Debug("记录通知到缓存")
}

// formatText 根据模板格式化文本内容
func (s *FeishuService) formatText(template string, params map[string]string) string {
	result := template
	for key, value := range params {
		placeholder := fmt.Sprintf("{%s}", key)
		result = string(bytes.Replace([]byte(result), []byte(placeholder), []byte(value), -1))
	}
	return result
}

// simplifyErrorReason 简化错误信息，提取关键信息
func simplifyErrorReason(reason string) string {
	// 常见错误模式匹配和简化
	if strings.Contains(reason, "FunctionNotFound") || strings.Contains(reason, "does not exist") {
		return "函数不存在"
	}
	if strings.Contains(reason, "InvalidParameterValue") {
		return "参数错误"
	}
	if strings.Contains(reason, "AccessDenied") || strings.Contains(reason, "Forbidden") {
		return "权限不足"
	}
	if strings.Contains(reason, "ServiceUnavailable") || strings.Contains(reason, "InternalError") {
		return "服务不可用"
	}
	if strings.Contains(reason, "Timeout") {
		return "请求超时"
	}
	if strings.Contains(reason, "QuotaExceeded") || strings.Contains(reason, "LimitExceeded") {
		return "配额超限"
	}

	// 如果没有匹配到常见模式，返回简化的错误信息
	// 提取StatusCode信息
	if strings.Contains(reason, "StatusCode:") {
		parts := strings.Split(reason, "StatusCode:")
		if len(parts) > 1 {
			statusPart := strings.TrimSpace(parts[1])
			if strings.HasPrefix(statusPart, "404") {
				return "函数不存在"
			}
			if strings.HasPrefix(statusPart, "403") {
				return "权限不足"
			}
			if strings.HasPrefix(statusPart, "500") {
				return "服务器错误"
			}
		}
	}

	// 默认返回通用错误信息
	return "部署失败"
}

// SendTextMessage 发送文本消息
func (s *FeishuService) SendTextMessage(text string) error {
	// 如果通知功能被禁用，直接返回
	if !s.Config.Enabled {
		logrus.Debug("飞书通知功能被禁用，跳过消息发送")
		return nil
	}

	// 准备消息内容
	message := FeishuMessage{
		MsgType: "text",
		Content: map[string]interface{}{
			"text": text,
		},
	}

	// 转换为JSON
	jsonData, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("消息序列化失败: %w", err)
	}

	// 发送请求
	req, err := http.NewRequest("POST", s.Config.WebhookURL, bytes.NewBuffer(jsonData))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{Timeout: 5 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态
	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("飞书API返回错误状态码: %s", resp.Status)
	}

	logrus.WithField("message", text).Debug("飞书消息发送成功")
	return nil
}

// NotifyLogin 发送用户登录通知
func (s *FeishuService) NotifyLogin(user *auth.UserInfo) error {
	// 检查是否启用登录通知
	if !s.Config.Notification.Login.Enabled {
		return nil
	}

	// 去重检查：如果同一用户近期已发送过登录通知，则跳过
	if s.isDuplicateNotification(user.Sub, "login") {
		logrus.WithFields(logrus.Fields{
			"user": user.PreferredUsername,
			"sub":  user.Sub,
			"ttl":  s.cacheTTL.String(),
		}).Info("近期内已发送过登录通知，跳过重复发送")
		return nil
	}

	// 先记录到缓存，确保即使发送失败也不会反复尝试
	s.recordNotification(user.Sub, "login")

	// 准备参数
	params := map[string]string{
		"username": user.PreferredUsername,
		"email":    user.Email,
		"name":     user.Name,
		"time":     time.Now().Format("2006-01-02 15:04:05"),
	}

	// 格式化消息
	messageText := s.formatText(s.Config.Notification.Login.Template, params)

	// 发送消息
	err := s.SendTextMessage(messageText)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"user":  user.PreferredUsername,
			"error": err.Error(),
		}).Error("发送登录通知失败")
		return err
	}

	logrus.WithField("user", user.PreferredUsername).Info("发送登录通知成功")
	return nil
}

// NotifyDesktopUpload 发送桌面客户端上传通知
func (s *FeishuService) NotifyDesktopUpload(user *auth.UserInfo, version, env string, summary map[string]interface{}) error {
	// 检查是否启用桌面客户端上传通知
	if !s.Config.Notification.DesktopUpload.Enabled {
		return nil
	}

	// 解析摘要信息
	totalFiles, _ := summary["total"].(int)
	successCount, _ := summary["success"].(int)
	failedCount, _ := summary["failed"].(int)
	successList, _ := summary["successList"].([]string)
	successDetails, _ := summary["successDetails"].(map[string][]string)
	failedList, _ := summary["failedList"].([]string)
	failedReasons, _ := summary["failedReasons"].(map[string]string)

	// 去重检查：基于用户、环境、版本。结果不同也应通知。
	notifyType := fmt.Sprintf("desktop_upload:%s:%s", env, version)
	if s.isDuplicateNotification(user.Sub, notifyType) {
		logrus.WithFields(logrus.Fields{
			"user":    user.PreferredUsername,
			"env":     env,
			"version": version,
			"ttl":     s.cacheTTL.String(),
		}).Info("近期内已发送过桌面客户端上传通知，跳过重复发送")
		return nil
	}

	// 先记录到缓存
	s.recordNotification(user.Sub, notifyType)

	// 准备基础参数
	params := map[string]string{
		"username": user.PreferredUsername,
		"name":     user.Name,
		"version":  version,
		"env":      env,
		"time":     time.Now().Format("2006-01-02 15:04:05"),
		"total":    strconv.Itoa(totalFiles),
		"success":  strconv.Itoa(successCount),
		"failed":   strconv.Itoa(failedCount),
	}

	// 根据结果构建消息文本
	var messageText string
	var template string

	if failedCount > 0 {
		// 有失败的情况
		template = "🖥️ 桌面客户端发布报告\n\n" +
			"👤 用户：{username}\n" +
			"📦 版本：{version}\n" +
			"🎯 环境：{env}\n" +
			"⏰ 时间：{time}\n\n" +
			"📊 发布结果：{total}个文件 (✅{success}个成功 ❌{failed}个失败){successDetails}{failedDetails}"
	} else {
		// 全部成功的情况
		template = "🖥️ 桌面客户端发布报告\n\n" +
			"👤 用户：{username}\n" +
			"📦 版本：{version}\n" +
			"🎯 环境：{env}\n" +
			"⏰ 时间：{time}\n\n" +
			"📊 发布结果：{total}个文件 (✅全部成功){successDetails}"
	}

	// 构建成功详情 - 使用表格格式
	if len(successList) > 0 {
		// 构建表格数据
		var tableRows []map[string]interface{}

		// 按平台分类文件
		platformFiles := map[string][]string{
			"Linux":   {},
			"Windows": {},
			"Mac":     {},
		}

		for _, fileName := range successList {
			switch {
			case strings.Contains(strings.ToLower(fileName), "linux") || strings.HasSuffix(strings.ToLower(fileName), ".appimage"):
				platformFiles["Linux"] = append(platformFiles["Linux"], fileName)
			case strings.Contains(strings.ToLower(fileName), "win") || strings.HasSuffix(strings.ToLower(fileName), ".exe") || strings.Contains(strings.ToLower(fileName), ".exe."):
				platformFiles["Windows"] = append(platformFiles["Windows"], fileName)
			case strings.Contains(strings.ToLower(fileName), "mac") || strings.HasSuffix(strings.ToLower(fileName), ".dmg") || strings.HasSuffix(strings.ToLower(fileName), ".zip") || strings.Contains(strings.ToLower(fileName), ".dmg.") || strings.Contains(strings.ToLower(fileName), ".zip."):
				platformFiles["Mac"] = append(platformFiles["Mac"], fileName)
			}
		}

		// 按顺序处理平台，构建表格数据
		platforms := []string{"Linux", "Windows", "Mac"}
		for _, platform := range platforms {
			files := platformFiles[platform]
			if len(files) > 0 {
				for _, fileName := range files {
					// 简化文件名显示（去掉版本号等冗余信息）
					displayFileName := fileName
					if strings.Contains(fileName, "latest-") {
						// 保持latest文件的原名
					} else {
						// 简化其他文件名
						if strings.Contains(fileName, "ngiq-p-cloud-desktop") {
							if strings.HasSuffix(fileName, ".AppImage") {
								displayFileName = "ngiq-p-cloud-desktop.AppImage"
							} else if strings.HasSuffix(fileName, ".exe") {
								displayFileName = "ngiq-p-cloud-desktop.exe"
							} else if strings.HasSuffix(fileName, ".exe.blockmap") {
								displayFileName = "ngiq-p-cloud-desktop.exe.blockmap"
							} else if strings.HasSuffix(fileName, ".dmg") {
								displayFileName = "ngiq-p-cloud-desktop.dmg"
							} else if strings.HasSuffix(fileName, ".dmg.blockmap") {
								displayFileName = "ngiq-p-cloud-desktop.dmg.blockmap"
							} else if strings.HasSuffix(fileName, ".zip") {
								displayFileName = "ngiq-p-cloud-desktop.zip"
							} else if strings.HasSuffix(fileName, ".zip.blockmap") {
								displayFileName = "ngiq-p-cloud-desktop.zip.blockmap"
							}
						}
					}

					// 获取完整的OSS路径
					var ossPath string
					if paths, exists := successDetails[fileName]; exists && len(paths) > 0 {
						// 使用第一个OSS路径（通常是latest路径）
						ossPath = paths[0]
						// 如果有多个路径，可以选择显示更有意义的那个
						if len(paths) > 1 {
							// 优先显示非latest的版本特定路径
							for _, path := range paths {
								if !strings.Contains(path, "latest") {
									ossPath = path
									break
								}
							}
						}
					} else {
						// 回退到基础路径（如果没有详细信息）
						ossPath = fmt.Sprintf("%s/ngiq-p-cloud-desktop/%s", env, strings.ToLower(platform))
					}

					// 添加到表格数据
					tableRows = append(tableRows, map[string]interface{}{
						"platform": platform,
						"filename": displayFileName,
						"osspath":  ossPath,
					})
				}
			}
		}

		// 暂时使用简化的成功详情，后续会用消息卡片替换
		params["successDetails"] = fmt.Sprintf("\n\n✅ 成功上传 %d 个文件", len(successList))
	} else {
		params["successDetails"] = ""
	}

	// 构建失败详情
	if len(failedList) > 0 {
		var failedDetailsList []string
		for _, fileName := range failedList {
			if reason, exists := failedReasons[fileName]; exists {
				failedDetailsList = append(failedDetailsList, fmt.Sprintf("%s (%s)", fileName, reason))
			} else {
				failedDetailsList = append(failedDetailsList, fileName)
			}
		}
		params["failedDetails"] = "\n\n❌ 失败文件：\n• " + strings.Join(failedDetailsList, "\n• ")
	} else {
		params["failedDetails"] = ""
	}

	// 格式化最终消息
	messageText = s.formatText(template, params)

	// 发送消息
	err := s.SendTextMessage(messageText)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"user":    user.PreferredUsername,
			"version": version,
			"env":     env,
			"error":   err.Error(),
		}).Error("发送桌面客户端上传通知失败")
		return err
	}

	logrus.WithFields(logrus.Fields{
		"user":    user.PreferredUsername,
		"version": version,
		"env":     env,
		"result":  fmt.Sprintf("%d/%d success, %d failed", successCount, totalFiles, failedCount),
	}).Info("发送桌面客户端上传通知成功")
	return nil
}

// NotifyLambdaDeploy 发送Lambda部署通知
func (s *FeishuService) NotifyLambdaDeploy(user *auth.UserInfo, service, version, env string, summary map[string]interface{}) error {
	// 检查是否启用Lambda部署通知
	if !s.Config.Notification.LambdaDeploy.Enabled {
		return nil
	}

	// 去重检查：构建特定的通知类型，包含服务、版本和环境信息，确保相同部署只通知一次
	notifyType := fmt.Sprintf("lambda_deploy:%s:%s:%s", service, version, env)
	if s.isDuplicateNotification(user.Sub, notifyType) {
		logrus.WithFields(logrus.Fields{
			"user":    user.PreferredUsername,
			"service": service,
			"version": version,
			"env":     env,
		}).Info("5分钟内已发送过相同服务、版本和环境的部署通知，跳过重复发送")
		return nil
	}

	// 解析摘要信息
	totalFunctions, _ := summary["total"].(int)
	successCount, _ := summary["success"].(int)
	failedCount, _ := summary["failed"].(int)
	successList, _ := summary["successList"].([]string)
	failedList, _ := summary["failedList"].([]string)
	failedReasons, _ := summary["failedReasons"].(map[string]string)
	cancelled, _ := summary["cancelled"].(bool)

	// 准备基础参数
	params := map[string]string{
		"username": user.PreferredUsername,
		"email":    user.Email,
		"service":  service,
		"version":  version,
		"env":      env,
		"time":     time.Now().Format("2006-01-02 15:04:05"),
		"total":    strconv.Itoa(totalFunctions),
		"success":  strconv.Itoa(successCount),
		"failed":   strconv.Itoa(failedCount),
	}

	// 根据结果构建消息文本
	var messageText string
	var template string

	if cancelled {
		// 部署被取消的情况
		template = "🚀 Lambda部署报告\n\n" +
			"👤 用户：{username}\n" +
			"📦 服务：{service} v{version}\n" +
			"🎯 环境：{env}\n" +
			"⏰ 时间：{time}\n\n" +
			"📊 部署结果：{total}个函数 (✅{success}个成功 ❌{failed}个失败 ⏸️已取消){successDetails}{failedDetails}"

		// 添加成功列表
		if len(successList) > 0 {
			params["successDetails"] = "\n\n✅ 成功部署：\n• " + strings.Join(successList, "\n• ")
		} else {
			params["successDetails"] = ""
		}

		// 添加失败列表，简化错误信息
		if len(failedList) > 0 {
			params["failedDetails"] = "\n\n❌ 失败部署：\n• " + strings.Join(failedList, "\n• ") +
				"\n💡 提示：部署已取消，剩余函数未部署"
		} else {
			params["failedDetails"] = ""
		}
	} else if failedCount > 0 {
		// 有失败的情况
		template = "🚀 Lambda部署报告\n\n" +
			"👤 用户：{username}\n" +
			"📦 服务：{service} v{version}\n" +
			"🎯 环境：{env}\n" +
			"⏰ 时间：{time}\n\n" +
			"📊 部署结果：{total}个函数 (✅{success}个成功 ❌{failed}个失败){successDetails}{failedDetails}"

		// 添加成功列表
		if len(successList) > 0 {
			params["successDetails"] = "\n\n✅ 成功部署：\n• " + strings.Join(successList, "\n• ")
		} else {
			params["successDetails"] = ""
		}

		// 添加失败列表，简化错误信息
		if len(failedList) > 0 {
			// 统计失败原因
			reasonCounts := make(map[string][]string)
			for _, funcName := range failedList {
				if reason, exists := failedReasons[funcName]; exists {
					// 简化错误信息
					simplifiedReason := simplifyErrorReason(reason)
					reasonCounts[simplifiedReason] = append(reasonCounts[simplifiedReason], funcName)
				} else {
					reasonCounts["未知错误"] = append(reasonCounts["未知错误"], funcName)
				}
			}

			failedDetails := "\n\n❌ 失败部署："
			for reason, funcs := range reasonCounts {
				failedDetails += "\n• " + strings.Join(funcs, "\n• ")
				if len(reasonCounts) == 1 && len(funcs) == len(failedList) {
					// 如果所有失败都是同一个原因，在最后统一显示
					failedDetails += "\n💡 失败原因：" + reason
				}
			}
			params["failedDetails"] = failedDetails
		} else {
			params["failedDetails"] = ""
		}
	} else {
		// 全部成功的情况
		template = "🚀 Lambda部署报告\n\n" +
			"👤 用户：{username}\n" +
			"📦 服务：{service} v{version}\n" +
			"🎯 环境：{env}\n" +
			"⏰ 时间：{time}\n\n" +
			"📊 部署结果：{total}个函数 (✅全部成功){successDetails}"

		// 添加成功列表
		if len(successList) > 0 {
			params["successDetails"] = "\n\n✅ 成功部署：\n• " + strings.Join(successList, "\n• ")
		} else {
			params["successDetails"] = ""
		}
		params["failedDetails"] = ""
	}

	// 格式化最终消息
	messageText = s.formatText(template, params)

	// 发送消息
	err := s.SendTextMessage(messageText)
	if err == nil {
		// 如果发送成功，记录到缓存中
		s.recordNotification(user.Sub, notifyType)
	}

	return err
}

// NotifyDesktopUploadCard 发送桌面客户端上传通知（消息卡片格式）
func (s *FeishuService) NotifyDesktopUploadCard(user *auth.UserInfo, version, env string, summary map[string]interface{}) error {
	logrus.WithFields(logrus.Fields{
		"user":    user.PreferredUsername,
		"version": version,
		"env":     env,
	}).Debug("开始发送桌面客户端上传通知卡片")

	// 检查是否启用桌面客户端上传通知
	if !s.Config.Notification.DesktopUpload.Enabled {
		logrus.Debug("桌面客户端上传通知被禁用，跳过发送")
		return nil
	}

	// 去重检查
	notifyType := fmt.Sprintf("desktop_upload:%s:%s", version, env)
	if s.isDuplicateNotification(user.Sub, notifyType) {
		logrus.WithFields(logrus.Fields{
			"user":    user.PreferredUsername,
			"version": version,
			"env":     env,
		}).Info("5分钟内已发送过相同版本和环境的上传通知，跳过重复发送")
		return nil
	}

	// 解析摘要信息
	totalFiles, _ := summary["total"].(int)
	successCount, _ := summary["success"].(int)
	failedCount, _ := summary["failed"].(int)
	successList, _ := summary["successList"].([]string)
	successDetails, _ := summary["successDetails"].(map[string][]string)
	failedList, _ := summary["failedList"].([]string)
	failedReasons, _ := summary["failedReasons"].(map[string]string)

	logrus.WithFields(logrus.Fields{
		"totalFiles":   totalFiles,
		"successCount": successCount,
		"failedCount":  failedCount,
		"successList":  successList,
		"failedList":   failedList,
	}).Debug("解析摘要信息完成")

	// 构建消息卡片
	logrus.Debug("开始构建消息卡片")
	card := s.buildDesktopUploadCard(user, version, env, totalFiles, successCount, failedCount, successList, successDetails, failedList, failedReasons)
	logrus.Debug("消息卡片构建完成")

	// 发送消息卡片
	err := s.sendCard(card)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"user":    user.PreferredUsername,
			"version": version,
			"env":     env,
			"error":   err.Error(),
		}).Error("发送桌面客户端上传通知卡片失败")
		return err
	}

	// 如果发送成功，记录到缓存中
	s.recordNotification(user.Sub, notifyType)

	logrus.WithFields(logrus.Fields{
		"user":    user.PreferredUsername,
		"version": version,
		"env":     env,
		"result":  fmt.Sprintf("%d/%d success, %d failed", successCount, totalFiles, failedCount),
	}).Info("发送桌面客户端上传通知卡片成功")
	return nil
}

// sendCard 发送消息卡片
func (s *FeishuService) sendCard(card map[string]interface{}) error {
	// 如果通知功能被禁用，直接返回
	if !s.Config.Enabled {
		logrus.Debug("飞书通知功能被禁用，跳过消息卡片发送")
		return nil
	}

	// 构建消息体
	message := FeishuMessage{
		MsgType: "interactive",
		Card:    card,
	}

	// 发送HTTP请求
	jsonData, err := json.Marshal(message)
	if err != nil {
		logrus.WithError(err).Error("序列化消息卡片失败")
		return fmt.Errorf("序列化消息失败: %v", err)
	}

	// 调试：打印发送的JSON数据
	logrus.WithField("json", string(jsonData)).Debug("准备发送消息卡片")
	logrus.WithField("webhook_url", s.Config.WebhookURL).Debug("使用的webhook地址")

	resp, err := http.Post(s.Config.WebhookURL, "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		logrus.WithError(err).Error("发送消息卡片请求失败")
		return fmt.Errorf("发送请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		logrus.WithError(readErr).Warn("读取响应体失败")
	}

	logrus.WithFields(logrus.Fields{
		"status_code": resp.StatusCode,
		"response":    string(body),
	}).Debug("飞书API响应")

	if resp.StatusCode != http.StatusOK {
		logrus.WithFields(logrus.Fields{
			"status_code": resp.StatusCode,
			"response":    string(body),
		}).Error("飞书API返回错误")
		return fmt.Errorf("发送失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	logrus.Debug("消息卡片发送成功")
	return nil
}

// buildDesktopUploadCard 构建桌面客户端上传通知的消息卡片
func (s *FeishuService) buildDesktopUploadCard(user *auth.UserInfo, version, env string, totalFiles, successCount, failedCount int, successList []string, successDetails map[string][]string, failedList []string, failedReasons map[string]string) map[string]interface{} {
	// 构建状态信息
	var statusColor string
	var statusText string
	if totalFiles == 0 {
		statusColor = "red"
		statusText = "全部失败"
	} else if failedCount > 0 {
		statusColor = "orange"
		statusText = fmt.Sprintf("部分成功 (%d/%d)", successCount, totalFiles)
	} else {
		statusColor = "green"
		statusText = "全部成功"
	}

	// 构建消息卡片（按照成功的JSON格式）
	//https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot#478cb64f
	card := map[string]interface{}{
		"schema": "2.0",
		"config": map[string]interface{}{
			"update_multi": true,
			"style": map[string]interface{}{
				"text_size": map[string]interface{}{
					"normal_v2": map[string]interface{}{
						"default": "normal",
						"pc":      "normal",
						"mobile":  "heading",
					},
				},
			},
		},
		"header": map[string]interface{}{
			"title": map[string]interface{}{
				"tag":     "plain_text",
				"content": "🖥️ 桌面客户端发布报告",
			},
			"template": statusColor,
		},
		"body": map[string]interface{}{
			"elements": []map[string]interface{}{
				{
					"tag": "div",
					"text": map[string]interface{}{
						"tag": "lark_md",
						"content": fmt.Sprintf("👤 用户：%s\n📦 版本：%s\n🎯 环境：%s\n⏰ 时间：%s\n\n📊 发布结果：%d个文件 (%s)",
							user.PreferredUsername,
							version,
							env,
							time.Now().Format("2006-01-02 15:04:05"),
							totalFiles,
							statusText),
					},
				},
			},
		},
	}

	// 如果有成功的文件，添加表格
	if len(successList) > 0 {
		// 构建表格数据
		var tableRows []map[string]interface{}

		// 按平台分类文件
		platformFiles := map[string][]string{
			"通用":      {},
			"Linux":   {},
			"Windows": {},
			"Mac":     {},
		}

		for _, fileName := range successList {
			fileNameLower := strings.ToLower(fileName)
			switch {
			// 通用配置文件
			case fileName == "latest.yml" || fileName == "latest-app-cn.yml":
				platformFiles["通用"] = append(platformFiles["通用"], fileName)
			// Linux 相关文件
			case strings.Contains(fileNameLower, "linux") || strings.HasSuffix(fileNameLower, ".appimage"):
				platformFiles["Linux"] = append(platformFiles["Linux"], fileName)
			// Windows 相关文件
			case strings.Contains(fileNameLower, "win") || strings.HasSuffix(fileNameLower, ".exe") || strings.Contains(fileNameLower, ".exe."):
				platformFiles["Windows"] = append(platformFiles["Windows"], fileName)
			// Mac 相关文件
			case strings.Contains(fileNameLower, "mac") || strings.HasSuffix(fileNameLower, ".dmg") || strings.HasSuffix(fileNameLower, ".zip") || strings.Contains(fileNameLower, ".dmg.") || strings.Contains(fileNameLower, ".zip."):
				platformFiles["Mac"] = append(platformFiles["Mac"], fileName)
			// 其他 yml 配置文件，根据文件名推断平台
			case strings.HasSuffix(fileNameLower, ".yml"):
				if strings.Contains(fileNameLower, "linux") {
					platformFiles["Linux"] = append(platformFiles["Linux"], fileName)
				} else if strings.Contains(fileNameLower, "win") {
					platformFiles["Windows"] = append(platformFiles["Windows"], fileName)
				} else if strings.Contains(fileNameLower, "mac") {
					platformFiles["Mac"] = append(platformFiles["Mac"], fileName)
				} else {
					// 无法确定平台的yml文件归类为通用
					platformFiles["通用"] = append(platformFiles["通用"], fileName)
				}
			// 默认情况：其他文件归类为通用
			default:
				platformFiles["通用"] = append(platformFiles["通用"], fileName)
			}
		}

		// 构建表格行数据
		platforms := []string{"通用", "Linux", "Windows", "Mac"}
		for _, platform := range platforms {
			files := platformFiles[platform]
			if len(files) > 0 {
				for i, fileName := range files {
					// 获取完整的OSS路径
					var ossPath string
					if paths, exists := successDetails[fileName]; exists && len(paths) > 0 {
						ossPath = paths[0]
						if len(paths) > 1 {
							for _, path := range paths {
								if !strings.Contains(path, "latest") {
									ossPath = path
									break
								}
							}
						}
					} else {
						if platform == "通用" {
							ossPath = fmt.Sprintf("%s/ngiq-p-cloud-desktop", env)
						} else {
							ossPath = fmt.Sprintf("%s/ngiq-p-cloud-desktop/%s", env, strings.ToLower(platform))
						}
					}

					// 平台名称只在第一行显示
					platformName := ""
					if i == 0 {
						if platform == "通用" {
							platformName = "通用"
						} else {
							platformName = strings.ToLower(platform)
						}
					} else {
						platformName = "--"
					}

					// 构建表格行（对象格式）
					row := map[string]interface{}{
						"platform": platformName,
						"filename": fileName,
						"osspath":  ossPath,
					}
					tableRows = append(tableRows, row)
				}
			}
		}

		// 添加表格组件
		//https://open.feishu.cn/document/feishu-cards/card-components/content-components/table#bc6a8146
		table := map[string]interface{}{
			"tag":        "table",
			"page_size":  10,
			"row_height": "low",
			"header_style": map[string]interface{}{
				"text_align":       "left",
				"text_size":        "normal",
				"background_style": "none",
				"text_color":       "grey",
				"bold":             true,
				"lines":            1,
			},
			"columns": []map[string]interface{}{
				{
					"name":             "platform",
					"display_name":     "平台",
					"data_type":        "text",
					"horizontal_align": "left",
					"vertical_align":   "top",
					"width":            "auto",
				},
				{
					"name":             "filename",
					"display_name":     "文件名称",
					"data_type":        "text",
					"horizontal_align": "left",
					"vertical_align":   "top",
					"width":            "auto",
				},
				{
					"name":             "osspath",
					"display_name":     "oss路径",
					"data_type":        "text",
					"horizontal_align": "left",
					"vertical_align":   "top",
					"width":            "auto",
				},
			},
			"rows": tableRows,
		}

		// 添加表格到body的elements中（如果是新格式）
		if bodyMap, ok := card["body"].(map[string]interface{}); ok {
			bodyElements := bodyMap["elements"].([]map[string]interface{})
			bodyElements = append(bodyElements, table)
			bodyMap["elements"] = bodyElements
		} else {
			// 兼容旧格式
			elements := card["elements"].([]map[string]interface{})
			elements = append(elements, table)
			card["elements"] = elements
		}
	}

	// 如果有失败的文件，添加失败信息
	if len(failedList) > 0 {
		failedText := "❌ **失败文件：**\n"
		for _, fileName := range failedList {
			reason := failedReasons[fileName]
			if reason == "" {
				reason = "未知错误"
			}
			failedText += fmt.Sprintf("• %s: %s\n", fileName, reason)
		}

		failedDiv := map[string]interface{}{
			"tag": "div",
			"text": map[string]interface{}{
				"tag":     "lark_md",
				"content": failedText,
			},
		}

		// 添加失败信息到body的elements中（如果是新格式）
		if bodyMap, ok := card["body"].(map[string]interface{}); ok {
			bodyElements := bodyMap["elements"].([]map[string]interface{})
			bodyElements = append(bodyElements, failedDiv)
			bodyMap["elements"] = bodyElements
		} else {
			// 兼容旧格式
			elements := card["elements"].([]map[string]interface{})
			elements = append(elements, failedDiv)
			card["elements"] = elements
		}
	}

	return card
}
