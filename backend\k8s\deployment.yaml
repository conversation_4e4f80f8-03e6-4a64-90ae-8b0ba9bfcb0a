apiVersion: apps/v1
kind: Deployment
metadata:
  name: ngiq-point-desktop-upload-oss
  namespace: ops
  labels:
    app: ngiq-point-desktop-upload-oss
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ngiq-point-desktop-upload-oss
  template:
    metadata:
      labels:
        app: ngiq-point-desktop-upload-oss
    spec:
      imagePullSecrets:
      - name: gitlab-registry-secret
      containers:
      - name: ngiq-point-desktop-upload-oss
        image: docker.ngdevops.cn/ops/ngiq-point-desktop-upload-oss:v202508051550
        env:
        - name: GITLAB_TOKEN
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: gitlab-token
        - name: DEV_OSS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: dev-oss-access-key-id
        - name: DEV_OSS_ACCESS_KEY_SECRET
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: dev-oss-access-key-secret
        - name: PROD_OSS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: prod-oss-access-key-id
        - name: PROD_OSS_ACCESS_KEY_SECRET
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: prod-oss-access-key-secret 
        - name: DEV_FC_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: dev-fc-access-key-id
        - name: DEV_FC_ACCESS_KEY_SECRET
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: dev-fc-access-key-secret
        - name: DEV_FC_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: dev-fc-endpoint
        - name: PROD_FC_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: prod-fc-access-key-id
        - name: PROD_FC_ACCESS_KEY_SECRET
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: prod-fc-access-key-secret
        - name: PROD_FC_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: ngiq-point-desktop-upload-oss-secret
              key: prod-fc-endpoint
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: config
          mountPath: /app/config.yaml
          subPath: config.yaml
        - name: localtime
          mountPath: /etc/localtime
          readOnly: true
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 256m
            memory: 1024Mi
      volumes:
      - name: config
        configMap:
          name: ngiq-point-desktop-upload-oss-config
      - name: localtime
        hostPath:
          path: /usr/share/zoneinfo/Asia/Shanghai

