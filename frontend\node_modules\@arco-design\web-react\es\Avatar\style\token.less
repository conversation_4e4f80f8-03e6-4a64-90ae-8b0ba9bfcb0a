@import '../../style/theme/default.less';

@avatar-size-default: @size-10;
@avatar-color-text: var(~'@{arco-cssvars-prefix}-color-white');
@avatar-color-bg: var(~'@{arco-cssvars-prefix}-color-fill-4');
@avatar-color-group-item-border: var(~'@{arco-cssvars-prefix}-color-bg-2');
@avatar-group-item-border-width: 2px;
@avatar-group-item-margin-left: -10px;
@avatar-group-popover-item-spacing: @spacing-2;
@avatar-font-weight-text: @font-weight-500;
@avatar-font-size-text: 20px;
@avatar-circle-border-radius: @radius-circle;
@avatar-square-border-radius: @radius-medium;
@avatar-font-size-max-count: 20px;
@avatar-color-max-count-text: var(~'@{arco-cssvars-prefix}-color-white');

@avatar-size-trigger-button: @size-5;
@avatar-spacing-trigger-button-right: @spacing-2;
@avatar-spacing-trigger-button-bottom: @spacing-2;
@avatar-color-trigger-button-bg: var(~'@{arco-cssvars-prefix}-color-neutral-2');
@avatar-color-trigger-button-bg_hover: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@avatar-color-trigger-mask-icon: var(~'@{arco-cssvars-prefix}-color-white');
@avatar-opacity-trigger-mask-bg: @opacity-6;
@avatar-color-trigger-icon-button: var(~'@{arco-cssvars-prefix}-color-fill-4');
@avatar-size-trigger-icon: @size-3;
@avatar-border-trigger-button-radius: @radius-circle;
