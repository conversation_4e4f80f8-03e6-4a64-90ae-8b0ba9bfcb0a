apiVersion: apps/v1
kind: Deployment
metadata:
  name: ngiq-point-desktop-upload-oss-frontend
  namespace: ops
  labels:
    app: ngiq-point-desktop-upload-oss-frontend
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ngiq-point-desktop-upload-oss-frontend
  template:
    metadata:
      labels:
        app: ngiq-point-desktop-upload-oss-frontend
    spec:
      imagePullSecrets:
      - name: gitlab-registry-secret
      containers:
      - name: ngiq-point-desktop-upload-oss-frontend
        image: docker.ngdevops.cn/ops/ngiq-point-desktop-upload-oss:fronted-v202508041604
        ports:
        - containerPort: 80
        volumeMounts:
        - name: nginx-config
          mountPath: /etc/nginx/conf.d/ptc-tools.ngdevops.cn.conf
          subPath: ptc-tools.ngdevops.cn.conf
        - name: localtime
          mountPath: /etc/localtime
          readOnly: true
        resources:
          requests:
            cpu: 100m
            memory: 128Mi
          limits:
            cpu: 256m
            memory: 2048Mi
      volumes:
      - name: localtime
        hostPath:
          path: /usr/share/zoneinfo/Asia/Shanghai
      - name: nginx-config
        configMap:
          name: nginx-config

