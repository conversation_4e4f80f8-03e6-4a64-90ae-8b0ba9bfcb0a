package utils

import (
	"archive/zip"
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"path/filepath"
	"strings"

	"github.com/sirupsen/logrus"
)

// ConvertJarToZip 将JAR文件直接压缩到ZIP包里（按照阿里云工程师建议）
func ConvertJarToZip(jarContent []byte, jarFileName string) ([]byte, error) {
	logrus.WithFields(logrus.Fields{
		"jarFileName": jarFileName,
		"jarSize":     len(jarContent),
	}).Info("开始将JAR文件直接压缩到ZIP包里")

	// 验证JAR文件是有效的ZIP格式
	if !isZipFile(jarContent) {
		logrus.WithField("jarFileName", jarFileName).Error("JAR文件不是有效的ZIP格式")
		return nil, fmt.Errorf("JAR文件不是有效的ZIP格式: %s", jarFileName)
	}

	// 创建新的ZIP文件，直接把JAR文件作为内容压缩进去
	var zipBuffer bytes.Buffer
	zipWriter := zip.NewWriter(&zipBuffer)

	// 将整个JAR文件作为一个文件添加到ZIP中
	fileInZip, err := zipWriter.Create(jarFileName)
	if err != nil {
		logrus.WithError(err).Error("在ZIP中创建JAR文件条目失败")
		return nil, fmt.Errorf("在ZIP中创建JAR文件条目失败: %w", err)
	}

	// 将JAR文件内容写入ZIP
	_, err = fileInZip.Write(jarContent)
	if err != nil {
		logrus.WithError(err).Error("写入JAR文件内容到ZIP失败")
		return nil, fmt.Errorf("写入JAR文件内容到ZIP失败: %w", err)
	}

	// 关闭ZIP写入器
	if err := zipWriter.Close(); err != nil {
		logrus.WithError(err).Error("关闭ZIP写入器失败")
		return nil, fmt.Errorf("关闭ZIP写入器失败: %w", err)
	}

	zipContent := zipBuffer.Bytes()
	logrus.WithFields(logrus.Fields{
		"jarFileName":  jarFileName,
		"originalSize": len(jarContent),
		"zipSize":      len(zipContent),
	}).Info("JAR文件直接压缩到ZIP包完成")

	return zipContent, nil
}

// CompressFileToZip 将任意文件内容压缩成标准ZIP格式，返回ZIP二进制数据
func CompressFileToZip(fileContent []byte, fileName string) ([]byte, error) {
	// 如果是JAR文件，直接转换而不是压缩
	if IsJarFile(fileName) {
		return ConvertJarToZip(fileContent, fileName)
	}

	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"fileSize": len(fileContent),
	}).Info("开始将文件压缩成标准ZIP格式")

	// 创建一个新的ZIP文件缓冲区
	var zipBuffer bytes.Buffer
	zipWriter := zip.NewWriter(&zipBuffer)

	// 将文件添加到ZIP中
	fileInZip, err := zipWriter.Create(fileName)
	if err != nil {
		logrus.WithError(err).Error("创建ZIP文件中的文件条目失败")
		return nil, fmt.Errorf("创建ZIP文件中的文件条目失败: %w", err)
	}

	// 写入文件内容
	_, err = fileInZip.Write(fileContent)
	if err != nil {
		logrus.WithError(err).Error("写入文件内容到ZIP失败")
		return nil, fmt.Errorf("写入文件内容到ZIP失败: %w", err)
	}

	// 关闭ZIP写入器
	err = zipWriter.Close()
	if err != nil {
		logrus.WithError(err).Error("关闭ZIP写入器失败")
		return nil, fmt.Errorf("关闭ZIP写入器失败: %w", err)
	}

	// 获取ZIP文件内容
	zipContent := zipBuffer.Bytes()
	logrus.WithFields(logrus.Fields{
		"originalFileSize": len(fileContent),
		"zipSize":          len(zipContent),
	}).Info("文件压缩成标准ZIP完成")

	// 验证生成的ZIP文件
	if err := ValidateZipContent(zipContent); err != nil {
		logrus.WithError(err).Error("生成的ZIP文件验证失败")
		return nil, fmt.Errorf("生成的ZIP文件验证失败: %w", err)
	}

	return zipContent, nil
}

// CreateZipFromJar 将JAR文件内容压缩成ZIP格式并返回base64编码（保留原函数以兼容）
func CreateZipFromJar(jarContent []byte, jarFileName string) (string, error) {
	// 第一步：压缩成ZIP
	zipContent, err := CompressFileToZip(jarContent, jarFileName)
	if err != nil {
		return "", err
	}

	// 第二步：base64编码
	base64Content := base64.StdEncoding.EncodeToString(zipContent)
	logrus.WithField("base64Length", len(base64Content)).Info("ZIP文件base64编码完成")

	return base64Content, nil
}

// EncodeZipToBase64 将已有的ZIP文件内容进行base64编码
func EncodeZipToBase64(zipContent []byte, fileName string) (string, error) {
	logrus.WithFields(logrus.Fields{
		"fileName": fileName,
		"zipSize":  len(zipContent),
	}).Info("开始对ZIP文件进行base64编码")

	// 验证是否为ZIP文件
	if !isZipFile(zipContent) {
		logrus.WithField("fileName", fileName).Warn("文件可能不是有效的ZIP格式")
	}

	// 进行base64编码
	base64Content := base64.StdEncoding.EncodeToString(zipContent)
	logrus.WithField("base64Length", len(base64Content)).Info("ZIP文件base64编码完成")

	return base64Content, nil
}

// isZipFile 检查文件内容是否为ZIP格式
func isZipFile(content []byte) bool {
	// ZIP文件的魔数是 "PK" (0x504B)
	if len(content) < 4 {
		return false
	}
	return content[0] == 0x50 && content[1] == 0x4B
}

// GetFileExtension 获取文件扩展名
func GetFileExtension(fileName string) string {
	return strings.ToLower(filepath.Ext(fileName))
}

// IsJarFile 判断是否为JAR文件
func IsJarFile(fileName string) bool {
	return GetFileExtension(fileName) == ".jar"
}

// IsZipFile 判断是否为ZIP文件
func IsZipFile(fileName string) bool {
	return GetFileExtension(fileName) == ".zip"
}

// ValidateZipContent 验证ZIP文件内容的完整性
func ValidateZipContent(content []byte) error {
	reader := bytes.NewReader(content)
	zipReader, err := zip.NewReader(reader, int64(len(content)))
	if err != nil {
		return fmt.Errorf("无法读取ZIP文件: %w", err)
	}

	// 检查ZIP文件是否包含文件
	if len(zipReader.File) == 0 {
		return fmt.Errorf("ZIP文件为空")
	}

	// 尝试读取第一个文件以验证完整性
	firstFile := zipReader.File[0]
	fileReader, err := firstFile.Open()
	if err != nil {
		return fmt.Errorf("无法打开ZIP文件中的第一个文件: %w", err)
	}
	defer fileReader.Close()

	// 读取少量数据以验证
	buffer := make([]byte, 1024)
	_, err = fileReader.Read(buffer)
	if err != nil && err != io.EOF {
		return fmt.Errorf("读取ZIP文件内容失败: %w", err)
	}

	logrus.WithField("fileCount", len(zipReader.File)).Debug("ZIP文件验证通过")
	return nil
}
