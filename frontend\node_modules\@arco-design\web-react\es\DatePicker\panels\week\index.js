var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
import React, { useContext } from 'react';
import { methods } from '../../../_util/dayjs';
import DatePanel from '../date';
import PickerContext from '../../context';
function WeekPicker(props) {
    var value = props.value, isRangePicker = props.isRangePicker, rangeValues = props.rangeValues, onPrev = props.onPrev, onNext = props.onNext, onSuperPrev = props.onSuperPrev, onSuperNext = props.onSuperNext, localeName = props.localeName, rest = __rest(props, ["value", "isRangePicker", "rangeValues", "onPrev", "onNext", "onSuperPrev", "onSuperNext", "localeName"]);
    var weekStart = useContext(PickerContext).weekStart;
    var bodyProps = isRangePicker ? { rangeValues: rangeValues } : { value: value };
    var headerOperations = { onPrev: onPrev, onNext: onNext, onSuperPrev: onSuperPrev, onSuperNext: onSuperNext };
    function isSameTime(current, target) {
        return methods.isSameWeek(current, target, weekStart, localeName);
    }
    return (React.createElement(DatePanel, __assign({}, rest, bodyProps, headerOperations, { isWeek: true, isSameTime: isSameTime, isRangePicker: isRangePicker })));
}
export default WeekPicker;
