var __read = (this && this.__read) || function (o, n) {
    var m = typeof Symbol === "function" && o[Symbol.iterator];
    if (!m) return o;
    var i = m.call(o), r, ar = [], e;
    try {
        while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);
    }
    catch (error) { e = { error: error }; }
    finally {
        try {
            if (r && !r.done && (m = i["return"])) m.call(i);
        }
        finally { if (e) throw e.error; }
    }
    return ar;
};
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
import { useRef } from 'react';
import useForceUpdate from '../../_util/hooks/useForceUpdate';
import useUpdate from '../../_util/hooks/useUpdate';
function useCurrentRef(initFunc, deps) {
    var ref = useRef(null);
    var forceUpdate = useForceUpdate();
    if (!ref.current) {
        ref.current = initFunc();
    }
    useUpdate(function () {
        ref.current = initFunc();
        forceUpdate();
    }, __spreadArray([], __read(deps), false));
    return ref.current;
}
export default useCurrentRef;
