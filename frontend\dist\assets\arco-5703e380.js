import{r as d,R as l,a as St,c as Ir,b as pv,g as Vn}from"./vendor-4d8e7009.js";function Ve(){return Ve=Object.assign?Object.assign.bind():function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)({}).hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},Ve.apply(null,arguments)}function An(e){"@babel/helpers - typeof";return An=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(n){return typeof n}:function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},An(e)}function mv(e,n){if(An(e)!="object"||!e)return e;var t=e[Symbol.toPrimitive];if(t!==void 0){var r=t.call(e,n||"default");if(An(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(n==="string"?String:Number)(e)}function gv(e){var n=mv(e,"string");return An(n)=="symbol"?n:n+""}function Ue(e,n,t){return(n=gv(n))in e?Object.defineProperty(e,n,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[n]=t,e}var We=d.createContext({prefixCls:"arco"});function ts(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function rs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?ts(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ts(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function hv(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=rs(rs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-check-circle-fill")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm10.207-24.379a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0L22 26.172l-4.878-4.88a1 1 0 0 0-1.415 0l-1.414 1.415a1 1 0 0 0 0 1.414l7 7a1 1 0 0 0 1.414 0l11.5-11.5Z",clipRule:"evenodd"}))}var Wo=l.forwardRef(hv);Wo.defaultProps={isIcon:!0};Wo.displayName="IconCheckCircleFill";const Fo=Wo;function ns(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function as(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?ns(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ns(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function yv(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=as(as({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-close-circle-fill")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm4.955-27.771-4.95 4.95-4.95-4.95a1 1 0 0 0-1.414 0l-1.414 1.414a1 1 0 0 0 0 1.414l4.95 4.95-4.95 4.95a1 1 0 0 0 0 1.414l1.414 1.414a1 1 0 0 0 1.414 0l4.95-4.95 4.95 4.95a1 1 0 0 0 1.414 0l1.414-1.414a1 1 0 0 0 0-1.414l-4.95-4.95 4.95-4.95a1 1 0 0 0 0-1.414l-1.414-1.414a1 1 0 0 0-1.414 0Z",clipRule:"evenodd"}))}var Bo=l.forwardRef(yv);Bo.defaultProps={isIcon:!0};Bo.displayName="IconCloseCircleFill";const Zu=Bo;function is(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function os(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?is(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):is(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function bv(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=os(os({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-info-circle-fill")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm2-30a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2Zm0 17h1a1 1 0 0 1 1 1v2a1 1 0 0 1-1 1h-6a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h1v-8a1 1 0 0 1-1-1v-2a1 1 0 0 1 1-1h3a1 1 0 0 1 1 1v11Z",clipRule:"evenodd"}))}var Ko=l.forwardRef(bv);Ko.defaultProps={isIcon:!0};Ko.displayName="IconInfoCircleFill";const Xu=Ko;function ls(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function ss(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?ls(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ls(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function xv(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=ss(ss({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-exclamation-circle-fill")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",fillRule:"evenodd",stroke:"none",d:"M24 44c11.046 0 20-8.954 20-20S35.046 4 24 4 4 12.954 4 24s8.954 20 20 20Zm-2-11a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1v-2a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v2Zm4-18a1 1 0 0 0-1-1h-2a1 1 0 0 0-1 1v12a1 1 0 0 0 1 1h2a1 1 0 0 0 1-1V15Z",clipRule:"evenodd"}))}var Go=l.forwardRef(xv);Go.defaultProps={isIcon:!0};Go.displayName="IconExclamationCircleFill";const Na=Go;function cs(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function us(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?cs(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):cs(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function Cv(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=us(us({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-close")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M9.857 9.858 24 24m0 0 14.142 14.142M24 24 38.142 9.858M24 24 9.857 38.142"}))}var Uo=l.forwardRef(Cv);Uo.defaultProps={isIcon:!0};Uo.displayName="IconClose";const Dt=Uo;var Yu={exports:{}},Ze={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zo=Symbol.for("react.element"),Xo=Symbol.for("react.portal"),Ia=Symbol.for("react.fragment"),ja=Symbol.for("react.strict_mode"),Ma=Symbol.for("react.profiler"),Ra=Symbol.for("react.provider"),ka=Symbol.for("react.context"),Ev=Symbol.for("react.server_context"),Aa=Symbol.for("react.forward_ref"),Da=Symbol.for("react.suspense"),La=Symbol.for("react.suspense_list"),za=Symbol.for("react.memo"),$a=Symbol.for("react.lazy"),wv=Symbol.for("react.offscreen"),qu;qu=Symbol.for("react.module.reference");function It(e){if(typeof e=="object"&&e!==null){var n=e.$$typeof;switch(n){case Zo:switch(e=e.type,e){case Ia:case Ma:case ja:case Da:case La:return e;default:switch(e=e&&e.$$typeof,e){case Ev:case ka:case Aa:case $a:case za:case Ra:return e;default:return n}}case Xo:return n}}}Ze.ContextConsumer=ka;Ze.ContextProvider=Ra;Ze.Element=Zo;Ze.ForwardRef=Aa;Ze.Fragment=Ia;Ze.Lazy=$a;Ze.Memo=za;Ze.Portal=Xo;Ze.Profiler=Ma;Ze.StrictMode=ja;Ze.Suspense=Da;Ze.SuspenseList=La;Ze.isAsyncMode=function(){return!1};Ze.isConcurrentMode=function(){return!1};Ze.isContextConsumer=function(e){return It(e)===ka};Ze.isContextProvider=function(e){return It(e)===Ra};Ze.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Zo};Ze.isForwardRef=function(e){return It(e)===Aa};Ze.isFragment=function(e){return It(e)===Ia};Ze.isLazy=function(e){return It(e)===$a};Ze.isMemo=function(e){return It(e)===za};Ze.isPortal=function(e){return It(e)===Xo};Ze.isProfiler=function(e){return It(e)===Ma};Ze.isStrictMode=function(e){return It(e)===ja};Ze.isSuspense=function(e){return It(e)===Da};Ze.isSuspenseList=function(e){return It(e)===La};Ze.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===Ia||e===Ma||e===ja||e===Da||e===La||e===wv||typeof e=="object"&&e!==null&&(e.$$typeof===$a||e.$$typeof===za||e.$$typeof===Ra||e.$$typeof===ka||e.$$typeof===Aa||e.$$typeof===qu||e.getModuleId!==void 0)};Ze.typeOf=It;Yu.exports=Ze;var Ju=Yu.exports,li,si,Va=Object.prototype.toString;function ct(e){return Va.call(e)==="[object Array]"}function xe(e){return Va.call(e)==="[object Object]"}function ar(e){return Va.call(e)==="[object String]"}function Ye(e){return Va.call(e)==="[object Number]"&&e===e}function ft(e){return e===void 0}function Qu(e){return e===null}function Ov(e){return e==null}function Ke(e){return typeof e=="function"}function sa(e,n){return e==null||e===!1||typeof e=="string"&&(n?e.trim()==="":e==="")}var ef=function(e){return e&&d.isValidElement(e)&&typeof e.type=="function"},_v=function(e){var n;return ef(e)&&!!(!((n=e.type.prototype)===null||n===void 0)&&n.isReactComponent)},Pv=function(e){return d.isValidElement(e)&&typeof e.type=="string"},tf=Number((li=St.version)===null||li===void 0?void 0:li.split(".")[0])>17,Tv=Number((si=St.version)===null||si===void 0?void 0:si.split(".")[0])>18,Sv=function(e){if(!Tv)return Ju.isForwardRef(e);var n=Symbol.for("react.element"),t=Symbol.for("react.transitional.element"),r=Symbol.for("react.forward_ref");if(typeof e=="object"&&e!==null){var a=e.$$typeof;if(a===n||a===t){var i=e.type,s=i&&i.$$typeof;return s===r}}return!1},Yo=function(e){return Pv(e)||Sv(e)?!0:ef(e)?_v(e):!1};var Nv=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Iv=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function K(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];for(var t=e.length,r=[],a=function(s){var o=e[s];if(!o)return"continue";ar(o)?r.push(o):ct(o)?r=r.concat(o):xe(o)&&Object.keys(o).forEach(function(u){o[u]&&r.push(u)})},i=0;i<t;i++)a(i);return Iv([],Nv(new Set(r)),!1).join(" ")}function jv(e){var n=[],t=e.substr(1);return e.length===3&&(t=e.replace(/(.)/g,"$1$1")),t.replace(/../g,function(r){n.push(parseInt(r,16))}),{r:n[0],g:n[1],b:n[2],rgb:"rgb("+n.join(",")+")"}}function Mv(e){var n=jv(e);return{r:n.r,g:n.g,b:n.b}}function Rv(e){var n=Mv(e),t=kv(n.r,n.g,n.b);return{h:t.h,s:t.s,l:t.l}}function kv(e,n,t){var r=e/255,a=n/255,i=t/255,s=Math.max(r,a,i),o=Math.min(r,a,i),u=(s+o)/2,c,f;if(s===o)c=0,f=0;else{var v=s-o;switch(f=u>.5?v/(2-s-o):v/(s+o),s){case r:c=(a-i)/v+(a<i?6:0);break;case a:c=(i-r)/v+2;break;case i:c=(r-a)/v+4;break}c/=6}return{h:c,s:f,l:u,hsl:"hsl("+c*360+", "+f*100+"%, "+u*100+"%)"}}function ci(e,n){var t=Rv(e),r=+t.h,a=+t.s,i=+t.l*100+ +n,s=Av([r*360,a*100,i]);return s.join(",")}function Av(e){var n=e[0]/360,t=e[1]/100,r=e[2]/100,a,i,s;if(t===0)return s=r*255,[s,s,s];r<.5?a=r*(1+t):a=r+t-r*t;for(var o=2*r-a,u=[0,0,0],c=0;c<3;c++)i=n+1/3*-(c-1),i<0&&i++,i>1&&i--,6*i<1?s=o+(a-o)*6*i:2*i<1?s=a:3*i<2?s=o+(a-o)*(2/3-i)*6:s=o,u[c]=s*255;return u}function qo(e,n){if(e==null)return{};var t={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(n.indexOf(r)!==-1)continue;t[r]=e[r]}return t}function ki(e,n){return ki=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,r){return t.__proto__=r,t},ki(e,n)}function Jo(e,n){e.prototype=Object.create(n.prototype),e.prototype.constructor=e,ki(e,n)}function Dv(e,n){return e.classList?!!n&&e.classList.contains(n):(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+n+" ")!==-1}function Lv(e,n){e.classList?e.classList.add(n):Dv(e,n)||(typeof e.className=="string"?e.className=e.className+" "+n:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+n))}function fs(e,n){return e.replace(new RegExp("(^|\\s)"+n+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function zv(e,n){e.classList?e.classList.remove(n):typeof e.className=="string"?e.className=fs(e.className,n):e.setAttribute("class",fs(e.className&&e.className.baseVal||"",n))}const ds={disabled:!1},ca=l.createContext(null);var rf=function(n){return n.scrollTop},Cn="unmounted",ur="exited",fr="entering",Nr="entered",Ai="exiting",Ut=function(e){Jo(n,e);function n(r,a){var i;i=e.call(this,r,a)||this;var s=a,o=s&&!s.isMounting?r.enter:r.appear,u;return i.appearStatus=null,r.in?o?(u=ur,i.appearStatus=fr):u=Nr:r.unmountOnExit||r.mountOnEnter?u=Cn:u=ur,i.state={status:u},i.nextCallback=null,i}n.getDerivedStateFromProps=function(a,i){var s=a.in;return s&&i.status===Cn?{status:ur}:null};var t=n.prototype;return t.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},t.componentDidUpdate=function(a){var i=null;if(a!==this.props){var s=this.state.status;this.props.in?s!==fr&&s!==Nr&&(i=fr):(s===fr||s===Nr)&&(i=Ai)}this.updateStatus(!1,i)},t.componentWillUnmount=function(){this.cancelNextCallback()},t.getTimeouts=function(){var a=this.props.timeout,i,s,o;return i=s=o=a,a!=null&&typeof a!="number"&&(i=a.exit,s=a.enter,o=a.appear!==void 0?a.appear:s),{exit:i,enter:s,appear:o}},t.updateStatus=function(a,i){if(a===void 0&&(a=!1),i!==null)if(this.cancelNextCallback(),i===fr){if(this.props.unmountOnExit||this.props.mountOnEnter){var s=this.props.nodeRef?this.props.nodeRef.current:St.findDOMNode(this);s&&rf(s)}this.performEnter(a)}else this.performExit();else this.props.unmountOnExit&&this.state.status===ur&&this.setState({status:Cn})},t.performEnter=function(a){var i=this,s=this.props.enter,o=this.context?this.context.isMounting:a,u=this.props.nodeRef?[o]:[St.findDOMNode(this),o],c=u[0],f=u[1],v=this.getTimeouts(),m=o?v.appear:v.enter;if(!a&&!s||ds.disabled){this.safeSetState({status:Nr},function(){i.props.onEntered(c)});return}this.props.onEnter(c,f),this.safeSetState({status:fr},function(){i.props.onEntering(c,f),i.onTransitionEnd(m,function(){i.safeSetState({status:Nr},function(){i.props.onEntered(c,f)})})})},t.performExit=function(){var a=this,i=this.props.exit,s=this.getTimeouts(),o=this.props.nodeRef?void 0:St.findDOMNode(this);if(!i||ds.disabled){this.safeSetState({status:ur},function(){a.props.onExited(o)});return}this.props.onExit(o),this.safeSetState({status:Ai},function(){a.props.onExiting(o),a.onTransitionEnd(s.exit,function(){a.safeSetState({status:ur},function(){a.props.onExited(o)})})})},t.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},t.safeSetState=function(a,i){i=this.setNextCallback(i),this.setState(a,i)},t.setNextCallback=function(a){var i=this,s=!0;return this.nextCallback=function(o){s&&(s=!1,i.nextCallback=null,a(o))},this.nextCallback.cancel=function(){s=!1},this.nextCallback},t.onTransitionEnd=function(a,i){this.setNextCallback(i);var s=this.props.nodeRef?this.props.nodeRef.current:St.findDOMNode(this),o=a==null&&!this.props.addEndListener;if(!s||o){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var u=this.props.nodeRef?[this.nextCallback]:[s,this.nextCallback],c=u[0],f=u[1];this.props.addEndListener(c,f)}a!=null&&setTimeout(this.nextCallback,a)},t.render=function(){var a=this.state.status;if(a===Cn)return null;var i=this.props,s=i.children;i.in,i.mountOnEnter,i.unmountOnExit,i.appear,i.enter,i.exit,i.timeout,i.addEndListener,i.onEnter,i.onEntering,i.onEntered,i.onExit,i.onExiting,i.onExited,i.nodeRef;var o=qo(i,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return l.createElement(ca.Provider,{value:null},typeof s=="function"?s(a,o):l.cloneElement(l.Children.only(s),o))},n}(l.Component);Ut.contextType=ca;Ut.propTypes={};function _r(){}Ut.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:_r,onEntering:_r,onEntered:_r,onExit:_r,onExiting:_r,onExited:_r};Ut.UNMOUNTED=Cn;Ut.EXITED=ur;Ut.ENTERING=fr;Ut.ENTERED=Nr;Ut.EXITING=Ai;const $v=Ut;var Vv=function(n,t){return n&&t&&t.split(" ").forEach(function(r){return Lv(n,r)})},ui=function(n,t){return n&&t&&t.split(" ").forEach(function(r){return zv(n,r)})},Qo=function(e){Jo(n,e);function n(){for(var r,a=arguments.length,i=new Array(a),s=0;s<a;s++)i[s]=arguments[s];return r=e.call.apply(e,[this].concat(i))||this,r.appliedClasses={appear:{},enter:{},exit:{}},r.onEnter=function(o,u){var c=r.resolveArguments(o,u),f=c[0],v=c[1];r.removeClasses(f,"exit"),r.addClass(f,v?"appear":"enter","base"),r.props.onEnter&&r.props.onEnter(o,u)},r.onEntering=function(o,u){var c=r.resolveArguments(o,u),f=c[0],v=c[1],m=v?"appear":"enter";r.addClass(f,m,"active"),r.props.onEntering&&r.props.onEntering(o,u)},r.onEntered=function(o,u){var c=r.resolveArguments(o,u),f=c[0],v=c[1],m=v?"appear":"enter";r.removeClasses(f,m),r.addClass(f,m,"done"),r.props.onEntered&&r.props.onEntered(o,u)},r.onExit=function(o){var u=r.resolveArguments(o),c=u[0];r.removeClasses(c,"appear"),r.removeClasses(c,"enter"),r.addClass(c,"exit","base"),r.props.onExit&&r.props.onExit(o)},r.onExiting=function(o){var u=r.resolveArguments(o),c=u[0];r.addClass(c,"exit","active"),r.props.onExiting&&r.props.onExiting(o)},r.onExited=function(o){var u=r.resolveArguments(o),c=u[0];r.removeClasses(c,"exit"),r.addClass(c,"exit","done"),r.props.onExited&&r.props.onExited(o)},r.resolveArguments=function(o,u){return r.props.nodeRef?[r.props.nodeRef.current,o]:[o,u]},r.getClassNames=function(o){var u=r.props.classNames,c=typeof u=="string",f=c&&u?u+"-":"",v=c?""+f+o:u[o],m=c?v+"-active":u[o+"Active"],p=c?v+"-done":u[o+"Done"];return{baseClassName:v,activeClassName:m,doneClassName:p}},r}var t=n.prototype;return t.addClass=function(a,i,s){var o=this.getClassNames(i)[s+"ClassName"],u=this.getClassNames("enter"),c=u.doneClassName;i==="appear"&&s==="done"&&c&&(o+=" "+c),s==="active"&&a&&rf(a),o&&(this.appliedClasses[i][s]=o,Vv(a,o))},t.removeClasses=function(a,i){var s=this.appliedClasses[i],o=s.base,u=s.active,c=s.done;this.appliedClasses[i]={},o&&ui(a,o),u&&ui(a,u),c&&ui(a,c)},t.render=function(){var a=this.props;a.classNames;var i=qo(a,["classNames"]);return l.createElement($v,Ve({},i,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},n}(l.Component);Qo.defaultProps={classNames:""};Qo.propTypes={};const nf=Qo;function Hv(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function el(e,n){var t=function(i){return n&&d.isValidElement(i)?n(i):i},r=Object.create(null);return e&&d.Children.map(e,function(a){return a}).forEach(function(a){r[a.key]=t(a)}),r}function Wv(e,n){e=e||{},n=n||{};function t(f){return f in n?n[f]:e[f]}var r=Object.create(null),a=[];for(var i in e)i in n?a.length&&(r[i]=a,a=[]):a.push(i);var s,o={};for(var u in n){if(r[u])for(s=0;s<r[u].length;s++){var c=r[u][s];o[r[u][s]]=t(c)}o[u]=t(u)}for(s=0;s<a.length;s++)o[a[s]]=t(a[s]);return o}function br(e,n,t){return t[n]!=null?t[n]:e.props[n]}function Fv(e,n){return el(e.children,function(t){return d.cloneElement(t,{onExited:n.bind(null,t),in:!0,appear:br(t,"appear",e),enter:br(t,"enter",e),exit:br(t,"exit",e)})})}function Bv(e,n,t){var r=el(e.children),a=Wv(n,r);return Object.keys(a).forEach(function(i){var s=a[i];if(d.isValidElement(s)){var o=i in n,u=i in r,c=n[i],f=d.isValidElement(c)&&!c.props.in;u&&(!o||f)?a[i]=d.cloneElement(s,{onExited:t.bind(null,s),in:!0,exit:br(s,"exit",e),enter:br(s,"enter",e)}):!u&&o&&!f?a[i]=d.cloneElement(s,{in:!1}):u&&o&&d.isValidElement(c)&&(a[i]=d.cloneElement(s,{onExited:t.bind(null,s),in:c.props.in,exit:br(s,"exit",e),enter:br(s,"enter",e)}))}}),a}var Kv=Object.values||function(e){return Object.keys(e).map(function(n){return e[n]})},Gv={component:"div",childFactory:function(n){return n}},tl=function(e){Jo(n,e);function n(r,a){var i;i=e.call(this,r,a)||this;var s=i.handleExited.bind(Hv(i));return i.state={contextValue:{isMounting:!0},handleExited:s,firstRender:!0},i}var t=n.prototype;return t.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},t.componentWillUnmount=function(){this.mounted=!1},n.getDerivedStateFromProps=function(a,i){var s=i.children,o=i.handleExited,u=i.firstRender;return{children:u?Fv(a,o):Bv(a,s,o),firstRender:!1}},t.handleExited=function(a,i){var s=el(this.props.children);a.key in s||(a.props.onExited&&a.props.onExited(i),this.mounted&&this.setState(function(o){var u=Ve({},o.children);return delete u[a.key],{children:u}}))},t.render=function(){var a=this.props,i=a.component,s=a.childFactory,o=qo(a,["component","childFactory"]),u=this.state.contextValue,c=Kv(this.state.children).map(s);return delete o.appear,delete o.enter,delete o.exit,i===null?l.createElement(ca.Provider,{value:u},c):l.createElement(ca.Provider,{value:u},l.createElement(i,o,c))},n}(l.Component);tl.propTypes={};tl.defaultProps=Gv;const rl=tl;var vs="__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED",Wr=St,Di,ps=function(e){xe(Wr[vs])&&(Wr[vs].usingClientEntryPoint=e)},Li;try{Li=Wr.createRoot}catch{}tf&&Li?Di=function(e,n){ps(!0);var t=Li(n);return ps(!1),t.render(e),t._unmount=function(){setTimeout(function(){var r;(r=t==null?void 0:t.unmount)===null||r===void 0||r.call(t)})},t}:Di=function(e,n){return Wr.render(e,n),{render:function(t){Wr.render(t,n)},_unmount:function(){Wr.unmountComponentAtNode(n)}}};var Jt;function Uv(e){var n=e.constructor;if(typeof n!="function")return!1;!Jt&&typeof WeakSet=="function"&&(Jt=new WeakSet);var t=!!(Jt!=null&&Jt.has(n));return Jt==null||Jt.add(n),t}var Ha=function(e,n){return e&&e instanceof Element?e:e&&e.current&&e.current instanceof Element?e.current:e&&Ke(e.getRootDOMNode)?e.getRootDOMNode():e instanceof d.Component&&St.findDOMNode?St.findDOMNode(e):n&&(tf&&Uv(n),St.findDOMNode)?St.findDOMNode(n):null},nl=function(e,n){e&&e.ref&&(Ke(e.ref)&&(e==null||e.ref(n)),"current"in e.ref&&(e.ref.current=n))},af=Di,zi=globalThis&&globalThis.__assign||function(){return zi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},zi.apply(this,arguments)},Zv=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Xv=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Yv=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function nr(e){var n=e.children,t=Zv(e,["children"]),r=d.useRef(),a=d.useRef(),i=d.useMemo(function(){return e.nodeRef===void 0&&Yo(n)&&d.isValidElement(n)?(a.current=!0,d.cloneElement(n,{ref:function(s){r.current=Ha(s),nl(n,s)}})):(a.current=!1,n)},[n,e.nodeRef]);return a.current&&["onEnter","onEntering","onEntered","onExit","onExiting","onExited"].forEach(function(s){e[s]&&(t[s]=function(o){for(var u=[],c=1;c<arguments.length;c++)u[c-1]=arguments[c];e[s].apply(e,Yv([r.current],Xv(u),!1))})}),l.createElement(nf,zi({},t,{nodeRef:a.current?r:void 0}),i)}var qv=globalThis&&globalThis.__extends||function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(r[i]=a[i])},e(n,t)};return function(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(n,t);function r(){this.constructor=n}n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}}(),ua=globalThis&&globalThis.__assign||function(){return ua=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ua.apply(this,arguments)};function Jv(e){return e!=null&&e.id?e==null?void 0:e.id:"arco_notice_id_"+Math.random().toFixed(10).slice(2)}var Qv=function(e){qv(n,e);function n(t){var r=e.call(this,t)||this;return r.add=function(a){var i=Jv(a),s=r.state.notices;return a.id&&~s.findIndex(function(o){return o.id===a.id})?r.update(a):r.setState(function(o){return{notices:o.notices.concat(ua(ua({},a),{id:i})),position:a.position}}),i},r.update=function(a){var i=r.state.notices.map(function(s){return a.id===s.id?a:s});r.setState({notices:i},function(){var s=i.map(function(o){return a.id===o.id&&delete o.update,o});r.setState({notices:s})})},r.clear=function(){r.setState({notices:[]})},r.state={notices:[],position:"topRight"},r.remove=r.remove.bind(r),r}return n.prototype.remove=function(t){this.setState(function(r){var a=r.notices.filter(function(i){return i.id!==t});return{notices:a}})},n}(d.Component);const of=Qv;function ms(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function gs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?ms(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):ms(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function ep(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=gs(gs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-loading")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M42 24c0 9.941-8.059 18-18 18S6 33.941 6 24 14.059 6 24 6"}))}var al=l.forwardRef(ep);al.defaultProps={isIcon:!0};al.displayName="IconLoading";const Yr=al;var $i=globalThis&&globalThis.__assign||function(){return $i=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},$i.apply(this,arguments)},tp=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function Nt(e){var n,t=e.children,r=e.className,a=e.disabled,i=e.prefix,s=e.size,o=s===void 0?"default":s,u=tp(e,["children","className","disabled","prefix","size"]),c=d.useContext(ye).getPrefixCls,f=c("icon-hover");return l.createElement("span",$i({className:K(f,(n={},n[i+"-icon-hover"]=i,n[f+"-size-"+o]=o&&o!=="default",n[f+"-disabled"]=a,n),r),onClick:e.onClick},u),t)}var rp={exports:{}},fi={exports:{}},hs;function np(){return hs||(hs=1,function(e,n){(function(t,r){e.exports=r()})(Ir,function(){var t=1e3,r=6e4,a=36e5,i="millisecond",s="second",o="minute",u="hour",c="day",f="week",v="month",m="quarter",p="year",g="date",h="Invalid Date",b=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,y=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,C={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(k){var L=["th","st","nd","rd"],S=k%100;return"["+k+(L[(S-20)%10]||L[S]||L[0])+"]"}},w=function(k,L,S){var N=String(k);return!N||N.length>=L?k:""+Array(L+1-N.length).join(S)+k},E={s:w,z:function(k){var L=-k.utcOffset(),S=Math.abs(L),N=Math.floor(S/60),A=S%60;return(L<=0?"+":"-")+w(N,2,"0")+":"+w(A,2,"0")},m:function k(L,S){if(L.date()<S.date())return-k(S,L);var N=12*(S.year()-L.year())+(S.month()-L.month()),A=L.clone().add(N,v),W=S-A<0,D=L.clone().add(N+(W?-1:1),v);return+(-(N+(S-A)/(W?A-D:D-A))||0)},a:function(k){return k<0?Math.ceil(k)||0:Math.floor(k)},p:function(k){return{M:v,y:p,w:f,d:c,D:g,h:u,m:o,s,ms:i,Q:m}[k]||String(k||"").toLowerCase().replace(/s$/,"")},u:function(k){return k===void 0}},O="en",x={};x[O]=C;var _="$isDayjsObject",T=function(k){return k instanceof M||!(!k||!k[_])},P=function k(L,S,N){var A;if(!L)return O;if(typeof L=="string"){var W=L.toLowerCase();x[W]&&(A=W),S&&(x[W]=S,A=W);var D=L.split("-");if(!A&&D.length>1)return k(D[0])}else{var F=L.name;x[F]=L,A=F}return!N&&A&&(O=A),A||!N&&O},I=function(k,L){if(T(k))return k.clone();var S=typeof L=="object"?L:{};return S.date=k,S.args=arguments,new M(S)},j=E;j.l=P,j.i=T,j.w=function(k,L){return I(k,{locale:L.$L,utc:L.$u,x:L.$x,$offset:L.$offset})};var M=function(){function k(S){this.$L=P(S.locale,null,!0),this.parse(S),this.$x=this.$x||S.x||{},this[_]=!0}var L=k.prototype;return L.parse=function(S){this.$d=function(N){var A=N.date,W=N.utc;if(A===null)return new Date(NaN);if(j.u(A))return new Date;if(A instanceof Date)return new Date(A);if(typeof A=="string"&&!/Z$/i.test(A)){var D=A.match(b);if(D){var F=D[2]-1||0,H=(D[7]||"0").substring(0,3);return W?new Date(Date.UTC(D[1],F,D[3]||1,D[4]||0,D[5]||0,D[6]||0,H)):new Date(D[1],F,D[3]||1,D[4]||0,D[5]||0,D[6]||0,H)}}return new Date(A)}(S),this.init()},L.init=function(){var S=this.$d;this.$y=S.getFullYear(),this.$M=S.getMonth(),this.$D=S.getDate(),this.$W=S.getDay(),this.$H=S.getHours(),this.$m=S.getMinutes(),this.$s=S.getSeconds(),this.$ms=S.getMilliseconds()},L.$utils=function(){return j},L.isValid=function(){return this.$d.toString()!==h},L.isSame=function(S,N){var A=I(S);return this.startOf(N)<=A&&A<=this.endOf(N)},L.isAfter=function(S,N){return I(S)<this.startOf(N)},L.isBefore=function(S,N){return this.endOf(N)<I(S)},L.$g=function(S,N,A){return j.u(S)?this[N]:this.set(A,S)},L.unix=function(){return Math.floor(this.valueOf()/1e3)},L.valueOf=function(){return this.$d.getTime()},L.startOf=function(S,N){var A=this,W=!!j.u(N)||N,D=j.p(S),F=function(Z,Y){var J=j.w(A.$u?Date.UTC(A.$y,Y,Z):new Date(A.$y,Y,Z),A);return W?J:J.endOf(c)},H=function(Z,Y){return j.w(A.toDate()[Z].apply(A.toDate("s"),(W?[0,0,0,0]:[23,59,59,999]).slice(Y)),A)},z=this.$W,B=this.$M,re=this.$D,V="set"+(this.$u?"UTC":"");switch(D){case p:return W?F(1,0):F(31,11);case v:return W?F(1,B):F(0,B+1);case f:var $=this.$locale().weekStart||0,X=(z<$?z+7:z)-$;return F(W?re-X:re+(6-X),B);case c:case g:return H(V+"Hours",0);case u:return H(V+"Minutes",1);case o:return H(V+"Seconds",2);case s:return H(V+"Milliseconds",3);default:return this.clone()}},L.endOf=function(S){return this.startOf(S,!1)},L.$set=function(S,N){var A,W=j.p(S),D="set"+(this.$u?"UTC":""),F=(A={},A[c]=D+"Date",A[g]=D+"Date",A[v]=D+"Month",A[p]=D+"FullYear",A[u]=D+"Hours",A[o]=D+"Minutes",A[s]=D+"Seconds",A[i]=D+"Milliseconds",A)[W],H=W===c?this.$D+(N-this.$W):N;if(W===v||W===p){var z=this.clone().set(g,1);z.$d[F](H),z.init(),this.$d=z.set(g,Math.min(this.$D,z.daysInMonth())).$d}else F&&this.$d[F](H);return this.init(),this},L.set=function(S,N){return this.clone().$set(S,N)},L.get=function(S){return this[j.p(S)]()},L.add=function(S,N){var A,W=this;S=Number(S);var D=j.p(N),F=function(B){var re=I(W);return j.w(re.date(re.date()+Math.round(B*S)),W)};if(D===v)return this.set(v,this.$M+S);if(D===p)return this.set(p,this.$y+S);if(D===c)return F(1);if(D===f)return F(7);var H=(A={},A[o]=r,A[u]=a,A[s]=t,A)[D]||1,z=this.$d.getTime()+S*H;return j.w(z,this)},L.subtract=function(S,N){return this.add(-1*S,N)},L.format=function(S){var N=this,A=this.$locale();if(!this.isValid())return A.invalidDate||h;var W=S||"YYYY-MM-DDTHH:mm:ssZ",D=j.z(this),F=this.$H,H=this.$m,z=this.$M,B=A.weekdays,re=A.months,V=A.meridiem,$=function(Y,J,ae,ve){return Y&&(Y[J]||Y(N,W))||ae[J].slice(0,ve)},X=function(Y){return j.s(F%12||12,Y,"0")},Z=V||function(Y,J,ae){var ve=Y<12?"AM":"PM";return ae?ve.toLowerCase():ve};return W.replace(y,function(Y,J){return J||function(ae){switch(ae){case"YY":return String(N.$y).slice(-2);case"YYYY":return j.s(N.$y,4,"0");case"M":return z+1;case"MM":return j.s(z+1,2,"0");case"MMM":return $(A.monthsShort,z,re,3);case"MMMM":return $(re,z);case"D":return N.$D;case"DD":return j.s(N.$D,2,"0");case"d":return String(N.$W);case"dd":return $(A.weekdaysMin,N.$W,B,2);case"ddd":return $(A.weekdaysShort,N.$W,B,3);case"dddd":return B[N.$W];case"H":return String(F);case"HH":return j.s(F,2,"0");case"h":return X(1);case"hh":return X(2);case"a":return Z(F,H,!0);case"A":return Z(F,H,!1);case"m":return String(H);case"mm":return j.s(H,2,"0");case"s":return String(N.$s);case"ss":return j.s(N.$s,2,"0");case"SSS":return j.s(N.$ms,3,"0");case"Z":return D}return null}(Y)||D.replace(":","")})},L.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},L.diff=function(S,N,A){var W,D=this,F=j.p(N),H=I(S),z=(H.utcOffset()-this.utcOffset())*r,B=this-H,re=function(){return j.m(D,H)};switch(F){case p:W=re()/12;break;case v:W=re();break;case m:W=re()/3;break;case f:W=(B-z)/6048e5;break;case c:W=(B-z)/864e5;break;case u:W=B/a;break;case o:W=B/r;break;case s:W=B/t;break;default:W=B}return A?W:j.a(W)},L.daysInMonth=function(){return this.endOf(v).$D},L.$locale=function(){return x[this.$L]},L.locale=function(S,N){if(!S)return this.$L;var A=this.clone(),W=P(S,N,!0);return W&&(A.$L=W),A},L.clone=function(){return j.w(this.$d,this)},L.toDate=function(){return new Date(this.valueOf())},L.toJSON=function(){return this.isValid()?this.toISOString():null},L.toISOString=function(){return this.$d.toISOString()},L.toString=function(){return this.$d.toUTCString()},k}(),R=M.prototype;return I.prototype=R,[["$ms",i],["$s",s],["$m",o],["$H",u],["$W",c],["$M",v],["$y",p],["$D",g]].forEach(function(k){R[k[1]]=function(L){return this.$g(L,k[0],k[1])}}),I.extend=function(k,L){return k.$i||(k(L,M,I),k.$i=!0),I},I.locale=P,I.isDayjs=T,I.unix=function(k){return I(1e3*k)},I.en=x[O],I.Ls=x,I.p={},I})}(fi)),fi.exports}(function(e,n){(function(t,r){e.exports=r(np())})(Ir,function(t){function r(s){return s&&typeof s=="object"&&"default"in s?s:{default:s}}var a=r(t),i={name:"zh-cn",weekdays:"星期日_星期一_星期二_星期三_星期四_星期五_星期六".split("_"),weekdaysShort:"周日_周一_周二_周三_周四_周五_周六".split("_"),weekdaysMin:"日_一_二_三_四_五_六".split("_"),months:"一月_二月_三月_四月_五月_六月_七月_八月_九月_十月_十一月_十二月".split("_"),monthsShort:"1月_2月_3月_4月_5月_6月_7月_8月_9月_10月_11月_12月".split("_"),ordinal:function(s,o){return o==="W"?s+"周":s+"日"},weekStart:1,yearStart:4,formats:{LT:"HH:mm",LTS:"HH:mm:ss",L:"YYYY/MM/DD",LL:"YYYY年M月D日",LLL:"YYYY年M月D日Ah点mm分",LLLL:"YYYY年M月D日ddddAh点mm分",l:"YYYY/M/D",ll:"YYYY年M月D日",lll:"YYYY年M月D日 HH:mm",llll:"YYYY年M月D日dddd HH:mm"},relativeTime:{future:"%s内",past:"%s前",s:"几秒",m:"1 分钟",mm:"%d 分钟",h:"1 小时",hh:"%d 小时",d:"1 天",dd:"%d 天",M:"1 个月",MM:"%d 个月",y:"1 年",yy:"%d 年"},meridiem:function(s,o){var u=100*s+o;return u<600?"凌晨":u<900?"早上":u<1100?"上午":u<1300?"中午":u<1800?"下午":"晚上"}};return a.default.locale(i,null,!0),i})})(rp);var ys={formatYear:"YYYY 年",formatMonth:"YYYY 年 MM 月",monthBeforeYear:!1,monthFormat:"M月",yearFormat:"YYYY年",today:"今天",view:{month:"月",year:"年",week:"周",day:"日"},month:{long:{January:"一月",February:"二月",March:"三月",April:"四月",May:"五月",June:"六月",July:"七月",August:"八月",September:"九月",October:"十月",November:"十一月",December:"十二月"},short:{January:"一月",February:"二月",March:"三月",April:"四月",May:"五月",June:"六月",July:"七月",August:"八月",September:"九月",October:"十月",November:"十一月",December:"十二月"}},week:{long:{self:"周",monday:"周一",tuesday:"周二",wednesday:"周三",thursday:"周四",friday:"周五",saturday:"周六",sunday:"周日"},short:{self:"周",monday:"一",tuesday:"二",wednesday:"三",thursday:"四",friday:"五",saturday:"六",sunday:"日"}}};const ap={locale:"zh-CN",dayjsLocale:"zh-cn",Calendar:ys,DatePicker:{Calendar:ys,placeholder:{date:"请选择日期",week:"请选择周",month:"请选择月份",year:"请选择年份",quarter:"请选择季度"},placeholders:{date:["开始日期","结束日期"],week:["开始周","结束周"],month:["开始月份","结束月份"],year:["开始年份","结束年份"],quarter:["开始季度","结束季度"]},selectTime:"选择时间",selectDate:"选择日期",today:"今天",now:"此刻",ok:"确定"},Drawer:{okText:"确定",cancelText:"取消"},Empty:{noData:"暂无数据"},Modal:{okText:"确定",cancelText:"取消"},Pagination:{goto:"前往",page:"页",countPerPage:"条/页",total:"共 {0} 条",prev:"上一页",next:"下一页",currentPage:"第 {0} 页",prevSomePages:"向前 {0} 页",nextSomePages:"向后 {0} 页",pageSize:"页码"},Popconfirm:{okText:"确定",cancelText:"取消"},Table:{okText:"确定",resetText:"重置",sortAscend:"点击升序",sortDescend:"点击降序",cancelSort:"取消排序"},TimePicker:{ok:"确定",placeholder:"请选择时间",placeholders:["开始时间","结束时间"],now:"此刻"},Progress:{success:"完成",error:"失败"},Upload:{start:"开始",cancel:"取消",delete:"删除",reupload:"点击重试",upload:"点击上传",preview:"预览",drag:"点击或拖拽文件到此处上传",dragHover:"释放文件并开始上传",error:"上传失败"},Typography:{copy:"复制",copied:"已复制",edit:"编辑",fold:"折叠",unfold:"展开"},Transfer:{resetText:"重置"},ImagePreview:{fullScreen:"全屏",rotateRight:"向右旋转",rotateLeft:"向左旋转",zoomIn:"放大",zoomOut:"缩小",originalSize:"原始尺寸"},Form:{validateMessages:{required:"#{field} 是必填项",type:{string:"#{field} 不是合法的文本类型",number:"#{field} 不是合法的数字类型",boolean:"#{field} 不是合法的布尔类型",array:"#{field} 不是合法的数组类型",object:"#{field} 不是合法的对象类型",url:"#{field} 不是合法的 url 地址",email:"#{field} 不是合法的邮箱地址",ip:"#{field} 不是合法的 IP 地址"},number:{min:"`#{value}` 小于最小值 `#{min}`",max:"`#{value}` 大于最大值 `#{max}`",equal:"`#{value}` 不等于 `#{equal}`",range:"`#{value}` 不在 `#{min} ~ #{max}` 范围内",positive:"`#{value}` 不是正数",negative:"`#{value}` 不是负数"},array:{length:"`#{field}` 个数不等于 #{length}",minLength:"`#{field}` 个数最少为 #{minLength}",maxLength:"`#{field}` 个数最多为 #{maxLength}",includes:"#{field} 不包含 #{includes}",deepEqual:"#{field} 不等于 #{deepEqual}",empty:"`#{field}` 不是空数组"},string:{minLength:"字符数最少为 #{minLength}",maxLength:"字符数最多为 #{maxLength}",length:"字符数必须是 #{length}",match:"`#{value}` 不符合模式 #{pattern}",uppercase:"`#{value}` 必须全大写",lowercase:"`#{value}` 必须全小写"},object:{deepEqual:"`#{field}` 不等于期望值",hasKeys:"`#{field}` 不包含必须字段",empty:"`#{field}` 不是对象"},boolean:{true:"期望是 `true`",false:"期望是 `false`"}}},ColorPicker:{history:"最近使用颜色",preset:"系统预设颜色",empty:"暂无"}};function bs(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function xs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?bs(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):bs(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function ip(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=xs(xs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-empty")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M24 5v6m7 1 4-4m-18 4-4-4m28.5 22H28s-1 3-4 3-4-3-4-3H6.5M40 41H8a2 2 0 0 1-2-2v-8.46a2 2 0 0 1 .272-1.007l6.15-10.54A2 2 0 0 1 14.148 18H33.85a2 2 0 0 1 1.728.992l6.149 10.541A2 2 0 0 1 42 30.541V39a2 2 0 0 1-2 2Z"}))}var il=l.forwardRef(ip);il.defaultProps={isIcon:!0};il.displayName="IconEmpty";const op=il;var Vi=globalThis&&globalThis.__assign||function(){return Vi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Vi.apply(this,arguments)};function Ge(e,n){var t=Vi({},e);return n.forEach(function(r){r in t&&delete t[r]}),t}var fa=globalThis&&globalThis.__assign||function(){return fa=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},fa.apply(this,arguments)};function He(e,n,t){var r=e._ignorePropsFromGlobal,a=d.useMemo(function(){return fa(fa({},n),r?{}:t)},[n,t,r]),i=d.useMemo(function(){var s=Ge(e,["_ignorePropsFromGlobal"]);for(var o in a)s[o]===void 0&&(s[o]=a[o]);return s},[e,a]);return i}var Hi=globalThis&&globalThis.__assign||function(){return Hi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Hi.apply(this,arguments)},lp=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function sp(e,n){var t=d.useContext(ye),r=t.getPrefixCls,a=t.locale,i=t.componentConfig,s=He(e,{},i==null?void 0:i.Empty),o=s.style,u=s.className,c=s.description,f=s.icon,v=s.imgSrc,m=lp(s,["style","className","description","icon","imgSrc"]),p=r("empty"),g=K(p,u),h=a.Empty.noData,b=typeof c=="string"?c:"empty";return l.createElement("div",Hi({ref:n,className:g,style:o},m),l.createElement("div",{className:p+"-wrapper"},l.createElement("div",{className:p+"-image"},v?l.createElement("img",{alt:b,src:v}):f||l.createElement(op,null)),l.createElement("div",{className:p+"-description"},c||h)))}var lf=d.forwardRef(sp);lf.displayName="Empty";const cp=d.memo(lf);var Wi=globalThis&&globalThis.__assign||function(){return Wi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Wi.apply(this,arguments)};function up(e){switch(e){default:return l.createElement(cp,null)}}var sf={locale:ap,prefixCls:"arco",getPopupContainer:function(){return document.body},size:"default",renderEmpty:up,focusLock:{modal:{autoFocus:!0},drawer:{autoFocus:!0}}},ye=d.createContext(Wi({getPrefixCls:function(e,n){return(n||"arco")+"-"+e}},sf)),fp=globalThis&&globalThis.__extends||function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(r[i]=a[i])},e(n,t)};return function(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(n,t);function r(){this.constructor=n}n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}}(),wn=globalThis&&globalThis.__assign||function(){return wn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},wn.apply(this,arguments)},dp=function(e){fp(n,e);function n(t){var r=e.call(this,t)||this;return r.getRootDOMNode=function(){return r.rootDOMRef.current},r.startTimer=function(){var a=r.props,i=a.duration,s=a.onClose,o=a.id;i!==0&&(r.timer=window.setTimeout(function(){s&&s(o),r.removeTimer()},i))},r.removeTimer=function(){r.timer&&(window.clearTimeout(r.timer),r.timer=null)},r.onClose=function(){r.props.onClose&&r.props.onClose(r.props.id)},r.renderIcon=function(){var a=r.props,i=a.showIcon,s=a.icon,o=a.type,u=a.prefixCls,c=a.iconPrefix,f;if(s)f=s;else if(i){switch(o){case"info":f=l.createElement(Xu,null);break;case"success":f=l.createElement(Fo,null);break;case"error":f=l.createElement(Zu,null);break;case"warning":f=l.createElement(Na,null);break;case"loading":f=l.createElement(Yr,null);break}f=l.createElement(We.Provider,{value:c?{prefixCls:c}:{}},f)}return l.createElement("span",{className:u+"-icon"},f)},r.onMouseEnter=function(){r.removeTimer()},r.onMouseLeave=function(){r.removeTimer(),r.startTimer()},r.rootDOMRef=d.createRef(),r}return n.prototype.componentDidMount=function(){this.startTimer()},n.prototype.componentDidUpdate=function(t){(t.duration!==this.props.duration||this.props.update)&&(this.removeTimer(),this.startTimer())},n.prototype.componentWillUnmount=function(){this.removeTimer()},n.prototype.render=function(){var t,r=this.props,a=r.title,i=r.content,s=r.showIcon,o=r.className,u=r.style,c=r.type,f=r.btn,v=r.icon,m=r.prefixCls,p=r.closable,g=r.noticeType,h=r.iconPrefix,b=r.rtl,y=r.closeIcon,C=r.classPrefixCls,w=K(m,m+"-"+c,(t={},t[m+"-closable"]=p,t[m+"-rtl"]=b,t),o),E="closable"in this.props?p:!0,O=s;c==="normal"&&!v&&(O=!1);var x=wn({},this.context);if(C&&(x.prefixCls=C),g==="message")return E=p,l.createElement(pa,wn({},x),l.createElement("div",{style:{textAlign:"center"},onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave,ref:this.rootDOMRef},l.createElement("div",{className:w,style:u,role:"alert"},O&&this.renderIcon(),l.createElement("span",{className:m+"-content"},i),E&&(y!==void 0?l.createElement("span",{onClick:this.onClose,className:m+"-close-btn"},y):l.createElement(Nt,{prefix:m,className:m+"-close-btn",onClick:this.onClose},l.createElement(Dt,null))))));if(g==="notification")return l.createElement(pa,wn({},x),l.createElement("div",{ref:this.rootDOMRef,onMouseEnter:this.onMouseEnter,onMouseLeave:this.onMouseLeave},l.createElement("div",{className:w,style:u,role:"alert"},O&&l.createElement("div",{className:m+"-left"},this.renderIcon()),l.createElement("div",{className:m+"-right"},a&&l.createElement("div",{className:m+"-title"},a),l.createElement("div",{className:m+"-content"},i),f&&l.createElement("div",{className:m+"-btn-wrapper"},f)),E&&(y!==void 0?l.createElement("span",{onClick:this.onClose,className:m+"-close-btn"},y):l.createElement(Nt,{prefix:m,className:m+"-close-btn",onClick:this.onClose},l.createElement(We.Provider,{value:h?{prefixCls:h}:{}},l.createElement(Dt,null)))))))},n.defaultProps={type:"info",showIcon:!0,noticeType:"message",duration:3e3},n.contextType=ye,n}(d.Component);const cf=dp;var Cs=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Es=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},vp=d.forwardRef(function(e,n){var t=d.useContext(ye),r=Cs(d.useState([]),2),a=r[0],i=r[1];function s(c){i(function(f){return Es(Es([],Cs(f),!1),[c],!1)})}function o(c){i(function(f){return f.filter(function(v){return c!==v})})}function u(){return t}return d.useImperativeHandle(n,function(){return{addInstance:s,removeInstance:o,getContextConfig:u}}),l.createElement(l.Fragment,null,l.Children.map(a,function(c,f){return l.cloneElement(c,{key:f})}))});const uf=vp;var vr=globalThis&&globalThis.__assign||function(){return vr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},vr.apply(this,arguments)};function pp(e){e===void 0&&(e={});var n=e.maxCount,t=e.duration,r=t===void 0?3e3:t,a=e.prefixCls,i=d.createRef(),s=l.createElement(uf,{ref:i}),o={},u;function c(v){var m,p;if(i.current){var g=i.current.getContextConfig();p=g.rtl,m=g.prefixCls}var h=a||m,b=vr({position:"top",duration:r},v),y=b.position,C=b.transitionClassNames,w;if(o[y]){var E=o[y].state.notices;if(E.length>=n){var O=E[0];w=O.id,E.shift(),o[y].add(vr(vr({},b),{id:w}))}else w=o[y].add(b)}else u=l.createElement(pf,{transitionClassNames:C,ref:function(_){o[y]=_,o[y]&&(w=o[y].add(b))},prefixCls:h,rtl:p}),i.current.addInstance(u);var x=function(){o[y]&&o[y].remove(w)};return x}var f={};return["info","success","warning","error","normal"].forEach(function(v){f[v]=function(m){var p=ar(m)?{content:m}:m;return c(vr(vr({},p),{type:v}))}}),[f,s]}var mp=globalThis&&globalThis.__extends||function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(r[i]=a[i])},e(n,t)};return function(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(n,t);function r(){this.constructor=n}n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}}(),xt=globalThis&&globalThis.__assign||function(){return xt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},xt.apply(this,arguments)},gp=["info","success","error","warning","loading","normal"],Tt={},ff,ws,df,Fi,Os,_s;function vf(e){var n=xt({position:"top",duration:df},e),t=n.position,r=n.transitionClassNames,a=n.transitionTimeout,i,s=Tt[t]||{},o=s.instance,u=s.pending;if(o||u){var c=function(){var m=(Tt[t]||{}).instance,p=m.state.notices,g=p.find(function(b){return b.id===e.id}),h=xt(xt({},n),{update:g});p.length>=ff?g?m.add(xt(xt({},h),{id:g.id})):(p.shift(),m.add(h)):i=m.add(h)};o?c():u!=null&&u.then&&u.then(function(){c(),Tt[t].pending=null})}else{var f=document.createElement("div");(Fi||document.body).appendChild(f),Tt[t]={},Tt[t].pending=new Promise(function(m){af(l.createElement(Wa,{transitionClassNames:r,transitionTimeout:a,ref:function(p){Tt[t]||(Tt[t]={}),Tt[t].instance=p,i=p.add(n),m(null)}}),f)})}var v=function(){var m,p;(p=(m=Tt[t])===null||m===void 0?void 0:m.instance)===null||p===void 0||p.remove(i)};return v}var Wa=function(e){mp(n,e);function n(){var t=e!==null&&e.apply(this,arguments)||this;return t.remove=function(r){var a=t.state.notices.find(function(i){return i.id===r});a&&t.update(xt(xt({},a),{style:xt(xt({},a.style),{opacity:0})})),setTimeout(function(){e.prototype.remove.call(t,r)},100)},t}return n.prototype.render=function(){var t=this,r=this.props,a=r.transitionClassNames,i=r.transitionTimeout,s=r.prefixCls,o=r.rtl,u=r.closable,c=this.state,f=c.notices,v=c.position,m=s||ws,p=ft(o)?Os:o,g=ft(u)?_s:u,h=m?m+"-message":"arco-message",b={enter:Ye(i==null?void 0:i.enter)?i==null?void 0:i.enter:100,exit:Ye(i==null?void 0:i.exit)?i==null?void 0:i.exit:300},y=K(h+"-wrapper",h+"-wrapper-"+v);return l.createElement("div",{className:y},l.createElement(rl,{component:null},f.map(function(C){return l.createElement(nr,{key:C.id,timeout:b,classNames:a||"fadeMessage",onExit:function(w){w&&(w.style.height=w.scrollHeight+"px")},onExiting:function(w){w&&(w.style.height=0)},onExited:function(w){w&&(w.style.height=0,C.onClose&&C.onClose())}},l.createElement(cf,xt({},C,{prefixCls:h,classPrefixCls:m,iconPrefix:m,onClose:t.remove,noticeType:"message",rtl:p},ft(g)?{}:{closable:g})))})))},n.config=function(t){t===void 0&&(t={}),Ye(t.maxCount)&&(ff=t.maxCount),t.prefixCls&&(ws=t.prefixCls),Ye(t.duration)&&(df=t.duration),typeof t.rtl=="boolean"&&(Os=t.rtl),typeof t.closable=="boolean"&&(_s=t.closable),t.getContainer&&t.getContainer()!==Fi&&(Fi=t.getContainer(),Object.values(Tt).forEach(function(r){var a=r.instance;return a==null?void 0:a.clear()}),Tt={})},n.clear=function(){Object.values(Tt).forEach(function(t){var r=t.instance;r==null||r.clear()})},n.addInstance=vf,n}(of);gp.forEach(function(e){Wa[e]=function(n){var t=typeof n=="string"?{content:n}:n;return vf(xt(xt({},t),{type:e}))}});Wa.useMessage=pp;const pf=Wa;var pr=globalThis&&globalThis.__assign||function(){return pr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},pr.apply(this,arguments)};function hp(e){e===void 0&&(e={});var n=e.maxCount,t=e.duration,r=t===void 0?3e3:t,a=e.prefixCls,i=e.getContainer,s=d.createRef(),o=l.createElement(uf,{ref:s}),u={},c;function f(m){var p,g;if(s.current){var h=s.current.getContextConfig();g=h.rtl,p=h.prefixCls}var b=a||p,y=m.position;ft(m.position)&&(y=g?"topLeft":"topRight");var C=pr({duration:r},m),w;if(u[y]){var E=u[y].state.notices;if(E.length>=n){var O=E[0];w=O.id,E.shift(),u[y].add(pr(pr({},C),{id:w}))}else w=u[y].add(C)}else c=l.createElement(mf,{ref:function(x){u[y]=x,u[y]&&(w=u[y].add(C))},prefixCls:b,rtl:g,getContainer:i}),s.current.addInstance(c);return u[y]}var v={};return["info","success","warning","error","normal"].forEach(function(m){v[m]=function(p){return f(pr(pr({},p),{type:m}))}}),[v,o]}var yp=globalThis&&globalThis.__extends||function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(r[i]=a[i])},e(n,t)};return function(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(n,t);function r(){this.constructor=n}n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}}(),gt=globalThis&&globalThis.__assign||function(){return gt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},gt.apply(this,arguments)},bp=["info","success","error","warning","normal"],Et={},Ps,Ts,Ss,di,Gn,da=function(e){yp(n,e);function n(){var t=e!==null&&e.apply(this,arguments)||this;return t.remove=function(r){var a=t.state.notices.find(function(i){return i.id===r});a&&t.update(gt(gt({},a),{style:gt(gt({},a.style),{opacity:0})})),setTimeout(function(){e.prototype.remove.call(t,r)},200)},t}return n.prototype.render=function(){var t,r=this,a=this.state.notices,i=this.props,s=i.prefixCls,o=i.rtl,u=i.getContainer,c=this.state.position,f=ft(o)?Gn:o;ft(c)&&(c=f?"topLeft":"topRight");var v=s||Ts,m=v?v+"-notification":"arco-notification",p;c==="topLeft"||c==="bottomLeft"?p="slideNoticeLeft":p="slideNoticeRight";var g=K(m+"-wrapper",m+"-wrapper-"+c,(t={},t[m+"-wrapper-rtl"]=Gn,t)),h=u==null?void 0:u(),b=l.createElement("div",{className:g},l.createElement(rl,{component:null},a.map(function(y){return l.createElement(nr,{key:y.id,timeout:{enter:400,exit:300},classNames:p,onExit:function(C){C&&(C.style.height=C.scrollHeight+"px")},onExiting:function(C){C&&(C.style.height=0)},onExited:function(C){C&&(C.style.height=0,y.onClose&&y.onClose())}},l.createElement(cf,gt({},y,{onClose:r.remove,prefixCls:m,iconPrefix:v,classPrefixCls:v,noticeType:"notification",rtl:f})))})));return h?pv.createPortal(b,h):b},n.config=function(t){t===void 0&&(t={}),Ye(t.maxCount)&&(Ps=t.maxCount),t.prefixCls&&(Ts=t.prefixCls),Ye(t.duration)&&(Ss=t.duration),typeof t.rtl=="boolean"&&(Gn=t.rtl),t.getContainer&&t.getContainer()!==di&&(di=t.getContainer(),Object.values(Et).forEach(function(r){var a=r.instance;return a==null?void 0:a.clear()}),Et={})},n.clear=function(){Object.values(Et).forEach(function(t){var r=t.instance;r==null||r.clear()})},n.remove=function(t){Object.values(Et).forEach(function(r){var a=r.instance;a==null||a.remove(t)})},n.addInstance=function(t){var r=t.position;ft(t.position)&&(r=Gn?"topLeft":"topRight");var a=gt({duration:Ss},t),i=Et[r]||{},s=i.instance,o=i.pending;if(s||o){var u=function(){var f=(Et[r]||{}).instance,v=f.state.notices,m=v.find(function(g){return g.id===t.id}),p=gt(gt({},a),{update:m});return v.length>=Ps?m?f.add(gt(gt({},p),{id:m.id})):(v.shift(),f.add(p)):f.add(gt({},p)),f};return s?u():o!=null&&o.then&&o.then(function(){u(),Et[r].pending=null}),s}var c=document.createElement("div");return(di||document.body).appendChild(c),Et[r]={},Et[r].pending=new Promise(function(f){af(l.createElement(n,{ref:function(v){return Et[r]||(Et[r]={}),Et[r].instance=v,v.add(a),f(null),v}}),c)}),Et[r].instance},n}(of);bp.forEach(function(e){da[e]=function(n){return da.addInstance(gt(gt({},n),{type:e}))}});da.useNotification=hp;const mf=da;var Bi=globalThis&&globalThis.__assign||function(){return Bi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Bi.apply(this,arguments)};function xp(e){Bi({},e)}var va=globalThis&&globalThis.__assign||function(){return va=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},va.apply(this,arguments)},Un={primaryColor:{default:"--arcoblue-6",hover:"--arcoblue-5",active:"--arcoblue-7"},successColor:{default:"--green-6",hover:"--green-5",active:"--green-7"},infoColor:{default:"--arcoblue-6",hover:"--arcoblue-5",active:"--arcoblue-7"},warningColor:{default:"--orangered-6",hover:"--orangered-5",active:"--orangered-7"},dangerColor:{default:"--red-6",hover:"--red-5",active:"--red-7"}};function Cp(e){if(e&&xe(e)){var n=document.body;Object.keys(Un).forEach(function(t){e[t]&&(n.style.setProperty(Un[t].default,ci(e[t],0)),e[t+"Hover"]||n.style.setProperty(Un[t].hover,ci(e[t],10)),e[t+"Active"]||n.style.setProperty(Un[t].active,ci(e[t],-10)))})}}var Ep=sf,wp={};function pa(e){var n=He(e,Ep,wp),t=n.theme,r=n.prefixCls,a=n.children,i=n.locale,s=n.rtl,o=n.effectGlobalNotice,u=o===void 0?!0:o,c=n.effectGlobalModal,f=c===void 0?!0:c;d.useEffect(function(){Cp(t)},[t]),d.useEffect(function(){u&&(pf.config({prefixCls:r,rtl:s}),mf.config({prefixCls:r,rtl:s}))},[r,s,u]);function v(g,h){return(h||r)+"-"+g}var m=va(va({},Ge(n,["children"])),{getPrefixCls:v});d.useEffect(function(){f&&xp({locale:i,prefixCls:r,rtl:s})},[i,r,s,f]);var p=a;return r&&r!=="arco"&&(p=l.createElement(We.Provider,{value:{prefixCls:r}},a)),l.createElement(ye.Provider,{value:m},p)}pa.ConfigContext=ye;pa.displayName="ConfigProvider";ye.Consumer;var Ki=globalThis&&globalThis.__assign||function(){return Ki=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ki.apply(this,arguments)},Op=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},_p=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Pp={showIcon:!0,type:"info"};function Tp(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,Pp,i==null?void 0:i.Alert),u=o.style,c=o.className,f=o.action,v=o.type,m=v===void 0?"info":v,p=o.title,g=o.content,h=o.icon,b=o.showIcon,y=o.closable,C=o.closeable,w=o.afterClose,E=o.onClose,O=o.closeElement,x=o.banner,_=Op(o,["style","className","action","type","title","content","icon","showIcon","closable","closeable","afterClose","onClose","closeElement","banner"]),T=a("alert"),P=_p(d.useState(!0),2),I=P[0],j=P[1];function M(S){if(h)return h;switch(S){case"info":return l.createElement(Xu,null);case"success":return l.createElement(Fo,null);case"warning":return l.createElement(Na,null);case"error":return l.createElement(Zu,null);default:return null}}function R(S){j(!1),E==null||E(S)}var k=K(T,T+"-"+m,(t={},t[T+"-with-title"]=p,t[T+"-banner"]=x,t[T+"-rtl"]=s,t),c),L="closeable"in o?C:y;return l.createElement(nr,{in:I,timeout:300,classNames:"zoomInTop",unmountOnExit:!0,onExited:function(){w==null||w()}},l.createElement("div",Ki({ref:n,style:u,className:k,role:"alert"},_),b&&l.createElement("div",{className:T+"-icon-wrapper"},M(m)),l.createElement("div",{className:T+"-content-wrapper"},p&&l.createElement("div",{className:T+"-title"},p),g&&l.createElement("div",{className:T+"-content"},g)),f&&l.createElement("div",{className:T+"-action"},f),L&&l.createElement("button",{type:"button",onClick:R,className:T+"-close-btn"},O||l.createElement(Dt,null))))}var gf=d.forwardRef(Tp);gf.displayName="Alert";const Q6=gf;function Sp(e){var n=typeof e;return e!=null&&(n=="object"||n=="function")}var Hn=Sp,Np=typeof Ir=="object"&&Ir&&Ir.Object===Object&&Ir,hf=Np,Ip=hf,jp=typeof self=="object"&&self&&self.Object===Object&&self,Mp=Ip||jp||Function("return this")(),zt=Mp,Rp=zt,kp=function(){return Rp.Date.now()},Ap=kp,Dp=/\s/;function Lp(e){for(var n=e.length;n--&&Dp.test(e.charAt(n)););return n}var zp=Lp,$p=zp,Vp=/^\s+/;function Hp(e){return e&&e.slice(0,$p(e)+1).replace(Vp,"")}var Wp=Hp,Fp=zt,Bp=Fp.Symbol,Fa=Bp,Ns=Fa,yf=Object.prototype,Kp=yf.hasOwnProperty,Gp=yf.toString,dn=Ns?Ns.toStringTag:void 0;function Up(e){var n=Kp.call(e,dn),t=e[dn];try{e[dn]=void 0;var r=!0}catch{}var a=Gp.call(e);return r&&(n?e[dn]=t:delete e[dn]),a}var Zp=Up,Xp=Object.prototype,Yp=Xp.toString;function qp(e){return Yp.call(e)}var Jp=qp,Is=Fa,Qp=Zp,em=Jp,tm="[object Null]",rm="[object Undefined]",js=Is?Is.toStringTag:void 0;function nm(e){return e==null?e===void 0?rm:tm:js&&js in Object(e)?Qp(e):em(e)}var Wn=nm;function am(e){return e!=null&&typeof e=="object"}var Fn=am,im=Wn,om=Fn,lm="[object Symbol]";function sm(e){return typeof e=="symbol"||om(e)&&im(e)==lm}var Ba=sm,cm=Wp,Ms=Hn,um=Ba,Rs=0/0,fm=/^[-+]0x[0-9a-f]+$/i,dm=/^0b[01]+$/i,vm=/^0o[0-7]+$/i,pm=parseInt;function mm(e){if(typeof e=="number")return e;if(um(e))return Rs;if(Ms(e)){var n=typeof e.valueOf=="function"?e.valueOf():e;e=Ms(n)?n+"":n}if(typeof e!="string")return e===0?e:+e;e=cm(e);var t=dm.test(e);return t||vm.test(e)?pm(e.slice(2),t?2:8):fm.test(e)?Rs:+e}var gm=mm,hm=Hn,vi=Ap,ks=gm,ym="Expected a function",bm=Math.max,xm=Math.min;function Cm(e,n,t){var r,a,i,s,o,u,c=0,f=!1,v=!1,m=!0;if(typeof e!="function")throw new TypeError(ym);n=ks(n)||0,hm(t)&&(f=!!t.leading,v="maxWait"in t,i=v?bm(ks(t.maxWait)||0,n):i,m="trailing"in t?!!t.trailing:m);function p(x){var _=r,T=a;return r=a=void 0,c=x,s=e.apply(T,_),s}function g(x){return c=x,o=setTimeout(y,n),f?p(x):s}function h(x){var _=x-u,T=x-c,P=n-_;return v?xm(P,i-T):P}function b(x){var _=x-u,T=x-c;return u===void 0||_>=n||_<0||v&&T>=i}function y(){var x=vi();if(b(x))return C(x);o=setTimeout(y,h(x))}function C(x){return o=void 0,m&&r?p(x):(r=a=void 0,s)}function w(){o!==void 0&&clearTimeout(o),c=0,r=u=a=o=void 0}function E(){return o===void 0?s:C(vi())}function O(){var x=vi(),_=b(x);if(r=arguments,a=this,u=x,_){if(o===void 0)return g(u);if(v)return clearTimeout(o),o=setTimeout(y,n),p(u)}return o===void 0&&(o=setTimeout(y,n)),s}return O.cancel=w,O.flush=E,O}var bf=Cm;const Em=Vn(bf);var wm=bf,Om=Hn,_m="Expected a function";function Pm(e,n,t){var r=!0,a=!0;if(typeof e!="function")throw new TypeError(_m);return Om(t)&&(r="leading"in t?!!t.leading:r,a="trailing"in t?!!t.trailing:a),wm(e,n,{leading:r,maxWait:n,trailing:a})}var Tm=Pm;const ol=Vn(Tm);function As(e){return typeof e=="object"&&e!=null&&e.nodeType===1}function Ds(e,n){return(!n||e!=="hidden")&&e!=="visible"&&e!=="clip"}function pi(e,n){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var t=getComputedStyle(e,null);return Ds(t.overflowY,n)||Ds(t.overflowX,n)||function(r){var a=function(i){if(!i.ownerDocument||!i.ownerDocument.defaultView)return null;try{return i.ownerDocument.defaultView.frameElement}catch{return null}}(r);return!!a&&(a.clientHeight<r.scrollHeight||a.clientWidth<r.scrollWidth)}(e)}return!1}function Zn(e,n,t,r,a,i,s,o){return i<e&&s>n||i>e&&s<n?0:i<=e&&o<=t||s>=n&&o>=t?i-e-r:s>n&&o<t||i<e&&o>t?s-n+a:0}var Ls=function(e,n){var t=window,r=n.scrollMode,a=n.block,i=n.inline,s=n.boundary,o=n.skipOverflowHiddenElements,u=typeof s=="function"?s:function(ne){return ne!==s};if(!As(e))throw new TypeError("Invalid target");for(var c,f,v=document.scrollingElement||document.documentElement,m=[],p=e;As(p)&&u(p);){if((p=(f=(c=p).parentElement)==null?c.getRootNode().host||null:f)===v){m.push(p);break}p!=null&&p===document.body&&pi(p)&&!pi(document.documentElement)||p!=null&&pi(p,o)&&m.push(p)}for(var g=t.visualViewport?t.visualViewport.width:innerWidth,h=t.visualViewport?t.visualViewport.height:innerHeight,b=window.scrollX||pageXOffset,y=window.scrollY||pageYOffset,C=e.getBoundingClientRect(),w=C.height,E=C.width,O=C.top,x=C.right,_=C.bottom,T=C.left,P=a==="start"||a==="nearest"?O:a==="end"?_:O+w/2,I=i==="center"?T+E/2:i==="end"?x:T,j=[],M=0;M<m.length;M++){var R=m[M],k=R.getBoundingClientRect(),L=k.height,S=k.width,N=k.top,A=k.right,W=k.bottom,D=k.left;if(r==="if-needed"&&O>=0&&T>=0&&_<=h&&x<=g&&O>=N&&_<=W&&T>=D&&x<=A)return j;var F=getComputedStyle(R),H=parseInt(F.borderLeftWidth,10),z=parseInt(F.borderTopWidth,10),B=parseInt(F.borderRightWidth,10),re=parseInt(F.borderBottomWidth,10),V=0,$=0,X="offsetWidth"in R?R.offsetWidth-R.clientWidth-H-B:0,Z="offsetHeight"in R?R.offsetHeight-R.clientHeight-z-re:0,Y="offsetWidth"in R?R.offsetWidth===0?0:S/R.offsetWidth:0,J="offsetHeight"in R?R.offsetHeight===0?0:L/R.offsetHeight:0;if(v===R)V=a==="start"?P:a==="end"?P-h:a==="nearest"?Zn(y,y+h,h,z,re,y+P,y+P+w,w):P-h/2,$=i==="start"?I:i==="center"?I-g/2:i==="end"?I-g:Zn(b,b+g,g,H,B,b+I,b+I+E,E),V=Math.max(0,V+y),$=Math.max(0,$+b);else{V=a==="start"?P-N-z:a==="end"?P-W+re+Z:a==="nearest"?Zn(N,W,L,z,re+Z,P,P+w,w):P-(N+L/2)+Z/2,$=i==="start"?I-D-H:i==="center"?I-(D+S/2)+X/2:i==="end"?I-A+B+X:Zn(D,A,S,H,B+X,I,I+E,E);var ae=R.scrollLeft,ve=R.scrollTop;P+=ve-(V=Math.max(0,Math.min(ve+V/J,R.scrollHeight-L/J+Z))),I+=ae-($=Math.max(0,Math.min(ae+$/Y,R.scrollWidth-S/Y+X)))}j.push({el:R,top:V,left:$})}return j};function xf(e){return e===Object(e)&&Object.keys(e).length!==0}function Sm(e,n){n===void 0&&(n="auto");var t="scrollBehavior"in document.body.style;e.forEach(function(r){var a=r.el,i=r.top,s=r.left;a.scroll&&t?a.scroll({top:i,left:s,behavior:n}):(a.scrollTop=i,a.scrollLeft=s)})}function Nm(e){return e===!1?{block:"end",inline:"nearest"}:xf(e)?e:{block:"start",inline:"nearest"}}function Cf(e,n){var t=e.isConnected||e.ownerDocument.documentElement.contains(e);if(xf(n)&&typeof n.behavior=="function")return n.behavior(t?Ls(e,n):[]);if(t){var r=Nm(n);return Sm(Ls(e,r),r.behavior)}}function Ka(e,n){var t={};return n.forEach(function(r){var a=r;r in e&&(t[a]=e[a])}),t}function ir(e){var n={};return e&&Object.keys(e).forEach(function(t){var r=String(t);r.indexOf("data-")===0&&(n[r]=e[r]),r.indexOf("aria-")===0&&(n[r]=e[r])}),n}var Ef=function(){};function Im(e){return Ka(e,["onMouseEnter","onMouseLeave","onMouseMove","onContextMenu","onClick","onFocus","onBlur","tabIndex"])}var qr=function(){try{return!(typeof window<"u"&&document!==void 0)}catch{return!0}}(),it=function(){return qr?Ef:function(e,n,t,r){e&&e.addEventListener(n,t,r||!1)}}(),ut=function(){return qr?Ef:function(e,n,t,r){e&&e.removeEventListener(n,t,r||!1)}}(),ma=function(e,n){if(!e)return!1;if(e.contains)return e.contains(n);for(var t=n;t;){if(t===e)return!0;t=t.parentNode}return!1},wf=function(e){var n=e===document.documentElement?e.clientHeight:e.offsetHeight,t=e===document.documentElement?e.clientWidth:e.offsetWidth;return e.scrollHeight>n||e.scrollWidth>t},jm=function(e,n){n===void 0&&(n=document.documentElement);for(var t=[],r=e;r&&r!==n;)wf(r)&&t.push(r),r=r.parentElement;return t};function ll(){var e=d.useRef(!0);return d.useEffect(function(){e.current=!1},[]),e.current}var xr=typeof window>"u"?global:window,Mm=["webkit","ms","moz","o"],Ct=xr.requestAnimationFrame,ht=xr.cancelAnimationFrame;if((!Ct||!ht)&&(Mm.some(function(e){return Ct=xr[e+"RequestAnimationFrame"],ht=xr[e+"CancelAnimationFrame"]||xr[e+"CancelRequestAnimationFrame"],Ct&&ht}),!Ct||!ht)){var zs=0;Ct=function(e){var n=Date.now(),t=Math.max(0,16-(n-zs)),r=setTimeout(function(){e(),zs=n+t},t);return r},ht=function(e){clearTimeout(e)}}Ct=Ct.bind(xr);ht=ht.bind(xr);var Rm=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},km=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function Dn(e){var n=null,t=function(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];n&&ht(n),n=Ct(function(){e.apply(void 0,km([],Rm(r),!1)),n=null})};return t.cancel=function(){ht(n),n=null},t}var Of=function(){if(typeof Map<"u")return Map;function e(n,t){var r=-1;return n.some(function(a,i){return a[0]===t?(r=i,!0):!1}),r}return function(){function n(){this.__entries__=[]}return Object.defineProperty(n.prototype,"size",{get:function(){return this.__entries__.length},enumerable:!0,configurable:!0}),n.prototype.get=function(t){var r=e(this.__entries__,t),a=this.__entries__[r];return a&&a[1]},n.prototype.set=function(t,r){var a=e(this.__entries__,t);~a?this.__entries__[a][1]=r:this.__entries__.push([t,r])},n.prototype.delete=function(t){var r=this.__entries__,a=e(r,t);~a&&r.splice(a,1)},n.prototype.has=function(t){return!!~e(this.__entries__,t)},n.prototype.clear=function(){this.__entries__.splice(0)},n.prototype.forEach=function(t,r){r===void 0&&(r=null);for(var a=0,i=this.__entries__;a<i.length;a++){var s=i[a];t.call(r,s[1],s[0])}},n}()}(),Gi=typeof window<"u"&&typeof document<"u"&&window.document===document,ga=function(){return typeof global<"u"&&global.Math===Math?global:typeof self<"u"&&self.Math===Math?self:typeof window<"u"&&window.Math===Math?window:Function("return this")()}(),Am=function(){return typeof requestAnimationFrame=="function"?requestAnimationFrame.bind(ga):function(e){return setTimeout(function(){return e(Date.now())},1e3/60)}}(),Dm=2;function Lm(e,n){var t=!1,r=!1,a=0;function i(){t&&(t=!1,e()),r&&o()}function s(){Am(i)}function o(){var u=Date.now();if(t){if(u-a<Dm)return;r=!0}else t=!0,r=!1,setTimeout(s,n);a=u}return o}var zm=20,$m=["top","right","bottom","left","width","height","size","weight"],Vm=typeof MutationObserver<"u",Hm=function(){function e(){this.connected_=!1,this.mutationEventsAdded_=!1,this.mutationsObserver_=null,this.observers_=[],this.onTransitionEnd_=this.onTransitionEnd_.bind(this),this.refresh=Lm(this.refresh.bind(this),zm)}return e.prototype.addObserver=function(n){~this.observers_.indexOf(n)||this.observers_.push(n),this.connected_||this.connect_()},e.prototype.removeObserver=function(n){var t=this.observers_,r=t.indexOf(n);~r&&t.splice(r,1),!t.length&&this.connected_&&this.disconnect_()},e.prototype.refresh=function(){var n=this.updateObservers_();n&&this.refresh()},e.prototype.updateObservers_=function(){var n=this.observers_.filter(function(t){return t.gatherActive(),t.hasActive()});return n.forEach(function(t){return t.broadcastActive()}),n.length>0},e.prototype.connect_=function(){!Gi||this.connected_||(document.addEventListener("transitionend",this.onTransitionEnd_),window.addEventListener("resize",this.refresh),Vm?(this.mutationsObserver_=new MutationObserver(this.refresh),this.mutationsObserver_.observe(document,{attributes:!0,childList:!0,characterData:!0,subtree:!0})):(document.addEventListener("DOMSubtreeModified",this.refresh),this.mutationEventsAdded_=!0),this.connected_=!0)},e.prototype.disconnect_=function(){!Gi||!this.connected_||(document.removeEventListener("transitionend",this.onTransitionEnd_),window.removeEventListener("resize",this.refresh),this.mutationsObserver_&&this.mutationsObserver_.disconnect(),this.mutationEventsAdded_&&document.removeEventListener("DOMSubtreeModified",this.refresh),this.mutationsObserver_=null,this.mutationEventsAdded_=!1,this.connected_=!1)},e.prototype.onTransitionEnd_=function(n){var t=n.propertyName,r=t===void 0?"":t,a=$m.some(function(i){return!!~r.indexOf(i)});a&&this.refresh()},e.getInstance=function(){return this.instance_||(this.instance_=new e),this.instance_},e.instance_=null,e}(),_f=function(e,n){for(var t=0,r=Object.keys(n);t<r.length;t++){var a=r[t];Object.defineProperty(e,a,{value:n[a],enumerable:!1,writable:!1,configurable:!0})}return e},Kr=function(e){var n=e&&e.ownerDocument&&e.ownerDocument.defaultView;return n||ga},Pf=Ga(0,0,0,0);function ha(e){return parseFloat(e)||0}function $s(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];return n.reduce(function(r,a){var i=e["border-"+a+"-width"];return r+ha(i)},0)}function Wm(e){for(var n=["top","right","bottom","left"],t={},r=0,a=n;r<a.length;r++){var i=a[r],s=e["padding-"+i];t[i]=ha(s)}return t}function Fm(e){var n=e.getBBox();return Ga(0,0,n.width,n.height)}function Bm(e){var n=e.clientWidth,t=e.clientHeight;if(!n&&!t)return Pf;var r=Kr(e).getComputedStyle(e),a=Wm(r),i=a.left+a.right,s=a.top+a.bottom,o=ha(r.width),u=ha(r.height);if(r.boxSizing==="border-box"&&(Math.round(o+i)!==n&&(o-=$s(r,"left","right")+i),Math.round(u+s)!==t&&(u-=$s(r,"top","bottom")+s)),!Gm(e)){var c=Math.round(o+i)-n,f=Math.round(u+s)-t;Math.abs(c)!==1&&(o-=c),Math.abs(f)!==1&&(u-=f)}return Ga(a.left,a.top,o,u)}var Km=function(){return typeof SVGGraphicsElement<"u"?function(e){return e instanceof Kr(e).SVGGraphicsElement}:function(e){return e instanceof Kr(e).SVGElement&&typeof e.getBBox=="function"}}();function Gm(e){return e===Kr(e).document.documentElement}function Um(e){return Gi?Km(e)?Fm(e):Bm(e):Pf}function Zm(e){var n=e.x,t=e.y,r=e.width,a=e.height,i=typeof DOMRectReadOnly<"u"?DOMRectReadOnly:Object,s=Object.create(i.prototype);return _f(s,{x:n,y:t,width:r,height:a,top:t,right:n+r,bottom:a+t,left:n}),s}function Ga(e,n,t,r){return{x:e,y:n,width:t,height:r}}var Xm=function(){function e(n){this.broadcastWidth=0,this.broadcastHeight=0,this.contentRect_=Ga(0,0,0,0),this.target=n}return e.prototype.isActive=function(){var n=Um(this.target);return this.contentRect_=n,n.width!==this.broadcastWidth||n.height!==this.broadcastHeight},e.prototype.broadcastRect=function(){var n=this.contentRect_;return this.broadcastWidth=n.width,this.broadcastHeight=n.height,n},e}(),Ym=function(){function e(n,t){var r=Zm(t);_f(this,{target:n,contentRect:r})}return e}(),qm=function(){function e(n,t,r){if(this.activeObservations_=[],this.observations_=new Of,typeof n!="function")throw new TypeError("The callback provided as parameter 1 is not a function.");this.callback_=n,this.controller_=t,this.callbackCtx_=r}return e.prototype.observe=function(n){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(n instanceof Kr(n).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(n)||(t.set(n,new Xm(n)),this.controller_.addObserver(this),this.controller_.refresh())}},e.prototype.unobserve=function(n){if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");if(!(typeof Element>"u"||!(Element instanceof Object))){if(!(n instanceof Kr(n).Element))throw new TypeError('parameter 1 is not of type "Element".');var t=this.observations_;t.has(n)&&(t.delete(n),t.size||this.controller_.removeObserver(this))}},e.prototype.disconnect=function(){this.clearActive(),this.observations_.clear(),this.controller_.removeObserver(this)},e.prototype.gatherActive=function(){var n=this;this.clearActive(),this.observations_.forEach(function(t){t.isActive()&&n.activeObservations_.push(t)})},e.prototype.broadcastActive=function(){if(this.hasActive()){var n=this.callbackCtx_,t=this.activeObservations_.map(function(r){return new Ym(r.target,r.broadcastRect())});this.callback_.call(n,t,n),this.clearActive()}},e.prototype.clearActive=function(){this.activeObservations_.splice(0)},e.prototype.hasActive=function(){return this.activeObservations_.length>0},e}(),Tf=typeof WeakMap<"u"?new WeakMap:new Of,Sf=function(){function e(n){if(!(this instanceof e))throw new TypeError("Cannot call a class as a function.");if(!arguments.length)throw new TypeError("1 argument required, but only 0 present.");var t=Hm.getInstance(),r=new qm(n,t,this);Tf.set(this,r)}return e}();["observe","unobserve","disconnect"].forEach(function(e){Sf.prototype[e]=function(){var n;return(n=Tf.get(this))[e].apply(n,arguments)}});var Nf=function(){return typeof ga.ResizeObserver<"u"?ga.ResizeObserver:Sf}(),Jm=globalThis&&globalThis.__extends||function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(r[i]=a[i])},e(n,t)};return function(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(n,t);function r(){this.constructor=n}n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}}(),Qm=function(e){Jm(n,e);function n(){var t=e!==null&&e.apply(this,arguments)||this;return t.getRootElement=function(){var r=t.props.getTargetDOMNode;return Ha((r==null?void 0:r())||t.rootDOMRef,t)},t.getRootDOMNode=function(){return t.getRootElement()},t.componentWillUnmount=function(){t.resizeObserver&&t.destroyResizeObserver()},t.createResizeObserver=function(){var r=t.props.throttle,a=r===void 0?!0:r,i=function(c){var f,v;(v=(f=t.props).onResize)===null||v===void 0||v.call(f,c)},s=a?ol(i):i,o=!0;t.resizeObserver=new Nf(function(c){o&&(o=!1,i(c)),s(c)});var u=t.getRootElement();u&&t.resizeObserver.observe(u)},t.destroyResizeObserver=function(){t.resizeObserver&&t.resizeObserver.disconnect(),t.resizeObserver=null},t}return n.prototype.componentDidMount=function(){l.isValidElement(this.props.children)?this.createResizeObserver():console.warn("The children of ResizeObserver is invalid.")},n.prototype.componentDidUpdate=function(){!this.resizeObserver&&this.getRootElement()&&this.createResizeObserver()},n.prototype.render=function(){var t=this,r=this.props.children;return Yo(r)&&d.isValidElement(r)&&!this.props.getTargetDOMNode?d.cloneElement(r,{ref:function(a){t.rootDOMRef=a,nl(r,a)}}):(this.rootDOMRef=null,this.props.children)},n}(l.Component);const pt=Qm;var eg=qr?d.useEffect:d.useLayoutEffect;const Fr=eg;var tg=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function If(e){var n=tg(d.useState({value:e,resolve:function(a){}}),2),t=n[0],r=n[1];return d.useEffect(function(){t.resolve(t.value)},[t]),[t.value,function(a){return new Promise(function(i){r(function(s){var o=a;return typeof a=="function"&&(o=a(s.value)),{value:o,resolve:i}})})}]}var Ui=globalThis&&globalThis.__assign||function(){return Ui=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ui.apply(this,arguments)},rg=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function ng(e,n){var t=e.className,r=e.style,a=e.children,i=rg(e,["className","style","children"]),s=d.useContext(ye).getPrefixCls,o=s("btn-group"),u=K(o,t);return l.createElement("div",Ui({ref:n,className:u,style:r},i),a)}var jf=l.forwardRef(ng);jf.displayName="ButtonGroup";const ag=jf;var On=globalThis&&globalThis.__assign||function(){return On=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},On.apply(this,arguments)},ig=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},og=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},lg=/^[\u4e00-\u9fa5]{2}$/;function sg(e){var n=[],t=!1;return l.Children.forEach(e,function(r){var a=typeof r=="string"||typeof r=="number";if(a&&t){var i=n.length-1,s=n[i];n[i]=""+s+r}else n.push(r);t=a}),l.Children.map(n,function(r){return typeof r=="string"?l.createElement("span",null,r):r})}var cg={htmlType:"button",type:"default",shape:"square"};function ug(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.size,s=r.autoInsertSpaceInButton,o=r.componentConfig,u=r.rtl,c=He(e,cg,o==null?void 0:o.Button),f=c.style,v=c.className,m=c.children,p=c.htmlType,g=c.type,h=c.status,b=c.size,y=c.shape,C=c.href,w=c.anchorProps,E=c.disabled,O=c.loading,x=c.loadingFixedWidth,_=c.icon,T=c.iconOnly,P=c.onClick,I=c.long,j=ig(c,["style","className","children","htmlType","type","status","size","shape","href","anchorProps","disabled","loading","loadingFixedWidth","icon","iconOnly","onClick","long"]),M=O?l.createElement(Yr,null):_,R=og(d.useState(!1),2),k=R[0],L=R[1],S=d.useRef(),N=n||S;d.useEffect(function(){if(s&&N&&N.current){var B=N.current.textContent;lg.test(B)?k||L(!0):k&&L(!1)}},[N.current,s]);var A=a("btn"),W=g==="default"?"secondary":g,D=K(A,A+"-"+W,A+"-size-"+(b||i),A+"-shape-"+y,(t={},t[A+"-long"]=I,t[A+"-status-"+h]=h,t[A+"-loading-fixed-width"]=x,t[A+"-loading"]=O,t[A+"-link"]=C,t[A+"-icon-only"]=T||!m&&m!==0&&M,t[A+"-disabled"]=E,t[A+"-two-chinese-chars"]=k,t[A+"-rtl"]=u,t),v),F=function(B){if(O||E){typeof(B==null?void 0:B.preventDefault)=="function"&&B.preventDefault();return}P&&P(B)},H=l.createElement(l.Fragment,null,M,sg(m));if(C){var z=On({},w);return E?delete z.href:z.href=C,l.createElement("a",On({ref:N},j,z,{style:f,className:D,onClick:F}),H)}return l.createElement("button",On({ref:N},j,{style:f,className:D,type:p,disabled:E,onClick:F}),H)}var fg=d.forwardRef(ug),Ua=fg;Ua.__BYTE_BUTTON=!0;Ua.Group=ag;Ua.displayName="Button";const ia=Ua;function Vs(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Hs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Vs(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Vs(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function dg(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Hs(Hs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-search")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M33.072 33.071c6.248-6.248 6.248-16.379 0-22.627-6.249-6.249-16.38-6.249-22.628 0-6.248 6.248-6.248 16.379 0 22.627 6.248 6.248 16.38 6.248 22.628 0Zm0 0 8.485 8.485"}))}var sl=l.forwardRef(dg);sl.defaultProps={isIcon:!0};sl.displayName="IconSearch";const Zi=sl;function cl(e){var n=d.useRef();return d.useEffect(function(){n.current=e}),n.current}var vg=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function ot(e,n){var t=n||{},r=t.defaultValue,a=t.value,i=d.useRef(!0),s=cl(a),o=vg(d.useState(ft(a)?ft(r)?e:r:a),2),u=o[0],c=o[1];d.useEffect(function(){if(i.current){i.current=!1;return}a===void 0&&s!==a&&c(a)},[a]);var f=ft(a)?u:a;return[f,c,u]}var Xi=globalThis&&globalThis.__assign||function(){return Xi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Xi.apply(this,arguments)},pg=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},mg=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Mf=l.forwardRef(function(e,n){var t,r=d.useContext(ye).getPrefixCls,a=e.className,i=e.style,s=e.placeholder,o=e.disabled,u=e.searchButton,c=e.loading,f=e.defaultValue,v=e.addAfter,m=e.suffix,p=pg(e,["className","style","placeholder","disabled","searchButton","loading","defaultValue","addAfter","suffix"]),g=xe(e.maxLength)?e.maxLength.length:e.maxLength,h=xe(e.maxLength)&&e.maxLength.errorOnly?void 0:g,b=mg(ot("",{defaultValue:"defaultValue"in e?Gr(e.defaultValue,h):void 0,value:"value"in e?Gr(e.value,h):void 0}),2),y=b[0],C=b[1],w=r("input-search"),E=K(w,(t={},t[w+"-button"]=u,t),a),O=function(){!o&&e.onSearch&&e.onSearch(y)};return l.createElement(Za,Xi({},Ge(p,["onSearch"]),{disabled:o,className:E,style:i,ref:n,placeholder:s,addAfter:v!==void 0?v:u?l.createElement(ia,{disabled:o,size:p.size,className:w+"-btn",type:"primary",onClick:O,loading:c,loadingFixedWidth:!0,icon:u===!0&&!c&&l.createElement(Zi,null)},u!==!0&&u):null,suffix:m!==void 0?m:!u&&(c?l.createElement(Yr,null):l.createElement(Zi,{onClick:O})),onChange:function(x,_){C(x),e.onChange&&e.onChange(x,_)},defaultValue:f,onPressEnter:function(x){O(),e.onPressEnter&&e.onPressEnter(x)}}))});Mf.displayName="Search";const gg=Mf;var hg=`
  position: absolute;
  min-height: 0 !important;
  max-height: none;
  height:0;
  visibility: hidden;
  z-index: -100;
  top: 0;
  right: 0;
`,yg=["border-width","box-sizing","font-family","font-weight","font-size","font-variant","letter-spacing","line-height","padding-top","padding-bottom","padding-left","padding-right","text-indent","text-rendering","text-transform","width"],tr;function bg(e){tr||(tr=document.createElement("textarea"),document.body.appendChild(tr));var n=window.getComputedStyle(e),t=`
    `+yg.map(function(s){return s+":"+n.getPropertyValue(s)}).join(";")+`
  `;tr.setAttribute("style",""+hg+t);var r=parseFloat(n.getPropertyValue("padding-top"))+parseFloat(n.getPropertyValue("padding-bottom")),a=n.getPropertyValue("box-sizing"),i=parseFloat(n.getPropertyValue("border-top-width"))+parseFloat(n.getPropertyValue("border-bottom-width"));return{paddingSize:r,boxSizing:a,borderSize:i}}function xg(e,n){var t=function(){var y,C;return xe(e)&&(y=e.minRows,C=e.maxRows),{minRows:y,maxRows:C}};if(e){var r=t(),a=r.minRows,i=r.maxRows,s=n,o=bg(s),u=o.paddingSize,c=o.boxSizing,f=o.borderSize;tr.value=s.value||s.placeholder||"";var v=tr.scrollHeight+f,m=void 0,p=void 0,g=void 0;if(a||i){tr.value="";var h=tr.scrollHeight-u;Ye(a)&&(m=h*a,c==="border-box"&&(m+=u,m+=f),v=Math.max(v,m)),Ye(i)&&(p=h*i,c==="border-box"&&(p+=u,p+=f),g=v>p?"auto":"",p=Math.min(v,p))}var b={};return b.height=v,m&&(b.minHeight=m),p&&(b.maxHeight=p),g&&(b.overflowY=g),b}}var Lt={key:"Enter",code:13},Yi={key:"Escape",code:27},Rf={key:"Backspace",code:8},Ws={key:"Tab",code:9},kf={key:"ArrowUp",code:38},Af={key:"ArrowDown",code:40},Df={key:"ArrowLeft",code:37},Lf={key:"ArrowRight",code:39},Cg=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function zf(e){var n=e.value,t=e.maxLength,r=e.onChange,a=e.onKeyDown,i=e.onPressEnter,s=e.beforeTriggerValueChangeCallback,o=e.normalizeHandler,u=d.useRef(!1),c=Cg(d.useState(""),2),f=c[0],v=c[1],m=function(p,g){s&&s(p),r&&p!==n&&(t===void 0||p.length<=t)&&r(p,g)};return{compositionValue:f,triggerValueChangeCallback:m,compositionHandler:function(p){u.current=p.type!=="compositionend",u.current||(v(void 0),m(p.target.value,p))},valueChangeHandler:function(p){var g=p.target.value;u.current?(u.current=!1,v(g)):(f&&v(void 0),m(g,p))},keyDownHandler:function(p){var g=p.keyCode||p.which;if(!u.current&&(a&&a(p),g===Lt.code)){i&&i(p);var h=o==null?void 0:o("onPressEnter");h&&m(h(p.target.value),p)}}}}var _n=globalThis&&globalThis.__assign||function(){return _n=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},_n.apply(this,arguments)},Eg=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},mi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},wg=function(e,n){var t,r,a,i=e.className,s=e.style,o=e.wrapperStyle,u=e.placeholder,c=e.disabled,f=e.error,v=e.maxLength,m=e.showWordLimit,p=e.allowClear,g=e.onChange,h=e.onClear,b=e.onKeyDown,y=e.onPressEnter,C=e.status,w=e.clearIcon,E=Eg(e,["className","style","wrapperStyle","placeholder","disabled","error","maxLength","showWordLimit","allowClear","onChange","onClear","onKeyDown","onPressEnter","status","clearIcon"]),O=xe(v)?v.length:v,x=xe(v)?v.errorOnly?void 0:v.length:v,_=d.useRef(),T=mi(d.useState({}),2),P=T[0],I=T[1],j=mi(ot("",{defaultValue:"defaultValue"in e?Gr(e.defaultValue,x):void 0,value:"value"in e?Gr(e.value,x):void 0}),2),M=j[0],R=j[1],k=zf({value:M,maxLength:x,onChange:g,onKeyDown:b,onPressEnter:y,beforeTriggerValueChangeCallback:function(ie){!("value"in e)&&(x===void 0||ie.length<=x)&&R(ie)}}),L=k.compositionValue,S=k.compositionHandler,N=k.valueChangeHandler,A=k.keyDownHandler,W=k.triggerValueChangeCallback,D=L||M||"",F=d.useContext(ye),H=F.getPrefixCls,z=F.rtl,B=H("textarea");c&&(P.resize="none");var re=function(){if(_.current&&_.current.focus){if(_.current.setSelectionRange){var ie=_.current.textContent.length;_.current.setSelectionRange(ie,ie)}_.current.focus()}},V=function(){var ie=xg(e.autoSize,_.current);ie&&I(ie)},$=function(ie){ie.stopPropagation(),re(),W("",ie),h==null||h()};Fr(function(){V()},[D]),d.useImperativeHandle(n,function(){return{dom:_.current,focus:function(){re()},blur:function(){_.current&&_.current.blur&&_.current.blur()},getRootDOMNode:function(){return _.current}}},[]);var X=M?M.length:0,Z=O&&m||p,Y=d.useMemo(function(){return!x&&O?X>O:!1},[X,O,x]),J=C||(f||Y?"error":void 0),ae=K(B,(t={},t[B+"-"+J]=J,t[B+"-disabled"]=c,t[B+"-rtl"]=z,t),i),ve=l.createElement("textarea",_n({},Ge(E,["autoSize","defaultValue"]),{maxLength:x,ref:_,style:_n(_n({},s),P),className:ae,placeholder:u,disabled:c,value:D,onChange:N,onKeyDown:A,onCompositionStart:S,onCompositionUpdate:S,onCompositionEnd:S}));if(Z){var ne=!c&&p&&M,G=mi(z?[O,X]:[X,O],2),U=G[0],oe=G[1];return l.createElement("div",{className:K(B+"-wrapper",(r={},r[B+"-clear-wrapper"]=p,r[B+"-wrapper-rtl"]=z,r)),style:o},ve,ne?w!==void 0?l.createElement("span",{className:B+"-clear-icon",onClick:$,onMouseDown:function(ie){ie.preventDefault()}},w):l.createElement(Nt,{className:B+"-clear-icon"},l.createElement(Dt,{onClick:$,onMouseDown:function(ie){ie.preventDefault()}})):null,O&&m&&l.createElement("span",{className:K(B+"-word-limit",(a={},a[B+"-word-limit-error"]=Y,a))},U,"/",oe))}return ve},$f=l.forwardRef(wg);$f.displayName="TextArea";const Og=$f;function Fs(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Bs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Fs(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Fs(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function _g(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Bs(Bs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-eye")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M24 37c6.627 0 12.627-4.333 18-13-5.373-8.667-11.373-13-18-13-6.627 0-12.627 4.333-18 13 5.373 8.667 11.373 13 18 13Z",clipRule:"evenodd"}),l.createElement("path",{d:"M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0Z"}))}var ul=l.forwardRef(_g);ul.defaultProps={isIcon:!0};ul.displayName="IconEye";const Pg=ul;function Ks(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Gs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Ks(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ks(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function Tg(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Gs(Gs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-eye-invisible")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M14 14.5c-2.69 2-5.415 5.33-8 9.5 5.373 8.667 11.373 13 18 13 3.325 0 6.491-1.09 9.5-3.271M17.463 12.5C19 11 21.75 11 24 11c6.627 0 12.627 4.333 18 13-1.766 2.848-3.599 5.228-5.5 7.14"}),l.createElement("path",{d:"M29 24a5 5 0 1 1-10 0 5 5 0 0 1 10 0ZM6.852 7.103l34.294 34.294"}))}var fl=l.forwardRef(Tg);fl.defaultProps={isIcon:!0};fl.displayName="IconEyeInvisible";const Sg=fl;function Er(e){var n=d.useCallback(function(t){return{onKeyDown:function(r){var a,i,s,o,u,c,f=r.keyCode||r.which;f===Lt.code&&((a=t.onPressEnter)===null||a===void 0||a.call(t,r)),f===Af.code&&((i=t.onArrowDown)===null||i===void 0||i.call(t,r)),f===Df.code&&((s=t.onArrowLeft)===null||s===void 0||s.call(t,r)),f===Lf.code&&((o=t.onArrowRight)===null||o===void 0||o.call(t,r)),f===kf.code&&((u=t.onArrowUp)===null||u===void 0||u.call(t,r)),(c=e==null?void 0:e.onKeyDown)===null||c===void 0||c.call(e,r)}}},[]);return n}var jr=globalThis&&globalThis.__assign||function(){return jr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},jr.apply(this,arguments)},Ng=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Ig=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Vf=l.forwardRef(function(e,n){var t,r=Ig(ot(!1,{defaultValue:e.defaultVisibility,value:e.visibility}),2),a=r[0],i=r[1],s=d.useContext(ye).getPrefixCls,o=Er(),u=e.className,c=e.visibilityToggle,f=c===void 0?!0:c,v=e.onVisibilityChange,m=Ng(e,["className","visibilityToggle","onVisibilityChange"]),p=s("input-password"),g=K(p,(t={},t[p+"-visibility"]=f,t),u),h=function(E){"visibility"in e||i(E),v&&v(E)},b=e.suffix,y=function(){h(!a)};if(f){var C=jr({onClick:y,onMouseDown:function(E){return E.preventDefault()},onMouseUp:function(E){return E.preventDefault()}},o({onPressEnter:y}));if(e.suffix)b=l.createElement("span",jr({},C),e.suffix);else{var w=a?Pg:Sg;b=l.createElement(w,jr({},C,{focusable:void 0,"aria-hidden":void 0,tabIndex:0,className:p+"-visibility-icon"}))}}return l.createElement(Za,jr({},Ge(m,["visibility","defaultVisibility"]),{type:a?"text":"password",className:g,ref:n,suffix:b}))});Vf.displayName="Password";const jg=Vf;function Ln(e){return typeof e=="string"?e.replace(/(\s{2,})|(\s{1,}$)/g,function(n){return" ".repeat(n.length)}):e}var wt=globalThis&&globalThis.__assign||function(){return wt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},wt.apply(this,arguments)},Mg=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Rg=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},kg=2,Ag=function(e){if(!e)return{};var n=window.getComputedStyle(e),t=["font","letterSpacing","overflow","tabSize","textIndent","textTransform","whiteSpace","wordBreak","wordSpacing","paddingLeft","paddingRight","borderLeft","borderRight","boxSizing"];return t.reduce(function(r,a){return r[a]=n[a],r},{})},Hf=l.forwardRef(function(e,n){var t,r=e.allowClear,a=e.disabled,i=e.placeholder,s=e.className,o=e.style,u=e.height,c=e.prefixCls,f=e.hasParent,v=e.size,m=e.value,p=e.autoFitWidth,g=e.onClear,h=e.readOnly,b=e.onChange,y=e.onKeyDown,C=e.onPressEnter,w=e.maxLength,E=e.clearIcon,O=Mg(e,["allowClear","disabled","placeholder","className","style","height","prefixCls","hasParent","size","value","autoFitWidth","onClear","readOnly","onChange","onKeyDown","onPressEnter","maxLength","clearIcon"]),x=Ge(O,["error","status","showWordLimit","className","defaultValue","addBefore","addAfter","afterStyle","beforeStyle","prefix","suffix","normalize","normalizeTrigger","autoWidth"]),_=Rg(d.useState(),2),T=_[0],P=_[1],I=Er(),j=d.useRef(),M=d.useRef(),R=d.useRef(null),k=xe(w)?w.errorOnly?void 0:w.length:w,L=function($){var X,Z=e.normalizeTrigger||["onBlur"];return Array.isArray(Z)&&Z.indexOf($)>-1&&Ke(e.normalize)&&(X=e.normalize),X},S=zf({value:m,maxLength:k,onChange:b,onKeyDown:y,onPressEnter:C,normalizeHandler:L}),N=S.compositionValue,A=S.valueChangeHandler,W=S.compositionHandler,D=S.keyDownHandler,F=S.triggerValueChangeCallback,H=K(c,c&&(t={},t[c+"-size-"+v]=v,t[c+"-"+e.status]=e.status,t[c+"-disabled"]=a,t[c+"-autowidth"]=p,t),f?void 0:s),z=wt(wt({"aria-invalid":e.status==="error"||void 0},x),{readOnly:h,maxLength:k,disabled:a,placeholder:i,value:N||m||"",className:H,onKeyDown:D,onChange:A,onCompositionStart:function($){var X;(X=O.onCompositionStart)===null||X===void 0||X.call(O,$),W($)},onCompositionUpdate:function($){var X;(X=O.onCompositionUpdate)===null||X===void 0||X.call(O,$),W($)},onCompositionEnd:function($){var X;(X=O.onCompositionEnd)===null||X===void 0||X.call(O,$),W($)},onBlur:function($){var X;(X=e.onBlur)===null||X===void 0||X.call(e,$);var Z=L("onBlur");Z&&F(Z($.target.value),$)}});d.useImperativeHandle(n,function(){return{dom:j.current,getRootDOMNode:function(){return j.current},focus:function(){j.current&&j.current.focus&&j.current.focus()},blur:function(){j.current&&j.current.blur&&j.current.blur()}}},[]);var B=function(){if(M.current&&j.current){var $=M.current.offsetWidth;j.current.style.width=$+kg+"px"}};d.useEffect(function(){p&&((!xe(p)||!p.pure)&&P(Ag(j==null?void 0:j.current)),B())},[p]);var re=z.value||i,V=function($){j.current&&j.current.focus&&j.current.focus(),F("",$),g==null||g()};return l.createElement(l.Fragment,null,r?l.createElement(l.Fragment,null,l.createElement("input",wt({ref:j},z)),!h&&!a&&r&&m?E!==void 0?l.createElement("span",wt({tabIndex:0,className:c+"-clear-icon"},I({onPressEnter:V}),{onClick:function($){$.stopPropagation(),V($)},onMouseDown:function($){$.preventDefault()}}),E):l.createElement(Nt,wt({tabIndex:0,className:c+"-clear-icon"},I({onPressEnter:V}),{onClick:function($){$.stopPropagation(),V($)}}),l.createElement(Dt,{onMouseDown:function($){$.preventDefault()}})):null):l.createElement("input",wt({ref:j},z,{style:f?{}:wt(wt({minWidth:xe(p)?p.minWidth:void 0,maxWidth:xe(p)?p.maxWidth:void 0},o),"height"in e?{height:u}:{})})),p&&l.createElement(pt,{getTargetDOMNode:function(){return M.current},onResize:function(){var $=M.current.offsetWidth;if(typeof p=="object"){var X=typeof p.delay=="function"?p.delay($,R.current):p.delay;X?setTimeout(B,X):B()}else B();R.current=$}},l.createElement("span",{className:K(c+"-mirror"),style:f?T:wt(wt(wt({},T),o),"height"in e?{height:u}:{}),ref:M},Ln(re))))});Hf.displayName="InputComponent";const dl=Hf;var qi=globalThis&&globalThis.__assign||function(){return qi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},qi.apply(this,arguments)},Dg=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Wf=l.forwardRef(function(e,n){var t,r=d.useContext(ye).getPrefixCls,a=e.className,i=e.style,s=e.children,o=e.compact,u=Dg(e,["className","style","children","compact"]),c=r("input-group"),f=K(c,(t={},t[c+"-compact"]=o,t),a);return l.createElement("div",qi({ref:n,className:f,style:i},u),s)});Wf.displayName="InputGroup";const Lg=Wf;var Vt=globalThis&&globalThis.__assign||function(){return Vt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Vt.apply(this,arguments)},gi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},zg=function(e){e.target.tagName!=="INPUT"&&e.preventDefault()},Xn=function(e,n,t,r){return t===void 0&&(t={}),n?l.createElement("span",{style:t,className:e,onClick:r},n):null};function Gr(e,n){var t=e!==null&&!ft(e)&&!ar(e)?String(e):e||"";return n?t.slice(0,n):t}function $g(e,n){var t,r,a,i=d.useContext(ye),s=i.getPrefixCls,o=i.size,u=i.componentConfig,c=i.rtl,f=He(e,{},u==null?void 0:u.Input),v=f.className,m=f.style,p=f.addBefore,g=f.addAfter,h=f.suffix,b=f.prefix,y=f.beforeStyle,C=f.afterStyle,w=f.height,E=f.disabled,O=f.maxLength,x=f.showWordLimit,_=f.allowClear,T=f.autoWidth,P=T?Vt({minWidth:0,maxWidth:"100%"},xe(T)?T:{}):null,I=Vt({minWidth:P==null?void 0:P.minWidth,maxWidth:P==null?void 0:P.maxWidth,width:P&&"auto"},m),j=xe(O)?O.length:O,M=xe(O)&&O.errorOnly?void 0:j,R=gi(d.useState(!1),2),k=R[0],L=R[1],S=d.useRef(),N=d.useRef(),A=d.useRef(),W=gi(ot("",{defaultValue:"defaultValue"in f?Gr(f.defaultValue,M):void 0,value:"value"in f?Gr(f.value,M):void 0}),2),D=W[0],F=W[1];d.useImperativeHandle(n,function(){var oe,ie,te;return{focus:(oe=S.current)===null||oe===void 0?void 0:oe.focus,blur:(ie=S.current)===null||ie===void 0?void 0:ie.blur,dom:(te=S.current)===null||te===void 0?void 0:te.dom,getRootDOMNode:function(){var pe;return N.current||((pe=S.current)===null||pe===void 0?void 0:pe.dom)}}},[]);var H=function(oe,ie){"value"in f||F(oe),f.onChange&&f.onChange(oe,ie)},z=s("input"),B=f.size||o,re="height"in f,V=h,$=D?D.length:0,X=d.useMemo(function(){return!M&&j?$>j:!1},[$,j,M]);if(j&&x){var Z=gi(c?[j,$]:[$,j],2),Y=Z[0],J=Z[1];V=l.createElement("span",{className:K(z+"-word-limit",(t={},t[z+"-word-limit-error"]=X,t))},Y,"/",J)}var ae=K(z+"-group-wrapper",z+"-group-wrapper-"+B,(r={},r[z+"-custom-height"]=re,r[z+"-has-suffix"]=V,r[z+"-group-wrapper-disabled"]=E,r[z+"-group-wrapper-rtl"]=c,r[z+"-group-wrapper-autowidth"]=P,r),v),ve=f.status||(f.error||X?"error":void 0),ne=p||g||V||b,G=l.createElement(dl,Vt({ref:S},f,{autoFitWidth:!!P,style:I,status:ve,onFocus:function(oe){L(!0),f.onFocus&&f.onFocus(oe)},onBlur:function(oe){L(!1),f.onBlur&&f.onBlur(oe)},onChange:H,prefixCls:z,value:D,hasParent:!!ne||_,size:B})),U=K(z+"-inner-wrapper",(a={},a[z+"-inner-wrapper-"+ve]=ve,a[z+"-inner-wrapper-disabled"]=E,a[z+"-inner-wrapper-focus"]=k,a[z+"-inner-wrapper-has-prefix"]=b,a[z+"-inner-wrapper-"+B]=B,a[z+"-clear-wrapper"]=_,a[z+"-inner-wrapper-rtl"]=c,a));return ne?l.createElement("div",{ref:N,className:ae,style:Vt(Vt({},I),re?{height:w}:{})},l.createElement("span",{className:z+"-group"},Xn(z+"-group-addbefore",p,y),l.createElement("span",{className:U,ref:A,onMouseDown:function(oe){oe.target.tagName!=="INPUT"&&A.current&&ma(A.current,oe.target)&&oe.preventDefault()},onClick:function(oe){A.current&&ma(A.current,oe.target)&&S.current&&S.current.focus()}},Xn(z+"-group-prefix",b),G,Xn(z+"-group-suffix",V)),Xn(z+"-group-addafter",g,C))):_?l.createElement("span",{ref:N,className:K(v,U),style:Vt(Vt({},I),re?{height:w}:{}),onMouseDown:zg,onClick:function(){S.current&&S.current.focus()}},G):G}var Jr=l.forwardRef($g);Jr.displayName="Input";Jr.Search=gg;Jr.TextArea=Og;Jr.Password=jg;Jr.Group=Lg;const Za=Jr;var Vg=function(e){var n=e.getContainer,t=e.children,r=d.useRef(),a=ll();return(a||r.current===null)&&!qr&&(r.current=n()),d.useEffect(function(){return function(){var i=r.current;i&&i.parentNode&&(i.parentNode.removeChild(i),r.current=null)}},[]),r.current?St.createPortal(t,r.current):null};const Hg=Vg;var Mr=globalThis&&globalThis.__assign||function(){return Mr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Mr.apply(this,arguments)},Rr=0,ya=function(e,n){var t=n.position,r=e.getBoundingClientRect(),a=r.width,i=r.height,s=r.left,o=r.right,u=r.top,c=r.bottom,f=n.boundaryDistance||{},v="left"in f&&f.left||Rr,m="top"in f&&f.top||Rr,p,g,h,b;return["bottom","bl","br"].indexOf(t)>-1?(h=u,b=c):(h=u-m,b=c-m),["right","rt","rb"].indexOf(t)>-1?(p=s,g=o):(p=s-v,g=o-v),{width:a,height:i,left:p,right:g,top:h,bottom:b}};function Wg(e,n,t,r){if(!t||!e||qr)return{left:0,width:0,height:0,top:0};var a=function(h){return document.documentElement[h]||document.body[h]},i=t===document.body?a("scrollTop"):t.scrollTop,s=t===document.body?a("scrollLeft"):t.scrollLeft,o=n.left,u=n.top,c=n.width,f=n.height,v=t===document.body?0:ya(t,r).left,m=t===document.body?0:ya(t,r).top,p=u+i-m,g=o+s-v;return{left:g,top:p,width:c,height:f}}var Pt=function(e,n,t){return t<e?e:t>n?n:t},Fg=function(e,n){var t=0,r=0,a={};n?a=Mr({left:12,right:12,top:12,bottom:12},e):a=Mr({},e);for(var i in a)if(ct(a[i])){var s=0;["top","bottom"].indexOf(i)>-1?(s=1,t=a[i][0]):r=a[i][1],a[i]=a[i][s]}return Mr(Mr({},a),{horizontalOffset:t,verticalOffset:r})},Bg=function(e,n,t){var r=t.boundaryDistance,a=t.position;return n?{left:n.clientX,top:n.clientY,width:0,height:0,right:n.clientX,bottom:n.clientY}:ya(e,{boundaryDistance:r,position:a})},Kg=function(e){var n=e.offsetWidth,t=e.offsetHeight;return{width:n,height:t}},Gg=function(e){var n,t,r=e||{},a="left"in r?r.left:"right"in r?r.right:Rr,i="top"in r?r.top:"bottom"in r?r.bottom:Rr,s=(((n=document.documentElement)===null||n===void 0?void 0:n.clientHeight)||window.innerHeight)-(i||Rr),o=(((t=document.documentElement)===null||t===void 0?void 0:t.clientWidth)||window.innerWidth)-(a||Rr);return{windowHeight:s,windowWidth:o}};const Ug=function(e,n,t,r,a){var i=e.autoAlignPopupWidth,s=e.autoAlignPopupMinWidth,o=e.alignPoint,u=e.style;if(!t||!n||!r)return{};var c={},f=!e.alignPoint&&e.boundaryDistance||{},v=Bg(t,o&&a,{boundaryDistance:f,position:e.position}),m=Wg(t,v,r,{boundaryDistance:f,position:e.position}),p=m.left,g=m.top,h=m.width,b=m.height,y=Fg(e.popupAlign,e.showArrow),C=y.left||0,w=y.right||0,E=y.top||0,O=y.bottom||0;i&&(u==null?void 0:u.width)===void 0&&(n.style.width=t.offsetWidth+"px"),s&&(n.style.minWidth=t.offsetWidth+"px");var x=Kg(n),_=e.position,T={},P=function(A){if(e.autoFitPosition){var W=Gg(f),D=W.windowHeight,F=W.windowWidth,H=!1,z={left:p-v.left,top:g-v.top},B=c.top,re=B===void 0?0:B,V=c.left,$=V===void 0?0:V;if((A==="top"||A==="bottom")&&(z.left>$&&v.right>12?(c.left=Math.max(z.left,p-x.width),c.left=Math.max(c.left,p-x.width+24)):$-z.left+x.width>F&&F-v.left>12&&(c.left=Math.max(z.left,z.left+F-x.width),c.left=Math.max(c.left,p-x.width+24))),(A==="left"||A==="right")&&(z.top>re&&v.bottom>12?(c.top=z.top,c.top=Math.max(c.top,g-x.height+v.height/2)):re-z.top+x.height>D&&D-v.top>12&&(c.top=Math.max(z.top,z.top+D-x.height),c.top=Math.max(c.top,g-x.height+v.height/2))),A==="top"&&z.top>re&&(v.top<D-v.bottom?(c.top=Math.min(g+b+(E||0),z.top+D-x.height),H=!0):c.top=z.top),A==="bottom"&&re-z.top+x.height>D&&(D-v.bottom<v.top?(c.top=Math.max(g-x.height-(O||0),z.top),H=!0):c.top=z.top+D-x.height),A==="left"&&z.left>$&&(v.left<F-v.right?(c.left=Math.min(h+p+w,z.left+F-x.width),H=!0):c.left=z.left),A==="right"&&$-z.left+x.width>F&&(F-v.right<v.left?(c.left=Math.max(p-x.width-C,z.left),H=!0):c.left=z.left+F-x.width),c.left<0)c.left=0;else{var X=r.scrollWidth-x.width;c.left=Math.min(X,c.left)}return H}},I=y.horizontalOffset||0,j=y.verticalOffset||0;switch(e.position){case"top":{c.top=g-x.height-E,c.left=p+h/2-x.width/2,P("top")&&(_="bottom"),c.left+=I;var M=p-Number(c.left)+h/2;T.left=Pt(12,x.width-12,M);break}case"tl":c.top=g-x.height-E,c.left=p,P("top")&&(_="bl"),c.left+=I;var R=p-Number(c.left)+Math.min(h/2,50);T.left=Pt(12,x.width-12,R);break;case"tr":c.top=-n.clientHeight+g-E,c.left=p+h-x.width,P("top")&&(_="br"),c.left+=I,R=p-Number(c.left)+Math.max(h/2,h-50),T.left=Pt(12,x.width-12,R);break;case"bottom":{c.top=b+g+O,c.left=p+h/2-x.width/2,P("bottom")&&(_="top"),c.left+=I;var k=p-Number(c.left)+h/2;T.left=Pt(12,x.width-12,k);break}case"bl":c.top=b+g+O,c.left=p,P("bottom")&&(_="tl"),c.left+=I,R=p-Number(c.left)+Math.min(h/2,50),T.left=Pt(12,x.width-12,R);break;case"br":c.top=b+g+O,c.left=p+h-x.width,P("bottom")&&(_="tr"),c.left+=I,R=p-Number(c.left)+Math.max(h/2,h-50),T.left=Pt(12,x.width-12,R);break;case"left":{c.top=g+b/2-x.height/2,c.left=p-x.width-C,P("left")&&(_="right"),c.top+=j;var L=g-Number(c.top)+b/2;T.top=Pt(12,x.height-12,L);break}case"lt":c.top=g,c.left=p-x.width-C,P("left")&&(_="rt"),c.top+=j;var S=g-Number(c.top)+Math.min(b/2,50);T.top=Pt(12,x.height-12,S);break;case"lb":c.top=g+b-x.height,c.left=p-x.width-C,P("left")&&(_="rb"),c.top+=j,S=g-Number(c.top)+Math.max(b/2,b-50),T.top=Pt(12,x.height-12,S);break;case"right":{c.top=g+b/2-x.height/2,c.left=h+p+w,P("right")&&(_="left"),c.top+=j;var N=g-Number(c.top)+b/2;T.top=Pt(12,x.height-12,N);break}case"rt":c.top=g,c.left=h+p+w,P("right")&&(_="lt"),c.top+=j,S=g-Number(c.top)+Math.min(b/2,50),T.top=Pt(12,x.height-12,S);break;case"rb":c.top=g+b-x.height,c.left=h+p+w,P("right")&&(_="lb"),c.top+=j,S=g-Number(c.top)+Math.max(b/2,b-50),T.top=Pt(12,x.height-12,S);break}return{style:c,arrowStyle:T,realPosition:_}};function Zg(e,n,t,r){var a=n||{},i=t||{},s=e||{},o=r?new Set(r):new Set(Object.keys(s).concat(Object.keys(a)).concat(Object.keys(i))),u={};return o.forEach(function(c){s[c]!==void 0?u[c]=s[c]:c in i?u[c]=i[c]:c in a&&(u[c]=a[c])}),u}var Xg=globalThis&&globalThis.__extends||function(){var e=function(n,t){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var i in a)Object.prototype.hasOwnProperty.call(a,i)&&(r[i]=a[i])},e(n,t)};return function(n,t){if(typeof t!="function"&&t!==null)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");e(n,t);function r(){this.constructor=n}n.prototype=t===null?Object.create(t):(r.prototype=t.prototype,new r)}}(),st=globalThis&&globalThis.__assign||function(){return st=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},st.apply(this,arguments)};function Us(e,n){if(!e)return{};var t=ya(e,n),r=t.width,a=t.height,i=t.left,s=t.right;return{width:r,height:a,left:i,right:s}}var Ff=["onClick","onMouseEnter","onMouseLeave","onMouseMove","onFocus","onBlur","onContextMenu","onKeyDown"];function Yg(e,n){var t={},r=st({},e);return n.forEach(function(a){e&&a in e&&(t[a]=e[a],delete r[a])}),{picked:t,omitted:r}}var qg={blurToHide:!0,classNames:"fadeIn",trigger:"hover",position:"bottom",duration:200,unmountOnExit:!0,popupAlign:{},popupHoverStay:!0,clickOutsideToClose:!0,escToClose:!1,mouseLeaveToClose:!0,containerScrollToClose:!1,getDocument:function(){return window.document},autoFixPosition:!0,mouseEnterDelay:100,mouseLeaveDelay:100,autoFitPosition:!0},Jg=function(e){Xg(n,e);function n(t,r){var a=e.call(this,t,r)||this;a.delayTimer=null,a.updatePositionTimer=null,a.popupOpen=!1,a.mousedownToHide=!1,a.hasPopupMouseDown=!1,a.unmount=!1,a.isDidMount=!1,a.mouseLocation={clientX:0,clientY:0},a.observerContainer=null,a.scrollElements=null,a.resizeObserver=new Nf(function(){a.handleUpdatePosition()}),a.childrenDom=null,a.childrenDomSize={},a.getMergedProps=function(o){var u=a.context.componentConfig,c=Zg(o&&xe(o)?o:a.props,qg,u==null?void 0:u.Trigger,o&&ct(o)?o:void 0);return c},a.getRootElement=function(){var o,u;return a.childrenDom=Ha(((u=(o=a.props).getTargetDOMNode)===null||u===void 0?void 0:u.call(o))||a.rootElementRef,a),a.childrenDom},a.getPopupElement=function(){var o;return((o=a.triggerRef)===null||o===void 0?void 0:o.current)||null},a.offScrollListeners=function(){(a.scrollElements||[]).forEach(function(o){ut(o,"scroll",a.handleScroll)}),a.scrollElements=null},a.offWindowResize=function(){a.handleWindowResize=!1,ut(window,"resize",a.handleUpdatePosition)},a.offContainerResize=function(){a.resizeObserver&&a.observerContainer&&(a.resizeObserver.unobserve(a.observerContainer),a.observerContainer=null)},a.handleScroll=function(){var o=a.getMergedProps(["containerScrollToClose","updateOnScroll"]);o.containerScrollToClose?a.setPopupVisible(!1):o.updateOnScroll&&a.handleUpdatePosition()},a.onContainersScroll=function(o){var u,c;a.scrollElements||(a.scrollElements=jm(a.childrenDom,(u=a.popupContainer)===null||u===void 0?void 0:u.parentNode),o.containerScrollToClose&&((c=a.popupContainer)===null||c===void 0?void 0:c.parentNode)===document.body&&a.scrollElements.indexOf(document.body)===-1&&wf(document.documentElement)&&a.scrollElements.push(window),a.scrollElements.forEach(function(f){it(f,"scroll",a.handleScroll)}))},a.onContainerResize=function(){var o,u=(o=a.popupContainer)===null||o===void 0?void 0:o.parentNode;a.resizeObserver&&a.observerContainer!==u&&(a.offContainerResize(),u&&a.resizeObserver.observe(u),a.observerContainer=u)},a.handleUpdatePosition=Dn(function(){a.updatePopupPosition()}),a.isClickTrigger=function(){var o=a.getMergedProps(["trigger"]).trigger;return[].concat(o).indexOf("click")>-1},a.isFocusTrigger=function(){var o=a.getMergedProps(["trigger"]).trigger;return[].concat(o).indexOf("focus")>-1},a.isHoverTrigger=function(){var o=a.getMergedProps(["trigger"]).trigger;return[].concat(o).indexOf("hover")>-1},a.isContextMenuTrigger=function(){var o=a.getMergedProps(["trigger"]).trigger;return[].concat(o).indexOf("contextMenu")>-1},a.isMouseLeaveToClose=function(){return a.isHoverTrigger()&&a.getMergedProps(["mouseLeaveToClose"]).mouseLeaveToClose},a.isPopupHoverHide=function(){return a.isHoverTrigger()&&!a.getMergedProps(["popupHoverStay"]).popupHoverStay},a.isClickToHide=function(){if(a.isClickTrigger()||a.isContextMenuTrigger()){var o=a.getMergedProps(["clickToClose"]).clickToClose,u=o===void 0?!0:o;return u}return a.isHoverTrigger()&&a.props.clickToClose},a.isBlurToHide=function(){return a.isFocusTrigger()&&a.getMergedProps(["blurToHide"]).blurToHide},a.clearTimer=function(){a.updatePositionTimer&&(a.updatePositionTimer.cancel?a.updatePositionTimer.cancel():(clearTimeout(a.updatePositionTimer),a.updatePositionTimer=null)),a.delayTimer&&(clearTimeout(a.delayTimer),a.delayTimer=null),a.mouseDownTimeout&&(clearTimeout(a.mouseDownTimeout),a.mouseDownTimeout=null)},a.offClickOutside=function(){if(a.handleClickOutside){var o=a.getMergedProps(["getDocument"]).getDocument,u=Ke(o)&&o();ut(u,"mousedown",a.onClickOutside),a.handleClickOutside=!1}},a.getTransformOrigin=function(o){var u,c,f=a.getPopupElement();if(!f)return{};var v=a.getMergedProps(["showArrow","classNames"]),m=v.showArrow,p=v.classNames,g=m&&((u=a.arrowStyle)===null||u===void 0?void 0:u.top)||0,h=m&&((c=a.arrowStyle)===null||c===void 0?void 0:c.left)||0;g=g?g+"px":"",h=h?h+"px":"";var b={top:(h||"50%")+" 100% 0",tl:(h||"15px")+" 100% 0",tr:(h||f.clientWidth-15+"px")+" 100% 0",bottom:(h||"50%")+" 0 0",bl:(h||"15px")+" 0 0",br:(h||f.clientWidth-15+"px")+" 0 0",left:"100% "+(g||"50%")+" 0",lt:"100% "+(g||"15px")+" 0",lb:"100% "+(g||f.clientHeight-15+"px")+" 0",right:"0 "+(g||"50%")+" 0",rt:"0 "+(g||"15px")+" 0",rb:"0 "+(g||f.clientHeight-15+"px")+" 0"};if(p&&p.indexOf("zoom")>-1)return{transformOrigin:b[o]};if(p==="slideDynamicOrigin"){var y="0% 0%";return["top","tl","tr"].indexOf(o)>-1&&(y="100% 100%"),{transformOrigin:y}}return{}},a.getTransformTranslate=function(){if(a.getMergedProps(["classNames"]).classNames!=="slideDynamicOrigin")return"";switch(a.realPosition){case"bottom":case"bl":case"br":return"scaleY(0.9) translateY(-4px)";case"top":case"tl":case"tr":return"scaleY(0.9) translateY(4px)";default:return""}},a.getPopupStyle=function(){if(!(a.unmount||!a.popupContainer)){var o=a.popupContainer,u=a.triggerRef.current,c=a.getRootElement();if(!c.offsetParent&&!c.getClientRects().length)return a.state.popupStyle;var f=a.getMergedProps(),v=Ug(f,u,c,o,a.mouseLocation),m=v.style,p=v.arrowStyle,g=v.realPosition;return a.realPosition=g||f.position,a.arrowStyle=p||{},st(st({},m),a.getTransformOrigin(a.realPosition))}},a.showPopup=function(o){o===void 0&&(o=function(){});var u=a.getPopupStyle();a.setState({popupStyle:u},o)},a.update=Dn(function(o){if(!(a.unmount||!a.state.popupVisible)){var u=a.getPopupStyle();a.setState({popupStyle:u},function(){o==null||o()})}}),a.getRootDOMNode=function(){return a.getRootElement()},a.updatePopupPosition=function(o,u){o===void 0&&(o=0);var c=a.state.popupVisible;if(c){if(o<4){a.updatePositionTimer=a.update(u);return}a.updatePositionTimer=setTimeout(function(){var f=a.getPopupStyle();a.setState({popupStyle:f},function(){u==null||u()})},o)}},a.setPopupVisible=function(o,u,c){u===void 0&&(u=0);var f=a.getMergedProps(["onVisibleChange","popupVisible"]),v=f.onVisibleChange,m=a.state.popupVisible;o!==m?a.delayToDo(u,function(){v&&v(o),"popupVisible"in f?c==null||c():o?a.setState({popupVisible:!0},function(){a.showPopup(c)}):a.setState({popupVisible:!1},function(){c==null||c()})}):c==null||c()},a.delayToDo=function(o,u){o?(a.clearDelayTimer(),a.delayTimer=setTimeout(function(){u(),a.clearDelayTimer()},o)):u()},a.onClickOutside=function(o){var u=a.getMergedProps(["onClickOutside","clickOutsideToClose"]),c=u.onClickOutside,f=u.clickOutsideToClose,v=a.getPopupElement(),m=a.getRootElement();!ma(v,o.target)&&!ma(m,o.target)&&!a.hasPopupMouseDown&&(c==null||c(),f&&!a.isBlurToHide()&&!a.isHoverTrigger()&&a.setPopupVisible(!1))},a.onKeyDown=function(o){var u=o.keyCode||o.which;a.triggerPropsEvent("onKeyDown",o),u===Yi.code&&a.onPressEsc(o)},a.onPressEsc=function(o){var u=a.getMergedProps(["escToClose"]).escToClose;u&&o&&o.key===Yi.key&&a.state.popupVisible&&a.setPopupVisible(!1)},a.onMouseEnter=function(o){var u=a.getMergedProps(["mouseEnterDelay"]).mouseEnterDelay;a.triggerPropsEvent("onMouseEnter",o),a.clearDelayTimer(),a.setPopupVisible(!0,u||0)},a.onMouseMove=function(o){a.triggerPropsEvent("onMouseMove",o),a.setMouseLocation(o),a.state.popupVisible&&a.update()},a.onMouseLeave=function(o){var u=a.getMergedProps(["mouseLeaveDelay"]).mouseLeaveDelay;a.clearDelayTimer(),a.triggerPropsEvent("onMouseLeave",o),a.isMouseLeaveToClose()&&a.state.popupVisible&&a.setPopupVisible(!1,u||0)},a.onPopupMouseEnter=function(){a.clearDelayTimer()},a.onPopupMouseLeave=function(o){a.onMouseLeave(o)},a.setMouseLocation=function(o){a.getMergedProps(["alignPoint"]).alignPoint&&(a.mouseLocation={clientX:o.clientX,clientY:o.clientY})},a.onContextMenu=function(o){o.preventDefault(),a.triggerPropsEvent("onContextMenu",o),a.setMouseLocation(o),a.state.popupVisible?a.getMergedProps(["alignPoint"]).alignPoint&&a.update():a.setPopupVisible(!0,0)},a.clickToHidePopup=function(o){var u=a.state.popupVisible;u&&(a.mousedownToHide=!0),a.triggerPropsEvent("onClick",o),a.isClickToHide()&&u&&a.setPopupVisible(!u,0)},a.onClick=function(o){var u=a.state.popupVisible;u&&(a.mousedownToHide=!0),a.triggerPropsEvent("onClick",o),a.setMouseLocation(o),!(!a.isClickToHide()&&u)&&a.setPopupVisible(!u,0)},a.onFocus=function(o){var u=a.getMergedProps(["focusDelay"]).focusDelay,c=function(){a.triggerPropsEvent("onFocus",o)};a.clearDelayTimer(),a.mousedownToHide||(a.state.popupVisible?c==null||c():a.setPopupVisible(!0,u||0,c)),a.mousedownToHide=!1},a.onBlur=function(o){a.setPopupVisible(!1,200,function(){return a.triggerPropsEvent("onBlur",o)})},a.onResize=function(){a.getMergedProps(["autoFixPosition"]).autoFixPosition&&a.state.popupVisible&&a.updatePopupPosition()},a.onPopupMouseDown=function(){a.hasPopupMouseDown=!0,clearTimeout(a.mouseDownTimeout),a.mouseDownTimeout=setTimeout(function(){a.hasPopupMouseDown=!1},0)},a.getChild=function(){var o,u=a.props.children,c=u,f=c&&typeof c!="string"&&c.type,v=u;if(["string","number"].indexOf(typeof u)>-1||l.Children.count(u)>1)v=l.createElement("span",null,u);else if(c&&f&&(f.__BYTE_BUTTON===!0||f.__BYTE_CHECKBOX===!0||f.__BYTE_SWITCH===!0||f.__BYTE_RADIO===!0||f==="button")&&c.props.disabled){var m=Yg(c.props.style,["position","left","right","top","bottom","float","display","zIndex"]),p=m.picked,g=m.omitted;v=l.createElement("span",{className:(o=c.props)===null||o===void 0?void 0:o.className,style:st(st({display:"inline-block"},p),{cursor:"not-allowed"})},l.cloneElement(c,{style:st(st({},g),{pointerEvents:"none"}),className:void 0}))}return v||l.createElement("span",null)},a.appendToContainer=function(o){if(ht(a.rafId),a.isDidMount){var u=a.context.getPopupContainer,c=a.getMergedProps(["getPopupContainer"]).getPopupContainer,f=c||u,v=a.getRootElement(),m=f(v);if(m){m.appendChild(o);return}}a.rafId=Ct(function(){a.appendToContainer(o)})},a.getContainer=function(){var o=document.createElement("div");return o.style.width="100%",o.style.position="absolute",o.style.top="0",o.style.left="0",a.popupContainer=o,a.appendToContainer(o),o},a.triggerPropsEvent=function(o,u){var c=a.getChild(),f=c&&c.props&&c.props[o],v=a.getMergedProps([o])[o];Ke(f)&&f(u),Ke(v)&&v(u)},a.triggerOriginEvent=function(o){var u=a.getChild(),c=u&&u.props&&u.props[o],f=a.getMergedProps([o])[o];return Ke(f)&&Ke(c)?function(v){c(v),f(v)}:c||f};var i=a.getMergedProps(t),s="popupVisible"in i?i.popupVisible:i.defaultPopupVisible;return a.popupOpen=!!s,a.triggerRef=d.createRef(),a.state={popupVisible:!!s,popupStyle:{}},a}return n.getDerivedStateFromProps=function(t,r){return"popupVisible"in t&&t.popupVisible!==r.popupVisible?{popupVisible:t.popupVisible}:null},n.prototype.componentDidMount=function(){this.componentDidUpdate(this.getMergedProps()),this.isDidMount=!0,this.unmount=!1,this.childrenDom=this.getRootElement(),this.state.popupVisible&&(this.childrenDomSize=Us(this.childrenDom,{boundaryDistance:this.props.alignPoint?void 0:this.props.boundaryDistance,position:this.props.position}))},n.prototype.componentDidUpdate=function(t){var r=this.getMergedProps(t),a=this.getMergedProps();!r.popupVisible&&a.popupVisible&&this.update();var i=this.state.popupVisible;this.popupOpen=i;var s=a.getDocument;if(!i){this.offClickOutside(),this.offContainerResize(),this.offWindowResize(),this.offScrollListeners();return}var o=Us(this.childrenDom,{boundaryDistance:this.props.alignPoint?{}:this.props.boundaryDistance,position:this.props.position});if(JSON.stringify(o)!==JSON.stringify(this.childrenDomSize)&&(this.updatePopupPosition(),this.childrenDomSize=o),this.onContainerResize(),(a.updateOnScroll||a.containerScrollToClose)&&this.onContainersScroll(a),this.handleWindowResize||(it(window,"resize",this.handleUpdatePosition),this.handleWindowResize=!0),!this.handleClickOutside){var u=Ke(s)&&s();u&&(it(u,"mousedown",this.onClickOutside,{capture:xe(a.clickOutsideToClose)?a.clickOutsideToClose.capture:!1}),this.handleClickOutside=!0)}},n.prototype.componentWillUnmount=function(){this.unmount=!0,this.offClickOutside(),this.clearTimer(),this.offWindowResize(),this.offScrollListeners(),this.offContainerResize(),ht(this.rafId)},n.prototype.clearDelayTimer=function(){this.delayTimer&&(clearTimeout(this.delayTimer),this.delayTimer=null)},n.prototype.render=function(){var t,r,a,i=this,s,o=this.getMergedProps(),u=o.children,c=o.style,f=o.className,v=o.arrowProps,m=o.disabled,p=o.popup,g=o.classNames,h=o.duration,b=o.unmountOnExit,y=o.alignPoint,C=o.autoAlignPopupWidth,w=o.position,E=o.childrenPrefix,O=o.showArrow,x=o.popupStyle,_=o.__onExit,T=o.__onExited,P=u||u===0,I=this.context,j=I.getPrefixCls,M=I.zIndex,R=I.rtl,k=this.state,L=k.popupVisible,S=k.popupStyle;if(!p)return null;var N={},A={onMouseDown:this.onPopupMouseDown};this.isHoverTrigger()&&!m?(N.onMouseEnter=this.onMouseEnter,N.onMouseLeave=this.onMouseLeave,this.isClickToHide()&&(N.onClick=this.clickToHidePopup),y&&(N.onMouseMove=this.onMouseMove),this.isPopupHoverHide()||(A.onMouseEnter=this.onPopupMouseEnter,A.onMouseLeave=this.onPopupMouseLeave)):(N.onMouseEnter=this.triggerOriginEvent("onMouseEnter"),N.onMouseLeave=this.triggerOriginEvent("onMouseLeave")),this.isContextMenuTrigger()&&!m?(N.onContextMenu=this.onContextMenu,N.onClick=this.clickToHidePopup):N.onContextMenu=this.triggerOriginEvent("onContextMenu"),this.isClickTrigger()&&!m?N.onClick=this.onClick:N.onClick=N.onClick||this.triggerOriginEvent("onClick"),this.isFocusTrigger()&&!m?(N.onFocus=this.onFocus,this.isBlurToHide()&&(N.onBlur=this.onBlur)):(N.onFocus=this.triggerOriginEvent("onFocus"),N.onBlur=this.triggerOriginEvent("onBlur")),m?N.onKeyDown=this.triggerOriginEvent("onKeyDown"):N.onKeyDown=this.onKeyDown;var W=this.getChild(),D=l.Children.only(p());W.props.className&&(N.className=W.props.className),E&&L&&(N.className=N.className?N.className+" "+E+"-open":E+"-open"),this.isFocusTrigger()&&(N.tabIndex=m?-1:0);var F=j("trigger"),H=K(F,E,F+"-position-"+w,(t={},t[F+"-rtl"]=R,t),f),z=P&&l.createElement(pt,{onResize:this.onResize,getTargetDOMNode:function(){return i.rootElementRef}},l.cloneElement(W,st(st({},N),{ref:Yo(W)?function(V){i.rootElementRef=V,nl(W,V)}:void 0}))),B=l.createElement(nf,{in:!!L,timeout:h,classNames:g,unmountOnExit:b,appear:!0,mountOnEnter:!0,onEnter:function(){i.triggerRefDestoried=!1;var V=i.getPopupElement();V&&(V.style.display="initial",V.style.pointerEvents="none",g==="slideDynamicOrigin"&&(V.style.transform=i.getTransformTranslate()))},onEntering:function(){var V=i.getPopupElement();V&&g==="slideDynamicOrigin"&&(V.style.transform="")},onEntered:function(){var V=i.getPopupElement();V&&(V.style.pointerEvents="auto",i.forceUpdate())},onExit:function(){var V=i.getPopupElement();V&&(V.style.pointerEvents="none",_==null||_(V))},onExited:function(){var V=i.getPopupElement();V&&(V.style.display="none",b&&(i.triggerRefDestoried=!0),i.setState({popupStyle:{}}),T==null||T(V))},nodeRef:this.triggerRef},l.createElement(pt,{onResize:function(){var V=i.triggerRef.current;if(V){var $=i.getPopupStyle(),X=i.props.style||{};V.style.top=String(X.top||$.top+"px"),V.style.left=String(X.left||$.left+"px")}i.onResize()},getTargetDOMNode:function(){return i.getPopupElement()}},l.createElement("span",st({ref:this.triggerRef,"trigger-placement":this.realPosition,style:st(st(st({width:C&&(c==null?void 0:c.width)===void 0?(s=this.childrenDomSize)===null||s===void 0?void 0:s.width:""},S),{position:"absolute",zIndex:M||""}),c)},A,{className:H},ir(this.props)),l.createElement(D.type,st({ref:D.ref},D.props,{style:st(st({},D.props.style),x)})),(O||v)&&l.createElement("div",{className:K(F+"-arrow-container",(r={},r[E+"-arrow-container"]=E,r))},l.createElement("div",st({},v,{className:K(F+"-arrow",(a={},a[E+"-arrow"]=E,a),v==null?void 0:v.className),style:st(st({},this.arrowStyle),v==null?void 0:v.style)})))))),re=L||this.getPopupElement()&&!this.triggerRefDestoried?l.createElement(Hg,{getContainer:this.getContainer},B):null;return P?l.createElement(l.Fragment,null,z,re):re},n.displayName="Trigger",n.contextType=ye,n}(d.PureComponent);const vl=Jg;var Ji=globalThis&&globalThis.__assign||function(){return Ji=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ji.apply(this,arguments)},Qg=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function eh(e,n){var t=e.prefixCls,r=e.label,a=Qg(e,["prefixCls","label"]);return l.createElement("li",Ji({ref:n,className:t+"-group-title"},Ge(a,["_key","children","isSelectOptGroup"])),r)}var th=l.forwardRef(eh),Bf=th;Bf.__ARCO_SELECT_OPTGROUP__=!0;const rh=Bf;var Qi=globalThis&&globalThis.__assign||function(){return Qi=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Qi.apply(this,arguments)},hi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Zs=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},nh={isCheckboxGroup:!1,checkboxGroupValue:[],onGroupChange:function(){},registerValue:function(){},unRegisterValue:function(){}},Kf=d.createContext(nh);function Gf(e){var n,t=hi(ot([],{defaultValue:"defaultValue"in e?e.defaultValue||[]:void 0,value:"value"in e?e.value||[]:void 0}),2),r=t[0],a=t[1],i=hi(d.useState([]),2),s=i[0],o=i[1],u=d.useContext(ye),c=u.getPrefixCls,f=u.rtl,v=e.disabled,m=e.options,p=e.style,g=e.className,h=e.error,b=e.children,y=e.direction,C=y===void 0?"horizontal":y,w=c("checkbox"),E=K(w+"-group",(n={},n[w+"-group-is-error"]=h,n[w+"-group-direction-"+C]=C,n[w+"-group-rtl"]=f,n),g),O=d.useCallback(function(x,_,T){var P=r.slice();_?P.push(x):P.splice(r.indexOf(x),1),a(P),e.onChange&&e.onChange(P.filter(function(I){return s.indexOf(I)>-1}),T)},[r,e.onChange,s]);return l.createElement("span",Qi({className:E,style:p},Im(e)),l.createElement(Kf.Provider,{value:{isCheckboxGroup:!0,checkboxGroupValue:r,onGroupChange:O,disabled:v,registerValue:function(x){o(function(_){return Array.from(new Set(Zs(Zs([],hi(_),!1),[x],!1)))})},unRegisterValue:function(x){o(function(_){return _.filter(function(T){return T!==x})})}}},ct(m)?m.map(function(x){var _=xe(x)?x.label:x,T=xe(x)?x.value:x,P=xe(x)?x.icon:void 0;return l.createElement(Uf,{disabled:v||xe(x)&&x.disabled,key:T,value:T,icon:P},_)}):b))}Gf.displayName="CheckboxGroup";var vn=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Yn=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},ah=function(e,n){var t=vn(d.useState(Yn([],vn(n||[]),!1)),2),r=t[0],a=t[1],i=d.useMemo(function(){var g=function(b){return r.indexOf(b)>-1},h=function(b,y){var C=ct(b)?b:[b],w;y?w=Yn(Yn([],vn(r),!1),vn(C),!1):w=r.filter(function(E){return C.indexOf(E)===-1}),a(Array.from(new Set(w)))};return{isSelected:g,setValueSelected:h}},[r]),s=i.isSelected,o=i.setValueSelected,u=d.useMemo(function(){var g=function(){a(e)},h=function(){a([])},b=function(w){w===void 0&&(w=e);var E=ct(w)?w:[w],O=Yn([],vn(r),!1);E.forEach(function(x){var _=O.indexOf(x);_>-1?O.splice(_,1):O.push(x)}),a(O)},y=function(){return e.every(function(w){return s(w)})},C=function(){return e.some(function(w){return s(w)})&&!y()};return{selectAll:g,unSelectAll:h,toggle:b,isAllSelected:y,isPartialSelected:C}},[r,e,s]),c=u.selectAll,f=u.unSelectAll,v=u.toggle,m=u.isAllSelected,p=u.isPartialSelected;return{selected:r,setSelected:a,setValueSelected:o,selectAll:c,unSelectAll:f,toggle:v,isSelected:s,isAllSelected:m,isPartialSelected:p}};const ih=ah;function oh(e){var n=e.className;return l.createElement("svg",{className:n,"aria-hidden":!0,focusable:!1,viewBox:"0 0 1024 1024",width:"200",height:"200",fill:"currentColor"},l.createElement("path",{d:"M877.44815445 206.10060629a64.72691371 64.72691371 0 0 0-95.14856334 4.01306852L380.73381888 685.46812814 235.22771741 533.48933518a64.72691371 64.72691371 0 0 0-92.43003222-1.03563036l-45.82665557 45.82665443a64.72691371 64.72691371 0 0 0-0.90617629 90.61767965l239.61903446 250.10479331a64.72691371 64.72691371 0 0 0 71.19960405 15.14609778 64.33855261 64.33855261 0 0 0 35.08198741-21.23042702l36.24707186-42.71976334 40.5190474-40.77795556-3.36579926-3.49525333 411.40426297-486.74638962a64.72691371 64.72691371 0 0 0-3.88361443-87.64024149l-45.3088404-45.43829334z","p-id":"840"}))}var ba=globalThis&&globalThis.__assign||function(){return ba=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ba.apply(this,arguments)},lh=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},sh=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function ch(e,n){var t,r=d.useRef(null),a=d.useContext(ye),i=a.getPrefixCls,s=a.componentConfig,o=a.rtl,u=He(e,{},s==null?void 0:s.Checkbox),c=d.useContext(Kf),f=i("checkbox"),v=c.onGroupChange,m=ba({},u);c.isCheckboxGroup&&(m.checked=c.checkboxGroupValue.indexOf(u.value)!==-1,m.disabled="disabled"in u?u.disabled:c.disabled);var p=m.disabled,g=m.children,h=m.className,b=m.value,y=m.style,C=m.indeterminate,w=m.error,E=lh(m,["disabled","children","className","value","style","indeterminate","error"]),O=sh(ot(!1,{value:m.checked,defaultValue:m.defaultChecked}),2),x=O[0],_=O[1],T=K(f,(t={},t[f+"-disabled"]=!!p,t[f+"-indeterminate"]=!!C,t[f+"-checked"]=x,t[f+"-rtl"]=o,t.error=w,t),h);d.useEffect(function(){return c.registerValue(b),function(){c.unRegisterValue(b)}},[b]);var P=d.useCallback(function(M){M.persist(),M.stopPropagation(),_(M.target.checked),c.isCheckboxGroup&&v&&v(u.value,M.target.checked,M),u.onChange&&u.onChange(M.target.checked,M)},[v,c.isCheckboxGroup,u.onChange,u.value]),I=l.useCallback(function(M){Ke(u.children)&&(M.preventDefault(),r.current&&r.current.click()),E.onClick&&E.onClick(M)},[u.children,E.onClick]),j=l.createElement(oh,{className:f+"-mask-icon"});return m.icon&&(l.isValidElement(m.icon)?j=l.cloneElement(m.icon,{className:f+"-mask-icon"}):j=m.icon),l.createElement("label",ba({ref:n,"aria-disabled":p},Ge(E,["onChange"]),{onClick:I,className:T,style:y}),l.createElement("input",{value:b,disabled:!!p,ref:r,checked:!!x,onChange:P,onClick:function(M){return M.stopPropagation()},type:"checkbox"}),Ke(g)?g({checked:x,indeterminate:C}):l.createElement(l.Fragment,null,l.createElement(Nt,{prefix:f,className:f+"-mask-wrapper",disabled:x||p||C},l.createElement("div",{className:f+"-mask"},j)),!Ov(g)&&l.createElement("span",{className:f+"-text"},g)))}var Xa=l.forwardRef(ch);Xa.displayName="Checkbox";Xa.Group=Gf;Xa.useCheckbox=ih;const Uf=Xa;var kr=globalThis&&globalThis.__assign||function(){return kr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},kr.apply(this,arguments)},uh=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function fh(e,n){var t,r,a=e.style,i=e.className,s=e.wrapperClassName,o=e.disabled,u=e.prefixCls,c=e.rtl,f=e.value,v=e.children,m=e._isMultipleMode,p=e._isUserCreatedOption,g=e._isUserCreatingOption,h=e._valueActive,b=e._valueSelect,y=e._onMouseEnter,C=e._onMouseLeave,w=e._onClick,E=uh(e,["style","className","wrapperClassName","disabled","prefixCls","rtl","value","children","_isMultipleMode","_isUserCreatedOption","_isUserCreatingOption","_valueActive","_valueSelect","_onMouseEnter","_onMouseLeave","_onClick"]),O="value"in e?f:""+v,x="children"in e?v:""+f,_=m?b.indexOf(O)!==-1:b===O,T=kr({style:a,className:K(u+"-option",(t={},t[u+"-option-selected"]=_,t[u+"-option-disabled"]=o,t[u+"-option-hover"]=O===h,t[u+"-option-empty"]=!x&&x!==0||typeof x=="string"&&/^\s*$/.test(x),t[u+"-option-rtl"]=c,t),i),onMouseEnter:function(I){y&&y(O),E.onMouseEnter&&E.onMouseEnter(I)},onMouseLeave:function(I){C==null||C(),E.onMouseLeave&&E.onMouseLeave(I)},onClick:function(I){w&&w(O,o),E.onClick&&E.onClick(I)}},Ge(E,["_key","extra","isSelectOption","onClick","onMouseEnter","onMouseLeave"])),P={ref:n,role:"option","aria-selected":_};return p&&Object.assign(P,{"data-user-created":!0}),g&&Object.assign(P,{"data-user-creating":!0}),m?l.createElement("li",kr({},P,{className:K(u+"-option-wrapper",(r={},r[u+"-option-wrapper-selected"]=_,r[u+"-option-wrapper-disabled"]=o,r),s)}),l.createElement(Uf,{"aria-hidden":"true",className:u+"-checkbox",checked:_,disabled:o,onChange:T.onClick}),l.createElement("span",kr({},T),x)):l.createElement("li",kr({},P,T),x)}var dh=l.forwardRef(fh),Zf=dh;Zf.__ARCO_SELECT_OPTION__=!0;const Xf=Zf;var Xs=function(e){return JSON.stringify({code:e.code,ctrl:!!e.ctrl,shift:!!e.shift,alt:!!e.alt,meta:!!e.meta})};function Yf(e){var n={};return e.forEach(function(t,r){r=typeof r=="number"?{code:r}:r,n[Xs(r)]=t}),function(t){var r=Xs({code:t.keyCode||t.which,ctrl:!!t.ctrlKey,shift:!!t.shiftKey,alt:!!t.altKey,meta:!!t.metaKey}),a=n[r];a&&(t.stopPropagation(),a(t)===!1&&t.preventDefault())}}function Ys(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function qs(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Ys(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ys(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function vh(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=qs(qs({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-down")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M39.6 17.443 24.043 33 8.487 17.443"}))}var pl=l.forwardRef(vh);pl.defaultProps={isIcon:!0};pl.displayName="IconDown";const Bn=pl;var Pn=globalThis&&globalThis.__assign||function(){return Pn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Pn.apply(this,arguments)},ph=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},yi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},mh=["red","orangered","orange","gold","lime","green","cyan","blue","arcoblue","purple","pinkpurple","magenta","gray"],gh={size:"default"};function hh(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=Er(),u=He(e,gh,i==null?void 0:i.Tag),c=u.className,f=u.style,v=u.children,m=u.color,p=u.closable,g=u.checkable,h=u.defaultChecked,b=u.size,y=u.onClose,C=u.onCheck,w=u.icon,E=u.closeIcon,O=u.bordered,x=u.__closeIconProps,_=ph(u,["className","style","children","color","closable","checkable","defaultChecked","size","onClose","onCheck","icon","closeIcon","bordered","__closeIconProps"]),T=a("tag"),P=yi(d.useState("visible"in u?u.visible:!0),2),I=P[0],j=P[1],M=yi(d.useState("checked"in u?u.checked:h),2),R=M[0],k=M[1],L=yi(d.useState(),2),S=L[0],N=L[1],A="checked"in u?u.checked:R,W="visible"in u?u.visible:I;function D($){var X=y&&y($);X&&X.then?(N(!0),X.then(function(){N(!1),j(!1)}).catch(function(){N(!1)})):j(!1)}function F(){var $=!A;"checked"in u||k($),C&&C($)}var H=m&&mh.indexOf(m)!==-1?m:"",z=g?A:!0,B=K(T,(t={},t[T+"-loading"]=S,t[T+"-hide"]=!W,t[T+"-"+H]=H,t[T+"-checkable"]=g,t[T+"-checked"]=z,t[T+"-size-"+b]=b,t[T+"-bordered"]=O,t[T+"-custom-color"]=z&&m&&!H,t[T+"-rtl"]=s,t),c),re=Pn({},f);m&&!H&&z&&(re.backgroundColor=m,re.borderColor=m);var V=Ge(_,["visible"]);return g&&(V.onClick=F),l.createElement("div",Pn({ref:n,style:re,className:B},V),w&&l.createElement("span",{className:T+"-icon"},w),l.createElement("span",{className:T+"-content"},v),p&&!S&&E!==null&&l.createElement(Nt,Pn({prefix:T,className:T+"-close-btn",onClick:D,role:"button",tabIndex:0},o({onPressEnter:D}),{"aria-label":"Close"},x),E!==void 0?E:l.createElement(Dt,null)),S&&l.createElement("span",{className:T+"-loading-icon"},l.createElement(Yr,null)))}var qf=d.forwardRef(hh);qf.displayName="Tag";const eo=qf;var Ht=globalThis&&globalThis.__assign||function(){return Ht=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ht.apply(this,arguments)},yh=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},bh={position:"top",trigger:"hover",escToClose:!1,unmountOnExit:!0,blurToHide:!0,popupHoverStay:!0},xh={enter:300,exit:100},Ch={left:12,right:12,top:12,bottom:12};function Eh(e,n){var t=d.useContext(ye),r=t.getPrefixCls,a=t.componentConfig,i=He(e,bh,a==null?void 0:a.Tooltip),s=i.style,o=i.className,u=i.children,c=i.trigger,f=i.escToClose,v=i.defaultPopupVisible,m=i.position,p=i.unmountOnExit,g=i.popupVisible,h=i.prefixCls,b=i.blurToHide,y=i.popupHoverStay,C=i.disabled,w=i.onVisibleChange,E=i.triggerProps,O=i.childrenPrefix,x=i.getPopupContainer,_=i.content,T=i.mini,P=i.color,I=yh(i,["style","className","children","trigger","escToClose","defaultPopupVisible","position","unmountOnExit","popupVisible","prefixCls","blurToHide","popupHoverStay","disabled","onVisibleChange","triggerProps","childrenPrefix","getPopupContainer","content","mini","color"]),j=d.useRef(),M=function(S,N){S===void 0&&(S=0),j.current&&j.current.updatePopupPosition(S,N)};d.useImperativeHandle(n,function(){return{updatePopupPosition:M,getRootDOMNode:function(){var S,N;return(N=(S=j.current)===null||S===void 0?void 0:S.getRootDOMNode)===null||N===void 0?void 0:N.call(S)}}},[]);var R=h||r("tooltip"),k=Ht(Ht(Ht(Ht({},Ka(I,Ff)),ir(I)),E),{className:K(o,E==null?void 0:E.className)}),L=Ke(_)?_():_;return"popupVisible"in i?k.popupVisible=g:sa(L,!0)&&(k.popupVisible=!1),(k.showArrow!==!1||k.arrowProps)&&(k.arrowProps=k.arrowProps||{},P&&(k.arrowProps.style=Ht({backgroundColor:P},k.arrowProps.style))),l.createElement(vl,Ht({style:Ht({maxWidth:350},s),ref:j,classNames:"zoomInFadeOut",duration:xh,popup:function(){var S;return l.createElement("div",{style:{backgroundColor:P},className:K(R+"-content",R+"-content-"+m,(S={},S[R+"-mini"]=T,S)),role:"tooltip"},l.createElement("div",{className:R+"-content-inner"},L))},position:m,disabled:C,trigger:c,escToClose:f,showArrow:!0,popupAlign:Ch,mouseEnterDelay:200,mouseLeaveDelay:200,unmountOnExit:p,popupHoverStay:y,blurToHide:b,childrenPrefix:O||R,getPopupContainer:x,onVisibleChange:w,defaultPopupVisible:v},k),u)}var Jf=d.forwardRef(Eh);Jf.displayName="Tooltip";const Cr=Jf;var xa=globalThis&&globalThis.__assign||function(){return xa=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},xa.apply(this,arguments)},wh=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Oh={position:"top",trigger:"hover",unmountOnExit:!0};function _h(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,Oh,i==null?void 0:i.Popover),u=o.style,c=o.className,f=o.children,v=o.position,m=o.getPopupContainer,p=o.trigger,g=o.defaultPopupVisible,h=o.popupVisible,b=o.triggerProps,y=o.unmountOnExit,C=o.onVisibleChange,w=o.content,E=o.title,O=wh(o,["style","className","children","position","getPopupContainer","trigger","defaultPopupVisible","popupVisible","triggerProps","unmountOnExit","onVisibleChange","content","title"]),x=a("popover"),_=Ke(E)?E():E,T=Ke(w)?w():w,P=sa(_,!0)&&sa(T,!0)?null:l.createElement("div",{className:K(x+"-inner",(t={},t[x+"-inner-rtl"]=s,t))},_?l.createElement("div",{className:x+"-title"},_):null,l.createElement("div",{className:x+"-inner-content"},T));return l.createElement(Cr,xa({},O,{ref:n,style:xa({maxWidth:350},u),className:c,prefixCls:x,getPopupContainer:m,position:v,trigger:p,content:P,popupHoverStay:!0,unmountOnExit:y,triggerProps:b,defaultPopupVisible:g,onVisibleChange:C||(b?b.onVisibleChange:void 0),childrenPrefix:x},"popupVisible"in o?{popupVisible:h}:{}),typeof f=="string"?l.createElement("span",null,f):f)}var Qf=d.forwardRef(_h);Qf.displayName="Popover";const ml=Qf;var bi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function Ph(e){var n,t=e.prefixCls,r=e.style,a=e.children,i=e.direction,s=e.disabled,o=e.droppable,u=o===void 0?!0:o,c=e.onDrop,f=e.onDragStart,v=e.onDragEnd,m=e.onDragOver,p=e.onDragLeave,g=d.useRef(null),h=d.useRef(null),b=bi(d.useState("none"),2),y=b[0],C=b[1],w=bi(d.useState(!1),2),E=w[0],O=w[1],x=bi(d.useState(null),2),_=x[0],T=x[1];return d.useEffect(function(){return function(){h.current&&clearTimeout(h.current)}},[]),d.useEffect(function(){y==="dragged"&&(h.current=setTimeout(function(){return C("none")},1e3))},[y]),l.createElement("li",{draggable:!0,ref:g,style:r,className:K(t+"-item",(n={},n[t+"-item-"+y]=y!=="none",n[t+"-item-gap-"+_]=_,n[t+"-item-disabled"]=s,n[t+"-item-dragover"]=E,n)),onDragStart:function(P){P.stopPropagation(),C("dragging");try{P.dataTransfer.setData("text/plain","")}catch{}f&&f(P)},onDragEnd:function(P){P.stopPropagation(),O(!1),C("dragged"),v&&v(P)},onDragOver:function(P){if(u){P.stopPropagation(),P.preventDefault();var I=g.current.getBoundingClientRect();T(i==="vertical"?P.pageY>window.pageYOffset+I.top+I.height/2?"bottom":"top":P.pageX>window.pageXOffset+I.left+I.width/2?"right":"left"),O(!0),m&&m(P)}},onDragLeave:function(P){u&&(P.stopPropagation(),O(!1),p&&p(P))},onDrop:function(P){u&&(P.stopPropagation(),P.preventDefault(),O(!1),T(null),C("none"),c&&c(P,_))}},a)}var Th=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function Sh(e){var n=d.useContext(ye).getPrefixCls,t=n("draggable"),r=e.className,a=e.children,i=e.direction,s=i===void 0?"vertical":i,o=e.onIndexChange,u=e.itemWrapperStyle,c=Th(d.useState(null),2),f=c[0],v=c[1];return l.createElement("div",{className:K(t,r)},l.Children.map(a,function(m,p){return l.createElement(Ph,{style:u,prefixCls:t,direction:s,onDragStart:function(){return v(p)},onDragEnd:function(){return v(null)},onDrop:function(g,h){var b=f,y=h==="left"||h==="top"?p:p+1;o&&b!==y&&o(y,b)}},m)}))}function Js(e){var n,t=d.useContext(ye).getPrefixCls,r=t("overflow-item"),a=d.useRef();d.useEffect(function(){return e.onResize(a.current),function(){e.unregister(a.current)}},[]);var i=e.hidden;return l.createElement(pt,{onResize:function(s){e.onResize(s==null?void 0:s[0].target)}},l.createElement("div",{ref:a,className:K(r,e.className,(n={},n[r+"-hidden"]=i,n))},e.children))}var Tn=globalThis&&globalThis.__assign||function(){return Tn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Tn.apply(this,arguments)},pn=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Nh=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function Ih(e){var n=d.useContext(ye).getPrefixCls,t=n("overflow"),r=e.items,a=e.suffixItems,i=e.ellipsisNode||function(E){var O=E.ellipsisCount;return"+"+O},s=pn(d.useState(),2),o=s[0],u=s[1],c=pn(d.useState(),2),f=c[0],v=c[1],m=pn(d.useState({}),2),p=m[0],g=m[1],h=pn(d.useState({}),2),b=h[0],y=h[1],C=r.length-f,w=C>0?i({ellipsisCount:C}):null;return d.useLayoutEffect(function(){var E=Object.values(p).length,O=Object.values(b).reduce(function(_,T){return _+((T==null?void 0:T.width)||0)},0),x=E;Object.keys(p).some(function(_,T){var P=p[_];if(P&&O+P.width>o)return x=T,!0;O+=(P==null?void 0:P.width)||0}),v(Math.max(x,0))},[p,o,b]),l.createElement(pt,{onResize:function(E){var O;u(((O=E==null?void 0:E[0])===null||O===void 0?void 0:O.target.clientWidth)||0)}},l.createElement("div",{className:K(t,e.className)},r.map(function(E,O){var x,_=(((x=E)===null||x===void 0?void 0:x.key)||O)+"_overflow_item_"+O;return l.createElement(Js,{key:_,onResize:function(T){g(function(P){return P[_]={node:T,width:T.clientWidth},P})},unregister:function(){g(function(T){return delete T[_],T})},hidden:f<O+1},E)}),Nh([w],pn(a),!1).map(function(E,O){if(!E)return null;var x=((E==null?void 0:E.key)||O)+"_overflow_suffix_item";return l.createElement(Js,{key:x,className:t+"-suffix-item",onResize:function(_){y(function(T){var P;return Tn(Tn({},T),(P={},P[""+x]={node:_,width:_.clientWidth},P))})},unregister:function(){y(function(_){return delete _[x],Tn({},_)})}},E)})))}var rr=globalThis&&globalThis.__assign||function(){return rr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},rr.apply(this,arguments)},mn=globalThis&&globalThis.__awaiter||function(e,n,t,r){function a(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function o(f){try{c(r.next(f))}catch(v){s(v)}}function u(f){try{c(r.throw(f))}catch(v){s(v)}}function c(f){f.done?i(f.value):a(f.value).then(o,u)}c((r=r.apply(e,n||[])).next())})},gn=globalThis&&globalThis.__generator||function(e,n){var t={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,a,i,s;return s={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function o(c){return function(f){return u([c,f])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,a&&(i=c[0]&2?a.return:c[0]?a.throw||((i=a.return)&&i.call(a),0):a.next)&&!(i=i.call(a,c[1])).done)return i;switch(a=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return t.label++,{value:c[1],done:!1};case 5:t.label++,a=c[1],c=[0];continue;case 7:c=t.ops.pop(),t.trys.pop();continue;default:if(i=t.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){t=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){t.label=c[1];break}if(c[0]===6&&t.label<i[1]){t.label=i[1],i=c;break}if(i&&t.label<i[2]){t.label=i[2],t.ops.push(c);break}i[2]&&t.ops.pop(),t.trys.pop();continue}c=n.call(e,t)}catch(f){c=[6,f],a=0}finally{r=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}},jh=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Pr=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Qs=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},to=300,Mh="responsive",Rh="__input_"+Math.random().toFixed(10).slice(2),hn=function(e){return e==null},qn=function(e){e.target.tagName!=="INPUT"&&e.preventDefault()},ec=function(e){return ct(e)?e.map(function(n){return xe(n)?rr(rr({},n),{label:"label"in n?n.label:n.value,value:n.value,closable:n.closable}):{label:n,value:n}}):[]},kh=function(e){var n=d.useRef(0),t=d.useRef(e.length);return d.useEffect(function(){n.current=e.length===0&&t.current>0?to:0,t.current=e.length},[e]),n},tc=function(e){var n=e.prefixCls,t=e.children,r=e.animation;return l.createElement("div",{className:n+"-inner"},r?l.createElement(rl,{component:null},t):t)},Ah={animation:!0,validate:function(e,n){return e&&n.every(function(t){return t.value!==e})}};function Dh(e,n){var t,r,a=this,i=d.useContext(ye),s=i.getPrefixCls,o=i.size,u=i.componentConfig,c=i.rtl,f=He(e,Ah,u==null?void 0:u.InputTag),v=f.className,m=f.style,p=f.placeholder,g=f.error,h=f.disabled,b=f.readOnly,y=f.allowClear,C=f.autoFocus,w=f.labelInValue,E=f.disableInput,O=f.animation,x=f.saveOnBlur,_=f.dragToSort,T=f.icon,P=f.prefix,I=f.suffix,j=f.addBefore,M=f.addAfter,R=f.tokenSeparators,k=f.validate,L=f.renderTag,S=f.tagClassName,N=f.maxTagCount,A=f.onInputChange,W=f.onKeyDown,D=f.onPaste,F=f.onChange,H=f.onFocus,z=f.onBlur,B=f.onPressEnter,re=f.onRemove,V=f.onClear,$=f.onClick,X=jh(f,["className","style","placeholder","error","disabled","readOnly","allowClear","autoFocus","labelInValue","disableInput","animation","saveOnBlur","dragToSort","icon","prefix","suffix","addBefore","addAfter","tokenSeparators","validate","renderTag","tagClassName","maxTagCount","onInputChange","onKeyDown","onPaste","onChange","onFocus","onBlur","onPressEnter","onRemove","onClear","onClick"]),Z=s("input-tag"),Y="size"in f?f.size:o,J=typeof N=="object"?N.count:N,ae=d.useRef(),ve=d.useRef(null),ne=d.useRef(),G=Pr(d.useState(!1),2),U=G[0],oe=G[1],ie=Pr(ot([],{defaultValue:"defaultValue"in f?ec(f.defaultValue):void 0,value:"value"in f?ec(f.value):void 0}),2),te=ie[0],pe=ie[1],Te=Pr(ot("",{value:f.inputValue}),2),Q=Te[0],me=Te[1],ee=kh(te),de=!!(_&&!b&&!h);d.useImperativeHandle(n,function(){var le,se;return{blur:(le=ae.current)===null||le===void 0?void 0:le.blur,focus:(se=ae.current)===null||se===void 0?void 0:se.focus,getRootDOMNode:function(){return ne.current}}},[]);var ce=function(le,se){h||b||("value"in f||pe(le),F&&F(w?le:le.map(function(Ee){return Ee==null?void 0:Ee.value}),se))},ge=function(le,se,Ee){re&&re(le,se,Ee),ce(Qs(Qs([],Pr(te.slice(0,se)),!1),Pr(te.slice(se+1)),!1),"remove")},fe=Yf(new Map([[Rf.code,function(le){if(!le.target.value&&te.length)for(var se=te.length-1;se>=0;se--){var Ee=te[se];if(Ee.closable!==!1){ge(Ee,se,le);return}}}]])),ue=function(){return mn(a,void 0,void 0,function(){var le,se,Ee;return gn(this,function(Se){switch(Se.label){case 0:return Se.trys.push([0,4,,5]),typeof k!="function"?[3,2]:[4,k(Q,te)];case 1:return se=Se.sent(),[3,3];case 2:se=!0,Se.label=3;case 3:return le=se,le&&(ce(te.concat({value:le===!0?Q:le,label:Q}),"add"),me("")),[3,5];case 4:return Ee=Se.sent(),console.error(Ee),[3,5];case 5:return[2]}})})},Pe=function(le,se,Ee){var Se;Ee===void 0&&(Ee=!1);var ze=le.value,_e=le.label,Me=!b&&!h&&le.closable!==!1,Re=typeof ze=="object"?se:ze,Xe=function(vt){ge(le,se,vt)};if(!Ee&&typeof J=="number"&&se>=J)return se===te.length-1?(_e=Oe(te.length-Number(J)),Me=!1,{valueKey:Re,dom:_e}):{valueKey:Re,dom:null};if(L)return{valueKey:Re,dom:L({value:ze,label:_e,closable:Me,onClose:Xe},se,te)};var Je={closable:Me,onClose:Xe,visible:!0,children:Ln(_e),closeIcon:T==null?void 0:T.removeIcon,__closeIconProps:{onMouseDown:qn},className:K(Z+"-tag",(Se={},Se[S]=S,Se)),title:typeof _e=="string"?_e:void 0};return{valueKey:Re,dom:l.createElement(eo,rr({key:Re+"-tag"},Je))}};function Oe(le){var se=typeof N=="object"&&N.render?N.render:function(){return l.createElement("span",{className:Z+"-tag-ellipsis"},"+",le)},Ee=xe(N)&&N.popoverProps;return l.createElement(ml,rr({content:l.createElement(l.Fragment,null,te.map(function(Se,ze){return{tagValue:Se,tagIndex:ze}}).slice(-le).map(function(Se){var ze,_e=Se.tagValue,Me=Se.tagIndex;return(ze=Pe(_e,Me,!0))===null||ze===void 0?void 0:ze.dom}))},Ee||{},{children:se(le,te)}))}var Ce=function(le){return mn(a,void 0,void 0,function(){var se,Ee,Se=this;return gn(this,function(ze){switch(ze.label){case 0:return ve.current=null,ct(R)&&R.length?(se=le.split(new RegExp("["+R.join("")+"]")),se.length>1?(ve.current=Date.now(),Ee=[],[4,Promise.all(se.map(function(_e){return mn(Se,void 0,void 0,function(){var Me,Re,Xe;return gn(this,function(Je){switch(Je.label){case 0:return _e?typeof k!="function"?[3,2]:[4,k(_e,te)]:[3,4];case 1:return Xe=Je.sent(),[3,3];case 2:Xe=!0,Je.label=3;case 3:return Re=Xe,[3,5];case 4:Re=!1,Je.label=5;case 5:return Me=Re,Me&&Ee.push({value:Me===!0?_e:Me,label:_e}),[2]}})})}))]):[3,2]):[3,2];case 1:ze.sent(),Ee.length&&ce(te.concat(Ee),"add"),ze.label=2;case 2:return[2]}})})},Ne=y&&!h&&!b&&te.length?l.createElement(Nt,{size:Y,key:"clearIcon",className:Z+"-clear-icon",onClick:function(le){var se;le.stopPropagation(),ce([],"clear"),U||(se=ae.current)===null||se===void 0||se.focus(),V==null||V()}},T&&T.clearIcon||l.createElement(Dt,null)):null,Ae=h||E,De=d.useMemo(function(){var le={},se=te.map(function(Ee,Se){var ze=le[Ee.value]||0,_e=ze>=1;le[Ee.value]=ze+1;var Me=Pe(Ee,Se),Re=Me.dom,Xe=Me.valueKey;return l.isValidElement(Re)?l.createElement(nr,{key:_e?Xe+"-"+Se:Xe,timeout:to,classNames:"zoomIn"},Re):Re});return se},[te]),Fe=[l.createElement(nr,{key:Rh,timeout:to,classNames:"zoomIn"},l.createElement(dl,{autoComplete:"off",size:Y,disabled:Ae,readOnly:b,ref:ae,autoFocus:C,placeholder:te.length?"":p,prefixCls:Z+"-input",autoFitWidth:{delay:function(){return ee.current},pure:!0},onPressEnter:function(le){return mn(a,void 0,void 0,function(){return gn(this,function(se){switch(se.label){case 0:return Q&&le.preventDefault(),B==null||B(le),[4,ue()];case 1:return se.sent(),[2]}})})},onFocus:function(le){!Ae&&!b&&(oe(!0),H==null||H(le))},onBlur:function(le){return mn(a,void 0,void 0,function(){return gn(this,function(se){switch(se.label){case 0:return oe(!1),z==null||z(le),x?[4,ue()]:[3,2];case 1:se.sent(),se.label=2;case 2:return me(""),[2]}})})},value:Q,onChange:function(le,se){A==null||A(le,se),se.nativeEvent.inputType!=="insertFromPaste"&&Ce(le),ve.current?me(""):me(le)},onKeyDown:function(le){fe(le),W==null||W(le)},onPaste:function(le){D==null||D(le),Ce(le.clipboardData.getData("text"))}}))],tt=!hn(P),Be=!hn(I)||!hn(Ne),dt=!hn(j),rt=!hn(M),he=dt||rt,Ie=f.status||(g?"error":void 0),qe=K(Z,(t={},t[Z+"-size-"+Y]=Y,t[Z+"-disabled"]=h,t[Z+"-"+Ie]=Ie,t[Z+"-focus"]=U,t[Z+"-readonly"]=b,t[Z+"-has-suffix"]=Be,t[Z+"-has-placeholder"]=!te.length,t[Z+"-rtl"]=c,t)),Le={style:m,className:v},we=l.createElement("div",rr({},Ge(X,["status","size","defaultValue","value","inputValue"]),he?{}:Le,{className:he?qe:K(qe,Le.className),onMouseDown:function(le){U&&qn(le)},onClick:function(le){var se;!U&&((se=ae.current)===null||se===void 0||se.focus()),$&&$(le)},ref:he?void 0:ne}),l.createElement("div",{className:Z+"-view"},tt&&l.createElement("div",{className:Z+"-prefix",onMouseDown:qn},P),de?l.createElement(tc,{key:"transitionGroupWithDrag",prefixCls:Z,animation:O},l.createElement(Sh,{itemWrapperStyle:{display:"inline-block"},direction:"horizontal",onIndexChange:function(le,se){var Ee=function(Se,ze,_e){Se=Se.slice();var Me=ze>_e,Re=Pr(Se.splice(ze,1),1),Xe=Re[0];return Se.splice(Me?_e:_e-1,0,Xe),Se};ce(Ee(te,se,le),"sort")}},De.concat(Fe))):l.createElement(tc,{prefixCls:Z,animation:O},J===Mh?l.createElement(Ih,{items:De,suffixItems:Fe,ellipsisNode:function(le){var se=le.ellipsisCount;return Oe(se)}}):De.concat(Fe)),Be&&l.createElement("div",{className:Z+"-suffix",onMouseDown:qn},Ne,I)));return he?l.createElement("div",rr({},Le,{className:K(Z+"-wrapper",(r={},r[Z+"-wrapper-rtl"]=c,r),Le.className),ref:ne}),dt&&l.createElement("div",{className:Z+"-addbefore"},j),we,rt&&l.createElement("div",{className:Z+"-addafter"},M)):we}var ed=l.forwardRef(Dh);ed.displayName="InputTag";const Lh=ed;function zh(e,n){var t={};return Object.keys(e).forEach(function(r){n.indexOf(r)!==-1&&(t[r]=e[r])}),t}var $h=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function gl(){var e=$h(d.useReducer(function(t){return t+1},0),2),n=e[1];return n}var Vh=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Hh=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function Wh(e){var n=d.useRef();return n.current=e,d.useCallback(function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var a=n.current;return a&&a.apply(void 0,Hh([],Vh(t),!1))},[n.current])}var bt=globalThis&&globalThis.__assign||function(){return bt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},bt.apply(this,arguments)},td=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},rc=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},xi="__arco_value_tag_placeholder",Tr={BEFORE:0,EDITING:1,NONE:2},Fh=l.forwardRef(function(e,n){var t,r,a=e.id,i=e.style,s=e.className,o=e.size,u=e.bordered,c=e.allowClear,f=e.allowCreate,v=e.status,m=e.loading,p=e.disabled,g=e.animation,h=e.prefixCls,b=e.suffixIcon,y=e.arrowIcon,C=e.removeIcon,w=e.clearIcon,E=e.placeholder,O=e.renderText,x=e.value,_=e.inputValue,T=e.popupVisible,P=e.maxTagCount,I=e.isMultiple,j=e.isEmptyValue,M=e.prefix,R=e.ariaControls,k=e.renderTag,L=e.dragToSort,S=e.rtl,N=e.htmlDataAttributes,A=e.onKeyDown,W=e.onChangeInputValue,D=e.onPaste,F=e.onClear,H=e.onFocus,z=e.onBlur,B=e.onRemoveCheckedItem,re=e.onSort,V=td(e,["id","style","className","size","bordered","allowClear","allowCreate","status","loading","disabled","animation","prefixCls","suffixIcon","arrowIcon","removeIcon","clearIcon","placeholder","renderText","value","inputValue","popupVisible","maxTagCount","isMultiple","isEmptyValue","prefix","ariaControls","renderTag","dragToSort","rtl","htmlDataAttributes","onKeyDown","onChangeInputValue","onPaste","onClear","onFocus","onBlur","onRemoveCheckedItem","onSort"]),$=d.useRef(null),X=d.useRef(null),Z=d.useContext(ye),Y=Z.size,J=Z.getPrefixCls,ae=rc(d.useState(Tr.NONE),2),ve=ae[0],ne=ae[1],G=rc(d.useState(!1),2),U=G[0],oe=G[1],ie=gl(),te="showSearch"in e?e.showSearch:I,pe=te||f,Te=o||Y,Q=U||T,me=xe(te)&&te.retainInputValue,ee=!I&&x!==void 0?O(x).text:"",de=function(he){he&&he.preventDefault()},ce=function(he){var Ie=pe?$.current:X.current;Ie&&(he==="focus"?Ie.focus():Ie.blur())},ge=function(he,Ie){pe&&Ie.target===X.current||(he==="focus"?(oe(!0),H&&H(Ie)):(oe(!1),z&&z(Ie)))},fe=function(he){if(!(pe&&he.currentTarget===X.current)){var Ie=he.keyCode||he.which;Ie===Lt.code&&he.preventDefault(),A&&A(he)}};d.useEffect(function(){ce(T?"focus":"blur"),pe&&ne(T?Tr.BEFORE:Tr.NONE)},[T]),d.useImperativeHandle(n,function(){return{dom:X.current,getRootDOMNode:function(){return X.current},focus:ce.bind(null,"focus"),blur:ce.bind(null,"blur"),getWidth:function(){return X.current&&X.current.clientWidth}}});var ue="arrowIcon"in e?y===null?null:l.createElement("div",{className:h+"-arrow-icon"},y):l.createElement("div",{className:h+"-arrow-icon"},l.createElement(Bn,null)),Pe=m?l.createElement("span",{className:h+"-loading-icon"},l.createElement(Yr,null)):b?l.createElement("span",{className:h+"-suffix-icon"},b):e.showSearch&&T?l.createElement("div",{className:h+"-search-icon"},l.createElement(Zi,null)):ue,Oe={paste:D,keyDown:fe,focus:function(he){he.stopPropagation(),ge("focus",he)},blur:function(he){he.stopPropagation(),ge("blur",he)},change:function(he,Ie){ne(Tr.EDITING),W&&W(he,Ie)}},Ce=function(){var he,Ie;switch(ve){case Tr.BEFORE:Ie=_||(me?ee:"");break;case Tr.EDITING:Ie=_||"";break;default:Ie=ee;break}var qe=!!(Q&&pe||j),Le={style:{width:"100%"},value:qe&&typeof Ie!="object"?Ie:"",placeholder:pe&&ee&&typeof ee!="object"?ee:E};return pe?(Le.onPaste=Oe.paste,Le.onKeyDown=Oe.keyDown,Le.onFocus=Oe.focus,Le.onBlur=Oe.blur,Le.onChange=Oe.change):(Le.tabIndex=-1,Le.style.pointerEvents="none"),l.createElement("span",{className:h+"-view-selector"},l.createElement(dl,bt({"aria-hidden":!qe||void 0,ref:$,disabled:p,className:K(h+"-view-input",(he={},he[h+"-hidden"]=!qe,he)),autoComplete:"off"},Le)),qe?l.createElement("span",{className:h+"-view-value-mirror"},Ln(Le.value?Ie:Le.placeholder)):null,l.createElement("span",{style:qe?{display:"none"}:{},className:h+"-view-value"},Ln(j?Le.placeholder:Ie)))},Ne=Wh(O),Ae=xe(P)?P.count:P,De=function(he){var Ie=Ne(he);return{value:he,label:Ie.text,closable:!Ie.disabled}},Fe=d.useMemo(function(){if(!I)return[];var he=ft(x)?[]:[].concat(x),Ie=Ye(Ae),qe=(Ie?he.slice(0,Ae):he).map(function(Le){return De(Le)});return qe},[x,I,P,Ne]),tt=function(){var he=xe(P)?P.count:P,Ie=xe(P)&&P.showPopover,qe=J("input-tag"),Le=function(_e){var Me=xe(P)&&Ke(P.render)?P.render(_e):he==="responsive"?l.createElement(eo,{key:xi,className:qe+"-tag"},"+",_e,"..."):"+"+_e+"...";return Me},we=ft(x)?[]:[].concat(x),le=function(){for(var _e=we.length-1;_e>=0;_e--){var Me=we[_e],Re=Ne(Me);if(!Re.disabled)return _e}return-1},se={onPaste:Oe.paste,onKeyDown:Oe.keyDown,onFocus:Oe.focus,onBlur:Oe.blur,onInputChange:Oe.change,onRemove:function(_e,Me,Re){var Xe=Re.keyCode||Re.which,Je=-1;Xe===Rf.code&&(Je=le())!==-1&&(_e=we[Je],Me=Je),P&&ie(),B&&B(_e,Me,Re)}},Ee={suffix:null,prefix:null,addBefore:null,addAfter:null,allowClear:!1,labelInValue:!1},Se=function(){if(Ye(he)){var _e=we.length-he;if(_e>0){var Me={label:Le(_e),closable:!1,value:xi};if(Ie){var Re=we.map(function(Xe,Je){return{val:Xe,index:Je}}).slice(he).map(function(Xe){var Je=De(Xe.val);return k?k(bt(bt({},Je),{onClose:function(){}}),Xe.index,Fe.concat(Me)):l.createElement(eo,bt({},Je,{key:Je.value,className:qe+"-tag",onClose:function(vt){return se.onRemove(Je.value,Xe.index,vt)}}),Je.label)});Me.label=l.createElement(ml,{content:!!Re.length&&l.createElement(l.Fragment,null,Re)},Me.label)}return Me}}}(),ze=Se?Fe.concat(Se):Fe;return l.createElement(Lh,bt({},Ee,{className:Q?J("input-tag")+"-focus":"",ref:$,disabled:p,dragToSort:L,disableInput:!te,animation:g,placeholder:E,value:ze,inputValue:_,size:Te,tagClassName:h+"-tag",renderTag:k,icon:{removeIcon:C},maxTagCount:he==="responsive"?{count:he,render:Le,popoverProps:Ie?{}:{disabled:!0}}:void 0,onChange:function(_e,Me){if(re&&Me==="sort"){var Re=_e.indexOf(xi);if(Re>-1){var Xe=_e.slice(0,Re),Je=_e.slice(Re+1),vt=we.slice(_e.length-1-we.length);re(Xe.concat(vt,Je))}else re(_e)}}},se))},Be=v||(e.error?"error":void 0),dt=!p&&!j&&c?l.createElement(Nt,{size:Te,key:"clearIcon",className:h+"-clear-icon",onClick:F,onMouseDown:de},w??l.createElement(Dt,null)):null,rt=K(h,h+"-"+(I?"multiple":"single"),(t={},t[h+"-show-search"]=te,t[h+"-open"]=T,t[h+"-size-"+Te]=Te,t[h+"-focused"]=Q,t[h+"-"+Be]=Be,t[h+"-disabled"]=p,t[h+"-no-border"]=!u,t[h+"-rtl"]=S,t),s);return l.createElement("div",bt({role:"combobox","aria-haspopup":"listbox","aria-autocomplete":"list","aria-expanded":T,"aria-disabled":p,"aria-controls":R},zh(V,["onClick","onMouseEnter","onMouseLeave"]),N,{ref:X,tabIndex:p?-1:0,id:a,style:i,className:rt,onKeyDown:fe,onFocus:function(he){!p&&!L&&(pe?$.current&&$.current.focus():ge("focus",he))},onBlur:function(he){return ge("blur",he)}}),l.createElement("div",{title:typeof ee=="string"?ee:void 0,className:K(h+"-view",(r={},r[h+"-view-with-prefix"]=M,r)),onClick:function(he){return T&&pe&&he.stopPropagation()}},M&&l.createElement("div",{"aria-hidden":"true",className:K(h+"-prefix"),onMouseDown:function(he){return U&&de(he)}},M),I?tt():Ce(),l.createElement("div",{"aria-hidden":"true",className:h+"-suffix",onMouseDown:function(he){return U&&de(he)}},dt,Pe)))}),Bh=function(e,n){var t,r=e.prefixCls,a=e.id,i=e.style,s=e.className,o=e.addBefore,u=e.rtl,c=e.renderView,f=e.autoWidth,v=td(e,["prefixCls","id","style","className","addBefore","rtl","renderView","autoWidth"]),m=f?bt({minWidth:0,maxWidth:"100%"},xe(f)?f:{}):null,p=d.useRef(null),g=o!=null,h=!1,b=g||h,y={id:a,style:bt(bt(bt({},m),{width:m?"auto":void 0}),i),className:s},C=ir(v);d.useImperativeHandle(n,function(){return p.current});var w=l.createElement(Fh,bt({},e,{ref:p,id:b?void 0:y.id,style:b?void 0:y.style,className:b?void 0:y.className,htmlDataAttributes:b?{}:C}));return typeof c=="function"&&(w=c(w)),b?l.createElement("div",bt({},C,y,{className:K(r+"-wrapper",(t={},t[r+"-wrapper-rtl"]=u,t),y.className)}),g&&l.createElement("div",{className:r+"-addbefore"},o),w):w},rd=l.forwardRef(Bh);rd.displayName="SelectView";const Kh=rd;function Gh(e){for(var n=0,t=0;t<e.length;t++)e.charCodeAt(t)>127||e.charCodeAt(t)===94?n+=2:n++;return n}var Uh=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Zh="__rc_ghost_item__";function nd(e,n){var t=Math.floor(e*n),r=t/n,a=(e-r)/(1/n);return{index:t,offsetPtg:Number.isNaN(a)?0:a}}function nc(e,n){n===void 0&&(n=!1);var t=Ha(e),r=0;if(n){var a=window.getComputedStyle(e),i=a.marginTop,s=a.marginBottom;r=Number(i.replace(/\D/g,""))+Number(s.replace(/\D/g,"")),r=isNaN(r)?0:r}return t?t.offsetHeight+r:0}function ro(e,n){return e<0?0:e>n?n:e}function Sr(e){var n=e.scrollTop,t=e.scrollHeight,r=e.clientHeight,a=t-r;return a<=0?0:ro(n,a)/a}function ac(e,n,t){var r=nd(e,n),a=r.index,i=r.offsetPtg,s=Math.ceil(e*t),o=Math.ceil((1-e)*t);return{itemIndex:a,itemOffsetPtg:i,startIndex:Math.max(0,a-s),endIndex:Math.min(n-1,a+o)}}function oa(e){var n=e.itemHeight,t=e.itemOffsetPtg,r=e.scrollPtg,a=e.clientHeight;return Math.floor(a*r-n*t)}function Xh(e){var n=e.scrollTop,t=Uh(e,["scrollTop"]);return n+oa(t)}function Yh(e){var n=e.locatedItemRelativeTop,t=e.locatedItemIndex,r=e.compareItemIndex,a=e.startIndex,i=e.endIndex,s=e.getItemKey,o=e.itemElementHeights,u=e.itemHeight,c=n,f=s(r);if(r<=t)for(var v=t;v>=a;v-=1){var m=s(v);if(m===f)break;var p=s(v-1);c-=o[p]||u}else for(var v=t;v<=i;v+=1){var m=s(v);if(m===f)break;c+=o[m]||u}return c}function qh(e){var n=-1,t=0;return e.forEach(function(r,a){var i;if(r=typeof r=="string"?r:(i=r.props)===null||i===void 0?void 0:i.children,typeof r=="string"){var s=Gh(r);s>t&&(t=s,n=a)}}),n}function Jh(e,n,t,r){var a=t-e,i=n-t,s=Math.min(a,i)*2;if(r<=s){var o=Math.floor(r/2);return r%2?t+o+1:t-o}return a>i?t-(r-i):t+(r-a)}function Qh(e,n,t){var r=e.length,a=n.length,i,s;if(r===0&&a===0)return null;r<a?(i=e,s=n):(i=n,s=e);var o={__EMPTY_ITEM__:!0};function u(g,h){return g!==void 0?t(g,h):o}for(var c=null,f=Math.abs(r-a)!==1,v=0;v<s.length;v+=1){var m=u(i[v],v),p=u(s[v],v);if(m!==p){c=v,f=f||m!==u(s[v+1],v+1);break}}return c===null?null:{index:c,multiple:f}}var Wt=globalThis&&globalThis.__assign||function(){return Wt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Wt.apply(this,arguments)},e1=function(e){var n=e.height,t=e.offset,r=e.children,a=e.outerStyle,i=e.innerStyle,s={},o={display:"flex",flexDirection:"column"};return t!==void 0?(s=Wt({height:n,position:"relative",overflow:"hidden",zIndex:0},a),o=Wt(Wt(Wt({},o),{transform:"translateY("+t+"px)",position:"absolute",left:0,right:0,top:0}),i)):(s=Wt({},a),o=Wt(Wt({},o),i)),d.createElement("div",{style:s},d.createElement("div",{style:o},r))};const ic=e1;var mt=globalThis&&globalThis.__assign||function(){return mt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},mt.apply(this,arguments)},t1=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},no=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},r1=32,Ar="__virtual_item_height_"+Math.random().toFixed(5).slice(2),n1=function(e){var n=gl(),t=e.current;d.useEffect(function(){Object.keys(t).length&&!t[Ar]&&(t[Ar]=Object.entries(t).reduce(function(r,a,i,s){var o=no(a,2),u=o[1],c=r+u;return i===s.length-1?Math.round(c/s.length):c},0),n())},[Object.keys(t).length])},a1=function(e){var n=d.useRef({}),t=d.useRef(e);return d.useEffect(function(){t.current=e},[e]),e!==t.current&&(n.current={}),function(r,a,i){return n.current.hasOwnProperty(a)||(n.current[a]=e(r,a,i)),n.current[a]}},ad=l.forwardRef(function(e,n){var t=e.style,r=e.className,a=e.children,i=e.data,s=i===void 0?[]:i,o=e.itemKey,u=e.threshold,c=u===void 0?100:u,f=e.wrapper,v=f===void 0?"div":f,m=e.height,p=m===void 0?"100%":m,g=e.isStaticItemHeight,h=g===void 0?!0:g,b=e.itemHeight,y=e.measureLongestItem,C=e.scrollOptions,w=e.onScroll,E=e.needFiller,O=E===void 0?!0:E,x=e.outerStyle,_=e.innerStyle,T=e.wrapperChild,P=T===void 0?l.Fragment:T,I=t1(e,["style","className","children","data","itemKey","threshold","wrapper","height","isStaticItemHeight","itemHeight","measureLongestItem","scrollOptions","onScroll","needFiller","outerStyle","innerStyle","wrapperChild"]),j=t&&t.maxHeight||p,M=d.useRef({}),R=no(d.useState(200),2),k=R[0],L=R[1],S=a1(a);n1(M);var N=s.length,A=b||M.current[Ar]||r1,W=Ye(j)?j:k,D=Math.ceil(W/A),F=A*N,H=c!==null&&N>=c&&F>W,z=d.useRef(null),B=d.useRef(null),re=d.useRef(!1),V=d.useRef(H),$=d.useMemo(function(){if(z.current){var Q=function(me){return+window.getComputedStyle(z.current)[me].replace(/\D/g,"")};return{top:Q("paddingTop"),bottom:Q("paddingBottom")}}return{top:0,bottom:0}},[z.current]),X=no(If({status:"NONE",startIndex:0,endIndex:0,itemIndex:0,itemOffsetPtg:0,startItemTop:0,scrollTop:0}),2),Z=X[0],Y=X[1],J=cl(s)||[],ae=ll(),ve=function(Q,me){return typeof o=="function"?o(Q,me):typeof o=="string"?Q[o]:Q.key||me},ne=function(Q,me){if(me===void 0&&(me=s),Q===me.length)return Zh;var ee=me[Q];return ee!==void 0?ve(ee,Q):null},G=function(Q){return M.current[Q]||A},U=function(Q){for(var me=Q.itemIndex,ee=Q.relativeTop,de=z.current,ce=de.scrollHeight,ge=de.clientHeight,fe=Z.scrollTop,ue=ce-ge,Pe=Number.MAX_VALUE,Oe=null,Ce=null,Ne=null,Ae=null,De=null,Fe=0,tt=0;tt<ue;tt++){var Be=Jh(0,ue,fe,tt),dt=Sr({scrollTop:Be,scrollHeight:ce,clientHeight:ge}),rt=ac(dt,N,D),he=rt.itemIndex,Ie=rt.itemOffsetPtg,qe=rt.startIndex,Le=rt.endIndex;if(qe<=me&&me<=Le){var we=oa({itemHeight:G(ne(he)),itemOffsetPtg:Ie,clientHeight:ge,scrollPtg:dt}),le=Yh({locatedItemRelativeTop:we,locatedItemIndex:he,compareItemIndex:me,startIndex:qe,endIndex:Le,itemHeight:A,getItemKey:ne,itemElementHeights:M.current}),se=Math.abs(le-ee);se<Pe?(Pe=se,Oe=Be,Ce=he,Ne=Ie,Ae=qe,De=Le,Fe=0):Fe+=1}if(Fe>10)break}Oe!==null&&(re.current=!0,z.current.scrollTop=Oe,Y(mt(mt({},Z),{status:"MEASURE_START",scrollTop:Oe,itemIndex:Ce,itemOffsetPtg:Ne,startIndex:Ae,endIndex:De}))),B.current=Ct(function(){re.current=!1})},oe=function(Q){var me=z.current,ee=me.scrollTop,de=me.clientHeight,ce=me.scrollHeight,ge=ro(ee,ce-de),fe=Sr({scrollTop:ge,clientHeight:de,scrollHeight:ce}),ue=nd(fe,N),Pe=ue.index,Oe=ue.offsetPtg;Y(mt(mt({},Z),{scrollTop:ge,itemIndex:Pe,itemOffsetPtg:Oe})),Q&&(w==null||w(Q,{index:Pe}))},ie=function(Q,me){me===void 0&&(me=!1);var ee=F,de=z.current,ce=de.scrollTop,ge=de.clientHeight,fe=ro(ce,ee-ge);if(!(!me&&(fe===Z.scrollTop||re.current))){var ue=Sr({scrollTop:fe,clientHeight:ge,scrollHeight:ee}),Pe=ac(ue,N,D),Oe=Pe.itemIndex,Ce=Pe.itemOffsetPtg,Ne=Pe.startIndex,Ae=Pe.endIndex;Y(mt(mt({},Z),{scrollTop:fe,itemIndex:Oe,itemOffsetPtg:Ce,startIndex:Ne,endIndex:Ae,status:"MEASURE_START"})),Q&&(w==null||w(Q,{index:Oe}))}};d.useEffect(function(){return function(){B.current&&ht(B.current)}},[]),d.useEffect(function(){z.current&&(ae&&(z.current.scrollTop=0),ie(null,!0))},[D]),d.useEffect(function(){if(z.current){var Q=null,me=V.current!==H?H?"virtual":"raw":"";if(V.current=H,W&&J.length!==s.length){var ee=Qh(J,s,ve);Q=ee?ee.index:null}if(me||H&&Q){var de=z.current.clientHeight,ce=oa({itemHeight:G(ne(Z.itemIndex,J)),itemOffsetPtg:Z.itemOffsetPtg,scrollPtg:Sr({scrollTop:Z.scrollTop,scrollHeight:J.length*A,clientHeight:de}),clientHeight:de});if(me==="raw"){for(var ge=ce,fe=0;fe<Z.itemIndex;fe++)ge-=G(ne(fe));z.current.scrollTop=-ge,re.current=!0,B.current=Ct(function(){re.current=!1})}else U({itemIndex:Z.itemIndex,relativeTop:ce})}}},[s,H]),Fr(function(){if(Z.status==="MEASURE_START"&&z.current){for(var Q=z.current,me=Q.scrollTop,ee=Q.scrollHeight,de=Q.clientHeight,ce=Sr({scrollTop:me,scrollHeight:ee,clientHeight:de}),ge=Xh({scrollPtg:ce,clientHeight:de,scrollTop:me-($.top+$.bottom)*ce,itemHeight:G(ne(Z.itemIndex)),itemOffsetPtg:Z.itemOffsetPtg}),fe=Z.itemIndex-1;fe>=Z.startIndex;fe--)ge-=G(ne(fe));Y(mt(mt({},Z),{startItemTop:ge,status:"MEASURE_DONE"}))}},[Z]),d.useImperativeHandle(n,function(){return{dom:z.current,getRootDOMNode:function(){return z.current},scrollTo:function(Q){B.current&&ht(B.current),B.current=Ct(function(){var me;if(z.current){if(typeof Q=="number"){z.current.scrollTop=Q;return}var ee="index"in Q?Q.index:"key"in Q?s.findIndex(function(Be,dt){return ve(Be,dt)===Q.key}):0,de=s[ee];if(de){var ce=typeof Q=="object"&&(!((me=Q.options)===null||me===void 0)&&me.block)?Q.options.block:(C==null?void 0:C.block)||"nearest",ge=z.current,fe=ge.clientHeight,ue=ge.scrollTop;if(H&&!h){if(ce==="nearest"){var Pe=Z.itemIndex,Oe=Z.itemOffsetPtg;if(Math.abs(Pe-ee)<D){var Ce=oa({itemHeight:G(ne(Pe)),itemOffsetPtg:Oe,clientHeight:fe,scrollPtg:Sr(z.current)});if(ee<Pe)for(var Ne=ee;Ne<Pe;Ne++)Ce-=G(ne(Ne));else for(var Ne=Pe;Ne<ee;Ne++)Ce+=G(ne(Ne));if(Ce<0||Ce>fe)ce=Ce<0?"start":"end";else return}else ce=ee<Pe?"start":"end"}Y(mt(mt({},Z),{startIndex:Math.max(0,ee-D),endIndex:Math.min(N-1,ee+D)})).then(function(){var Be=G(ve(de,ee));U({itemIndex:ee,relativeTop:ce==="start"?0:(fe-Be)/(ce==="center"?2:1)})})}else{for(var Ae=G(ne(ee)),Ce=0,Ne=0;Ne<ee;Ne++)Ce+=G(ne(Ne));var De=Ce+Ae,Fe=Ce+Ae/2;if(Fe>ue&&Fe<fe+ue)return;ce==="nearest"&&(Ce<ue?ce="start":De>ue+fe&&(ce="end"));var tt=fe-Ae;z.current.scrollTop=Ce-(ce==="start"?0:tt/(ce==="center"?2:1))}}}})}}},[s,A,Z]);var te=function(Q,me){return Q.map(function(ee,de){var ce=me+de,ge=S(ee,ce,{style:{},itemIndex:de}),fe=ve(ee,ce);return l.cloneElement(ge,{key:fe,ref:function(ue){var Pe,Oe,Ce=M.current;ue&&Z.status==="MEASURE_START"&&(!h||Ce[fe]===void 0)&&(h?(Ce[Ar]||(Ce[Ar]=nc(ue,!0)),Ce[fe]=Ce[Ar]):Ce[fe]=nc(ue,!0)),Ke((Pe=ge)===null||Pe===void 0?void 0:Pe.ref)&&((Oe=ge)===null||Oe===void 0||Oe.ref(ue))}})})},pe=d.useRef(null);d.useEffect(function(){pe.current=null},[s]);var Te=function(){if(y){var Q=pe.current===null?qh(s):pe.current,me=s[Q];return pe.current=Q,me?l.createElement("div",{style:{height:1,overflow:"hidden",opacity:0}},S(me,Q,{style:{}})):null}return null};return l.createElement(pt,{onResize:function(){if(z.current&&!Ye(j)){var Q=z.current.clientHeight;L(Q)}},getTargetDOMNode:function(){return z.current}},l.createElement(v,mt({ref:z,style:mt(mt({overflowY:"auto",overflowAnchor:"none"},t),{maxHeight:j}),className:r,onScroll:H?ie:oe},I),H?l.createElement(l.Fragment,null,l.createElement(ic,{height:F,outerStyle:x,innerStyle:_,offset:Z.status==="MEASURE_DONE"?Z.startItemTop:0},l.createElement(P,null,te(s.slice(Z.startIndex,Z.endIndex+1),Z.startIndex))),Te()):O?l.createElement(ic,{height:W,outerStyle:x,innerStyle:_},l.createElement(P,null,te(s,0))):l.createElement(P,null,te(s,0))))});ad.displayName="VirtualList";const id=ad;var i1=Array.isArray,Qr=i1,o1=Qr,l1=Ba,s1=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,c1=/^\w*$/;function u1(e,n){if(o1(e))return!1;var t=typeof e;return t=="number"||t=="symbol"||t=="boolean"||e==null||l1(e)?!0:c1.test(e)||!s1.test(e)||n!=null&&e in Object(n)}var f1=u1,d1=Wn,v1=Hn,p1="[object AsyncFunction]",m1="[object Function]",g1="[object GeneratorFunction]",h1="[object Proxy]";function y1(e){if(!v1(e))return!1;var n=d1(e);return n==m1||n==g1||n==p1||n==h1}var od=y1,b1=zt,x1=b1["__core-js_shared__"],C1=x1,Ci=C1,oc=function(){var e=/[^.]+$/.exec(Ci&&Ci.keys&&Ci.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function E1(e){return!!oc&&oc in e}var w1=E1,O1=Function.prototype,_1=O1.toString;function P1(e){if(e!=null){try{return _1.call(e)}catch{}try{return e+""}catch{}}return""}var ld=P1,T1=od,S1=w1,N1=Hn,I1=ld,j1=/[\\^$.*+?()[\]{}|]/g,M1=/^\[object .+?Constructor\]$/,R1=Function.prototype,k1=Object.prototype,A1=R1.toString,D1=k1.hasOwnProperty,L1=RegExp("^"+A1.call(D1).replace(j1,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function z1(e){if(!N1(e)||S1(e))return!1;var n=T1(e)?L1:M1;return n.test(I1(e))}var $1=z1;function V1(e,n){return e==null?void 0:e[n]}var H1=V1,W1=$1,F1=H1;function B1(e,n){var t=F1(e,n);return W1(t)?t:void 0}var en=B1,K1=en,G1=K1(Object,"create"),Ya=G1,lc=Ya;function U1(){this.__data__=lc?lc(null):{},this.size=0}var Z1=U1;function X1(e){var n=this.has(e)&&delete this.__data__[e];return this.size-=n?1:0,n}var Y1=X1,q1=Ya,J1="__lodash_hash_undefined__",Q1=Object.prototype,e0=Q1.hasOwnProperty;function t0(e){var n=this.__data__;if(q1){var t=n[e];return t===J1?void 0:t}return e0.call(n,e)?n[e]:void 0}var r0=t0,n0=Ya,a0=Object.prototype,i0=a0.hasOwnProperty;function o0(e){var n=this.__data__;return n0?n[e]!==void 0:i0.call(n,e)}var l0=o0,s0=Ya,c0="__lodash_hash_undefined__";function u0(e,n){var t=this.__data__;return this.size+=this.has(e)?0:1,t[e]=s0&&n===void 0?c0:n,this}var f0=u0,d0=Z1,v0=Y1,p0=r0,m0=l0,g0=f0;function tn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}tn.prototype.clear=d0;tn.prototype.delete=v0;tn.prototype.get=p0;tn.prototype.has=m0;tn.prototype.set=g0;var h0=tn;function y0(){this.__data__=[],this.size=0}var b0=y0;function x0(e,n){return e===n||e!==e&&n!==n}var sd=x0,C0=sd;function E0(e,n){for(var t=e.length;t--;)if(C0(e[t][0],n))return t;return-1}var qa=E0,w0=qa,O0=Array.prototype,_0=O0.splice;function P0(e){var n=this.__data__,t=w0(n,e);if(t<0)return!1;var r=n.length-1;return t==r?n.pop():_0.call(n,t,1),--this.size,!0}var T0=P0,S0=qa;function N0(e){var n=this.__data__,t=S0(n,e);return t<0?void 0:n[t][1]}var I0=N0,j0=qa;function M0(e){return j0(this.__data__,e)>-1}var R0=M0,k0=qa;function A0(e,n){var t=this.__data__,r=k0(t,e);return r<0?(++this.size,t.push([e,n])):t[r][1]=n,this}var D0=A0,L0=b0,z0=T0,$0=I0,V0=R0,H0=D0;function rn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}rn.prototype.clear=L0;rn.prototype.delete=z0;rn.prototype.get=$0;rn.prototype.has=V0;rn.prototype.set=H0;var Ja=rn,W0=en,F0=zt,B0=W0(F0,"Map"),hl=B0,sc=h0,K0=Ja,G0=hl;function U0(){this.size=0,this.__data__={hash:new sc,map:new(G0||K0),string:new sc}}var Z0=U0;function X0(e){var n=typeof e;return n=="string"||n=="number"||n=="symbol"||n=="boolean"?e!=="__proto__":e===null}var Y0=X0,q0=Y0;function J0(e,n){var t=e.__data__;return q0(n)?t[typeof n=="string"?"string":"hash"]:t.map}var Qa=J0,Q0=Qa;function ey(e){var n=Q0(this,e).delete(e);return this.size-=n?1:0,n}var ty=ey,ry=Qa;function ny(e){return ry(this,e).get(e)}var ay=ny,iy=Qa;function oy(e){return iy(this,e).has(e)}var ly=oy,sy=Qa;function cy(e,n){var t=sy(this,e),r=t.size;return t.set(e,n),this.size+=t.size==r?0:1,this}var uy=cy,fy=Z0,dy=ty,vy=ay,py=ly,my=uy;function nn(e){var n=-1,t=e==null?0:e.length;for(this.clear();++n<t;){var r=e[n];this.set(r[0],r[1])}}nn.prototype.clear=fy;nn.prototype.delete=dy;nn.prototype.get=vy;nn.prototype.has=py;nn.prototype.set=my;var yl=nn,cd=yl,gy="Expected a function";function bl(e,n){if(typeof e!="function"||n!=null&&typeof n!="function")throw new TypeError(gy);var t=function(){var r=arguments,a=n?n.apply(this,r):r[0],i=t.cache;if(i.has(a))return i.get(a);var s=e.apply(this,r);return t.cache=i.set(a,s)||i,s};return t.cache=new(bl.Cache||cd),t}bl.Cache=cd;var hy=bl,yy=hy,by=500;function xy(e){var n=yy(e,function(r){return t.size===by&&t.clear(),r}),t=n.cache;return n}var Cy=xy,Ey=Cy,wy=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Oy=/\\(\\)?/g,_y=Ey(function(e){var n=[];return e.charCodeAt(0)===46&&n.push(""),e.replace(wy,function(t,r,a,i){n.push(a?i.replace(Oy,"$1"):r||t)}),n}),Py=_y;function Ty(e,n){for(var t=-1,r=e==null?0:e.length,a=Array(r);++t<r;)a[t]=n(e[t],t,e);return a}var Sy=Ty,cc=Fa,Ny=Sy,Iy=Qr,jy=Ba,My=1/0,uc=cc?cc.prototype:void 0,fc=uc?uc.toString:void 0;function ud(e){if(typeof e=="string")return e;if(Iy(e))return Ny(e,ud)+"";if(jy(e))return fc?fc.call(e):"";var n=e+"";return n=="0"&&1/e==-My?"-0":n}var Ry=ud,ky=Ry;function Ay(e){return e==null?"":ky(e)}var fd=Ay,Dy=Qr,Ly=f1,zy=Py,$y=fd;function Vy(e,n){return Dy(e)?e:Ly(e,n)?[e]:zy($y(e))}var Hy=Vy,Wy=Ba,Fy=1/0;function By(e){if(typeof e=="string"||Wy(e))return e;var n=e+"";return n=="0"&&1/e==-Fy?"-0":n}var Ky=By,Gy=Hy,Uy=Ky;function Zy(e,n){n=Gy(n,e);for(var t=0,r=n.length;e!=null&&t<r;)e=e[Uy(n[t++])];return t&&t==r?e:void 0}var Xy=Zy,Yy=Xy;function qy(e,n,t){var r=e==null?void 0:Yy(e,n);return r===void 0?t:r}var Jy=qy;const Rt=Vn(Jy);var Qy=fd,dd=/[\\^$.*+?()[\]{}|]/g,eb=RegExp(dd.source);function tb(e){return e=Qy(e),e&&eb.test(e)?e.replace(dd,"\\$&"):e}var rb=tb;const nb=Vn(rb);function ab(e){var n=e.nodeList,t=e.pattern,r=e.highlightClassName;if(!t)return n;var a=function(i){return i&&i.props&&typeof i.props.children=="string"?d.cloneElement(i,void 0,l.createElement(ib,{text:i.props.children,keyword:t,highlightClassName:r})):i};return ct(n)?n.map(function(i){return a(i)}):a(n)}function ib(e){var n=e.text,t=e.keyword,r=e.highlightClassName;if(!t)return l.createElement(l.Fragment,null,n);t.length>1e3&&(t=t.slice(0,1e3));var a=new RegExp("("+nb(t)+")","i"),i=n.split(a);return l.createElement(l.Fragment,null,i.map(function(s,o){return a.test(s)?l.createElement("span",{key:o,className:r},s):l.createElement("span",{key:o},s)}))}var Ca=globalThis&&globalThis.__assign||function(){return Ca=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ca.apply(this,arguments)};function ob(e){e&&e.preventDefault()}function la(e,n){return n?!ct(e)||!e.length:e===void 0}function dc(e,n,t){return t&&(n?e=Array.isArray(e)?e.map(function(r){return xe(r)&&"label"in r?r.value:r}):e:e=xe(e)&&"label"in e?e.value:e),la(e,n)?n?Array.isArray(e)?e:[]:void 0:e}function vd(e){return Rt(e,"props.isSelectOption")||Rt(e,"type.__ARCO_SELECT_OPTION__")}function pd(e){return Rt(e,"props.isSelectOptGroup")||Rt(e,"type.__ARCO_SELECT_OPTGROUP__")}function md(e,n,t){var r=e.children,a=e.options,i=e.filterOption,s=n.inputValue,o=s===void 0?"":s,u=n.userCreatedOptions,c=n.userCreatingOption,f=n.prefixCls,v=t===void 0?{}:t,m=v.optionInfoMap,p=m===void 0?new Map:m,g=v.optionValueList,h=g===void 0?[]:g,b=v.customNodeCount,y=b===void 0?0:b,C=!1,w=!1,E=[],O=[],x=function(I){var j=Rt(I,"props.value"),M=Rt(I,"props.children");return j===void 0&&M!==null&&M!==void 0?M.toString():j},_=function(I,j,M){var R=I.label,k=I.value;return!R&&!k&&!j?(y++,"custom_node_"+y):M?j||"group_"+R:j||typeof k+"_"+k||R+"_"+p.size},T=function(I,j){var M=x(I),R=!0;i===!0?R=M!==void 0&&String(M).toLowerCase().indexOf(o.toLowerCase())!==-1:typeof i=="function"&&(R=!o||i(o,I));var k=p.get(M),L=(k==null?void 0:k._origin)==="userCreatedOptions"||(k==null?void 0:k._origin)==="userCreatingOption";if(!k||L){"_key"in I.props||(I=l.cloneElement(I,{_key:_(I.props,I.key)}));var S=p.size,N=Ca(Ca({child:I},I.props),{value:M,_index:S,_origin:j,_valid:R});if(p.set(M,N),L){var A=E.findIndex(function(W){var D;return((D=W==null?void 0:W.props)===null||D===void 0?void 0:D.value)===M});A>-1&&(R?E[A]=I:E.splice(A,1))}else h.push(M),R&&(E.push(I),N.disabled||O.push(S))}typeof I.props.children!="string"&&(w=!0)},P=function(I,j){j&&ct(I)&&I.length&&I.forEach(function(M){(ar(M)||Ye(M))&&(M={label:M,value:M});var R=l.createElement(Xf,{_key:_(M),value:M.value,disabled:M.disabled===!0,extra:M.extra},Ln(M.label));T(R,j)})};return c&&P([c],"userCreatingOption"),r&&l.Children.map(r,function(I){if(pd(I)){var j=I.props,M=j.children,R=j.options,k=md({children:M,options:R,filterOption:i},{inputValue:o,prefixCls:f},{optionInfoMap:p,optionValueList:h,customNodeCount:y}),L=k.childrenList,S=k.optionIndexListForArrowKey,N=k.hasComplexLabelInOptions;L.length&&(E.push(l.cloneElement(I,{children:null,_key:_(I.props,I.key,!0)})),E=E.concat(L),O=O.concat(S),C=!0,w=w||N)}else vd(I)?T(I,"children"):xe(I)&&I.props&&E.push(l.cloneElement(I,{_key:_(I.props,I.key)}))}),P(a,"options"),P(u,"userCreatedOptions"),{childrenList:ab({nodeList:E,pattern:o,highlightClassName:f+"-highlight"}),optionInfoMap:p,optionValueList:h,optionIndexListForArrowKey:O,hasOptGroup:C,hasComplexLabelInOptions:w}}var lb=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},yn={};function an(e){var n=lb(d.useState(),2),t=n[0],r=n[1];return d.useEffect(function(){yn[e]=e in yn?yn[e]:0,r(yn[e]),yn[e]+=1},[]),typeof t=="number"?""+e+t:void 0}var mr=globalThis&&globalThis.__assign||function(){return mr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},mr.apply(this,arguments)},lr=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},sb=globalThis&&globalThis.__values||function(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")},cb=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},vc="userCreatingOption",ub={trigger:"click",bordered:!0,filterOption:!0,unmountOnExit:!0,defaultActiveFirstOption:!0},fb={bottom:4};function db(e,n){var t=d.useContext(ye),r=t.getPrefixCls,a=t.renderEmpty,i=t.componentConfig,s=t.rtl,o=He(e,ub,i==null?void 0:i.Select),u=o.children,c=o.renderFormat,f=o.defaultActiveFirstOption,v=o.disabled,m=o.unmountOnExit,p=o.notFoundContent,g=o.showSearch,h=o.tokenSeparators,b=o.options,y=o.filterOption,C=o.labelInValue,w=o.getPopupContainer,E=o.trigger,O=o.triggerElement,x=o.triggerProps,_=o.dropdownRender,T=o.dropdownMenuStyle,P=o.dropdownMenuClassName,I=o.virtualListProps,j=o.onChange,M=o.onSelect,R=o.onDeselect,k=o.onClear,L=o.onSearch,S=o.onFocus,N=o.onBlur,A=o.onPopupScroll,W=o.onVisibleChange,D=o.onInputValueChange,F=o.onPaste,H=o.onKeyDown,z=o.mode,B=o.allowCreate;z==="tags"&&(z="multiple",B=!0);var re=r("select"),V=z==="multiple",$=lr(d.useState(dc(o.defaultValue,V,C)),2),X=$[0],Z=$[1],Y="value"in o?dc(o.value,V,C):X,J=lr(ot("",{value:"inputValue"in o?o.inputValue||"":void 0}),3),ae=J[0],ve=J[1],ne=J[2],G=lr(ot(!1,{defaultValue:o.defaultPopupVisible,value:"popupVisible"in o?o.popupVisible:x&&"popupVisible"in x?x.popupVisible:void 0}),2),U=G[0],oe=G[1],ie=lr(d.useState(null),2),te=ie[0],pe=ie[1],Te=lr(d.useState([]),2),Q=Te[0],me=Te[1],ee=lr(d.useState(ct(Y)?Y[0]:Y),2),de=ee[0],ce=ee[1],ge=d.useMemo(function(){return md({children:u,options:b,filterOption:y},{prefixCls:re,inputValue:ae,userCreatedOptions:Q,userCreatingOption:te})},[u,b,y,ae,te,Q]),fe=ge.childrenList,ue=ge.optionInfoMap,Pe=ge.optionValueList,Oe=ge.optionIndexListForArrowKey,Ce=ge.hasOptGroup,Ne=ge.hasComplexLabelInOptions,Ae=d.useRef(null),De=d.useRef(null),Fe=d.useRef(null),tt=d.useRef([]),Be=d.useRef(null),dt=d.useRef(ae),rt=d.useRef(null),he=d.useRef(0),Ie=d.useRef(!1),qe=an(re+"-popup-"),Le=la(Y,V),we=d.useMemo(function(){var q,be;if(f){var je=ct(Y)?Y[0]:Y,ke=!Le&&((q=ue.get(je))===null||q===void 0?void 0:q._valid),Qe=((be=ue==null?void 0:ue.get(Pe[0]))===null||be===void 0?void 0:be._origin)===vc;return ke&&!Qe?je:Pe[Oe[0]]}},[Y,ue,Pe,Oe,f,Le]),le=d.useCallback(function(q,be){var je,ke=ue.get(q);Ae.current&&(!((je=ke==null?void 0:ke.child)===null||je===void 0)&&je.props)&&Ae.current.scrollTo({key:ke.child.props._key,options:be})},[ue]),se=d.useCallback(function(q,be){return be===void 0&&(be=!1),xe(B)&&typeof(B==null?void 0:B.formatter)=="function"?B.formatter(q,be):q},[B]),Ee=function(q,be){(q!==dt.current||be!==rt.current)&&(ve(q),dt.current=q,rt.current=be,D&&D(q,be))},Se=function(q){U!==q&&(oe(q),W&&W(q),x&&x.onVisibleChange&&x.onVisibleChange(q))};d.useEffect(function(){V?Array.isArray(Y)||Z(Y===void 0?[]:[Y]):Array.isArray(Y)&&Z(Y.length===0?void 0:Y[0])},[V,Y]),d.useEffect(function(){if(U){ce(we);var q=ct(Y)?Y[0]:Y;!Le&&ue.has(q)&&setTimeout(function(){return le(q)})}},[U]),d.useEffect(function(){(Be.current==="up"||Be.current==="down")&&(le(de),Be.current="none")},[de]),d.useEffect(function(){ce(we)},[JSON.stringify(fe.map(function(q){var be;return(be=q==null?void 0:q.props)===null||be===void 0?void 0:be.value}))]),d.useEffect(function(){tt.current=tt.current.filter(function(q){return V?ct(Y)&&Y.indexOf(q.value)>-1:q.value===Y})},[Y,V]),d.useEffect(function(){if(B){var q=void 0;if(la(Y,V))q=[];else{var be=Array.isArray(Y)?Y:[Y],je=be.filter(function($e){var nt,jt=ue.get($e)||((nt=tt.current.find(function(Xt){return Xt.value===$e}))===null||nt===void 0?void 0:nt.option);return!jt||jt._origin===vc}).map(function($e){return se($e)}),ke=Q.filter(function($e){var nt=xe($e)?$e.value:$e;return be.indexOf(nt)!==-1});q=ke.concat(je)}var Qe=function($e){return $e.map(function(nt){return xe(nt)?nt.value:nt}).toString()};Qe(q)!==Qe(Q)&&me(q)}},[Y,B,V,se]),d.useEffect(function(){B&&pe(ae&&!ue.has(ae)?se(ae,!0):null)},[ae,se]),d.useEffect(function(){var q=rt.current;ne===ae&&(q==="manual"||q==="optionListHide")&&L&&L(ae,q)},[ae]);var ze=d.useCallback(function(q){var be=ue.get(q);if(be){var je=tt.current.findIndex(function(Qe){return Qe.value===q});return je>-1?tt.current.splice(je,1,{value:q,option:be}):tt.current.push({value:q,option:be}),be}var ke=tt.current.find(function(Qe){return Qe.value===q});return ke&&ke.option},[ue]),_e=function(q){if(Oe.length){if(de===void 0||!ue.has(de))return Pe[Oe[0]];var be=ue.get(de),je=be._index,ke=Oe.indexOf(je),Qe=Oe.length;return Pe[Oe[((q==="up"?ke-1:ke+1)+Qe)%Qe]]}},Me=function(q,be){be===void 0&&(be=la(q,V));var je=q,ke=q===void 0?void 0:Array.isArray(q)?q.map(ze):ze(q);if(C&&!be){var Qe=function($e,nt){var jt,Xt;if(nt)return nt.children;var lt="value"in o?o.value:"defaultValue"in o?o.defaultValue:null;if(Array.isArray(lt))try{for(var Yt=sb(lt),qt=Yt.next();!qt.done;qt=Yt.next()){var un=qt.value;if(xe(un)&&un.value===$e)return un.label}}catch(oi){jt={error:oi}}finally{try{qt&&!qt.done&&(Xt=Yt.return)&&Xt.call(Yt)}finally{if(jt)throw jt.error}}else if(xe(lt)&&lt.value===$e)return lt.label};Array.isArray(q)?je=q.map(function($e,nt){return{value:$e,label:Qe($e,ke[nt])}}):je={value:q,label:Qe(q,ke)}}return{option:ke,value:je}},Re=function(q){if(Z(q),j){var be=Me(q);j(be.value,be.option)}},Xe=function(q,be){if(be==="remove"||be==="add"&&ue.get(q)){var je=be==="add"?Y.concat(q):Y.filter(function($e){return $e!==q}),ke=be==="add"?M:R;if(Re(je),typeof ke=="function"){var Qe=Me(q,!1);ke(Qe.value,Qe.option)}}},Je=function(q,be){be||(V?(Xe(q,Y.indexOf(q)===-1?"add":"remove"),(!xe(g)||!g.retainInputValueWhileSelect)&&Ee("","optionChecked")):(q!==Y&&Re(q),setTimeout(function(){Se(!1)})))},vt=Yf(new Map([[Yi.code,function(){return Se(!1)}],[Lt.code,function(){if(U){var q=ue.get(de);q&&Je(de,q.disabled)}else Se(!0)}],[Ws.code,function(){return Se(!1)}],[kf.code,function(){if(U)return Be.current="up",ce(_e("up")),!1}],[Af.code,function(){if(U)return Be.current="down",ce(_e("down")),!1}]])),or=function(){var q,be,je,ke=(x==null?void 0:x.autoAlignPopupWidth)===!1&&(!(!((be=x==null?void 0:x.style)===null||be===void 0)&&be.width)||((je=x==null?void 0:x.style)===null||je===void 0?void 0:je.width)==="auto"),Qe=ke&&Ne,$e="notFoundContent"in o?p:a("Select"),nt=fe.length?l.createElement(id,mr({id:qe,role:"listbox",style:T,className:K(re+"-popup-inner",P),ref:Ae,data:fe,height:null,isStaticItemHeight:!Ce,measureLongestItem:ke,itemKey:function(lt){return lt.props._key},onMouseDown:ob,onMouseMove:function(){Be.current=null},onScroll:function(lt){return A&&A(lt.target)}},I,{threshold:Qe?null:I==null?void 0:I.threshold}),function(lt){var Yt;if(pd(lt))return l.createElement(lt.type,mr({},lt.props,{prefixCls:re}));if(vd(lt)){var qt=(Yt=lt.props)===null||Yt===void 0?void 0:Yt.value,un=xe(te)?te.value:te,oi=Q.map(function(fn){return xe(fn)?fn.value:fn}),vv={prefixCls:re,rtl:s,_valueActive:de,_valueSelect:Y,_isMultipleMode:V,_isUserCreatingOption:B&&un===qt,_isUserCreatedOption:B&&oi.indexOf(qt)>-1,_onClick:Je,_onMouseEnter:function(fn){Be.current===null&&ce(fn)},_onMouseLeave:function(){Be.current===null&&ce(void 0)}};return lt&&l.createElement(lt.type,mr({},lt.props,vv))}return lt}):null,jt=B&&ae,Xt=$e&&!jt?l.createElement("div",{style:T,className:K(re+"-popup-inner",P)},$e):null;return l.createElement("div",{className:K(re+"-popup",(q={},q[re+"-popup-hidden"]=nt===null&&Xt===null,q[re+"-popup-multiple"]=V,q)),tabIndex:-1,onKeyDown:function(lt){return vt(lt)}},typeof _=="function"?_(nt||Xt):nt||Xt)},$t=function(q){if(he.current=null,V&&ct(h)&&h.length){var be=q.split(new RegExp("["+h.join("")+"]"));if(be.length>1){he.current=Date.now();var je=be.filter(function($e,nt){return $e&&be.indexOf($e)===nt}),ke=Y.slice(0),Qe=!1;je.forEach(function($e){ke.indexOf($e)===-1&&(B||ue.get($e))&&(ke.push($e),Qe=!0)}),Qe&&Re(ke)}}return!!he.current},Zt={onFocus:S,onBlur:function(q){N==null||N(q),!U&&!Ie.current&&Ee("","optionListHide")},onKeyDown:function(q){if(q.target.tagName==="INPUT"&&q.target.value){var be=q.key===Ws.key,je=q.key===Lt.key;if(je||be){var ke=je?`
`:be?"	":"";$t(q.target.value+ke)&&Ee("","tokenSeparator")}}vt(q),H==null||H(q)},onChangeInputValue:function(q,be){var je=be.nativeEvent.inputType;je!=="insertFromPaste"&&$t(q),he.current?Ee("","tokenSeparator"):Ee(q,"manual"),!U&&q&&Se(!0)},onPaste:function(q){$t(q.clipboardData.getData("text")),F==null||F(q)},onRemoveCheckedItem:function(q,be,je){je.stopPropagation(),Xe(Y[be],"remove")},onClear:function(q){if(q.stopPropagation(),V){var be=Y.filter(function(je){var ke=ue.get(je);return ke&&ke.disabled});Re(be)}else Re(void 0);Ee("","manual"),k==null||k(U)}};d.useImperativeHandle(n,function(){var q,be;return{dom:(q=Fe.current)===null||q===void 0?void 0:q.dom,focus:function(){Fe.current&&Fe.current.focus()},blur:function(){Fe.current&&Fe.current.blur()},hotkeyHandler:vt,activeOptionValue:de,getOptionInfoByValue:ze,getOptionInfoList:function(){return cb([],lr(ue.values()),!1).filter(function(je){return je._valid})},scrollIntoView:le,getRootDOMNode:(be=Fe.current)===null||be===void 0?void 0:be.getRootDOMNode}},[vt,ue,de,ze,le]);var wr=function(q){return l.createElement(vl,mr({ref:function(be){return De.current=be},popup:or,trigger:E,disabled:v,getPopupContainer:w,classNames:"slideDynamicOrigin",autoAlignPopupWidth:!0,popupAlign:fb,popupVisible:U,unmountOnExit:m,onVisibleChange:Se,__onExit:function(){Ie.current=!0},__onExited:function(){Ie.current=!1,Ee("","optionListHide")}},Ge(x,["popupVisible","onVisibleChange"])),q)},Or=typeof O=="function"?O(Me(Y)):O;return l.createElement(pt,{onResize:function(){return De.current.updatePopupPosition()}},Or!=null?wr(Or):l.createElement(Kh,mr({},o,Zt,{ref:Fe,value:Y,inputValue:ae,popupVisible:U,rtl:s,prefixCls:re,allowCreate:!!B,ariaControls:qe,isEmptyValue:Le,isMultiple:V,onSort:Re,renderText:function(q){var be=ze(q),je=q;if(Ke(c)){var ke=Me(q,!1);je=c(ke.option||null,ke.value)}else{var Qe=!1;if(C){var $e=o.value||o.defaultValue;if(Array.isArray($e)){var nt=$e.find(function(jt){return xe(jt)&&jt.value===q});nt&&(je=nt.label,Qe=!0)}else xe($e)&&(je=$e.label,Qe=!0)}!Qe&&be&&"children"in be&&(je=be.children)}return{text:je,disabled:be&&be.disabled}},renderView:wr})))}var vb=l.forwardRef(db),ei=vb;ei.displayName="Select";ei.Option=Xf;ei.OptGroup=rh;const gd=ei;var pb=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function mb(e){var n,t=e.prefixCls,r=e.maxCount,a=e.count,i=e.className,s=e.style,o=pb(d.useState(!1),2),u=o[0],c=o[1],f=cl(a),v=a!==f;return l.createElement(nr,{classNames:"badge-zoom",in:a>0,timeout:300,appear:!0,mountOnEnter:!0,unmountOnExit:!0,onEntered:function(){c(!0)}},l.createElement("span",{className:i,style:s},l.createElement("span",{key:a,className:K((n={},n[t+"-number-text"]=u&&v,n))},r&&a>r?r+"+":a)))}var kt=globalThis&&globalThis.__assign||function(){return kt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},kt.apply(this,arguments)},gb=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},hb=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},yb=["red","orangered","orange","gold","lime","green","cyan","arcoblue","purple","pinkpurple","magenta","gray"],bb={count:0,maxCount:99};function xb(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,bb,i==null?void 0:i.Badge),u=o.count,c=o.text,f=o.className,v=o.dotClassName,m=o.dot,p=o.maxCount,g=o.color,h=o.dotStyle,b=o.offset,y=o.style,C=o.status,w=o.children,E=gb(o,["count","text","className","dotClassName","dot","maxCount","color","dotStyle","offset","style","status","children"]),O=a("badge"),x=kt({},h||{}),_=hb(b||[],2),T=_[0],P=_[1];T&&(x.marginRight=-T),P&&(x.marginTop=P);var I=function(){var j,M;if(xe(u))return l.createElement("span",{className:K(O+"-custom-dot",v),style:x},u);var R=!g||yb.indexOf(g)>-1?{}:{backgroundColor:g};return c&&!g&&!C?l.createElement("span",{className:K(O+"-text",v),style:x},c):C||g&&typeof u=="number"&&u<=0?l.createElement("span",{className:O+"-status-wrapper"},l.createElement("span",{className:K(O+"-status-dot",(j={},j[O+"-status-"+C]=C,j[O+"-color-"+g]=g,j),v),style:kt(kt({},R),x)}),c&&l.createElement("span",{className:O+"-status-text"},c)):(m||g)&&typeof u=="number"&&u>0?l.createElement(nr,{classNames:"badge-zoom",in:m||!!g,timeout:200,appear:!0,mountOnEnter:!0,unmountOnExit:!0},l.createElement("span",{className:K(O+"-dot",(M={},M[O+"-color-"+g]=g,M),v),style:kt(kt({},R),x)})):l.createElement(mb,{prefixCls:O,className:K(O+"-number",v),style:kt(kt({},R),x),maxCount:p,count:u})};return l.createElement("span",kt({className:K(O,(t={},t[O+"-status"]=C,t[O+"-no-children"]=!w,t[O+"-rtl"]=s,t),f),ref:n,style:y},E),w,I())}var hd=d.forwardRef(xb);hd.displayName="Badge";const ew=hd;function pc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function mc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?pc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):pc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function Cb(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=mc(mc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-more")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M38 25v-2h2v2h-2ZM23 25v-2h2v2h-2ZM8 25v-2h2v2H8Z"}),l.createElement("path",{d:"M38 25v-2h2v2h-2ZM23 25v-2h2v2h-2ZM8 25v-2h2v2H8Z"}))}var xl=l.forwardRef(Cb);xl.defaultProps={isIcon:!0};xl.displayName="IconMore";const Cl=xl;var Dr=globalThis&&globalThis.__assign||function(){return Dr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Dr.apply(this,arguments)},Eb=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},wb=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Ob={position:"br",trigger:"hover",type:"default",icon:l.createElement(Cl,null),unmountOnExit:!0};function _b(e,n){var t,r=d.useContext(ye).componentConfig,a=He(e,Ob,r==null?void 0:r["Dropdown.Button"]),i=a.className,s=a.style,o=a.children,u=a.disabled,c=a.position,f=c===void 0?"br":c,v=a.type,m=v===void 0?"default":v,p=a.size,g=a.icon,h=g===void 0?l.createElement(Cl,null):g,b=a.onClick,y=a.buttonProps,C=a.buttonsRender,w=Eb(a,["className","style","children","disabled","position","type","size","icon","onClick","buttonProps","buttonsRender"]),E=l.createElement(ia,Dr({disabled:u,type:m,size:p,onClick:b},y),o),O=l.createElement(ia,{disabled:u,type:m,size:p,icon:h});C&&(t=wb(C([E,O]),2),E=t[0],O=t[1]);var x=u||!O||O.props&&O.props.loading;return l.createElement(ia.Group,Dr({className:i,style:s,ref:n},ir(a)),E,l.createElement(wl,Dr({disabled:u,position:f},w,{triggerProps:Dr({disabled:x},w==null?void 0:w.triggerProps)}),O))}var yd=d.forwardRef(_b);yd.displayName="DropdownButton";const Pb=yd;var Sn=globalThis&&globalThis.__assign||function(){return Sn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Sn.apply(this,arguments)},Tb=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},gc=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Sb=globalThis&&globalThis.__values||function(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")},Nb=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},Ib={position:"bl",trigger:"hover",unmountOnExit:!0},jb={left:4,right:4,top:4,bottom:4};function Mb(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,Ib,i==null?void 0:i.Dropdown),u=o.trigger,c=o.droplist,f=o.children,v=o.position,m=o.disabled,p=o.unmountOnExit,g=o.triggerProps,h=o.getPopupContainer,b=o.onVisibleChange,y=Tb(o,["trigger","droplist","children","position","disabled","unmountOnExit","triggerProps","getPopupContainer","onVisibleChange"]),C=a("dropdown"),w=d.useRef(null),E=gc(ot(!1,{defaultValue:o.defaultPopupVisible,value:o.popupVisible}),2),O=E[0],x=E[1],_=function(){return l.Children.only(c||l.createElement("span",null))},T=function(j){x(j),b&&b(j),g&&g.onVisibleChange&&g.onVisibleChange(j)},P=function(j){j!==O&&T(j)},I=function(){var j,M,R,k=_();if(Rt(k,"type.__ARCO_MENU__")||Rt(k,"props.isMenu")){var L=!0;try{for(var S=Sb(l.Children.toArray(k.props.children)),N=S.next();!N.done;N=S.next()){var A=N.value;if(A!=null){L=!1;break}}}catch(W){j={error:W}}finally{try{N&&!N.done&&(M=S.return)&&M.call(S)}finally{if(j)throw j.error}}return l.cloneElement(k,{prefixCls:K(C+"-menu",(R={},R[C+"-menu-hidden"]=L,R)),inDropdown:!0,selectable:!1,onClickMenuItem:function(){for(var W,D=[],F=0;F<arguments.length;F++)D[F]=arguments[F];var H=null,z=_();z.props.onClickMenuItem&&(H=(W=z.props).onClickMenuItem.apply(W,Nb([],gc(D),!1)));var B=w.current&&w.current.getRootElement();B&&B.focus&&B.focus(),H instanceof Promise?H.finally(function(){return T(!1)}):H!==!1&&T(!1)}})}return k};return l.createElement(vl,Sn({ref:function(j){return w.current=j},classNames:"slideDynamicOrigin",childrenPrefix:C,trigger:u,popup:I,mouseEnterDelay:400,mouseLeaveDelay:400,disabled:m,unmountOnExit:p,position:v,popupVisible:O,popupAlign:jb,getPopupContainer:h,alignPoint:u==="contextMenu"},Ka(y,Ff),ir(y),Ge(g,["onVisibleChange"]),{onVisibleChange:P}),l.isValidElement(f)?l.cloneElement(f,Sn(Sn({},typeof m=="boolean"?{disabled:m}:{}),{className:K((t={},t[C+"-popup-visible"]=O,t[[C]+"-rtl"]=s,t),f.props.className)})):f)}var Rb=l.forwardRef(Mb),El=Rb;El.displayName="Dropdown";El.Button=Pb;const wl=El;var kb=d.createContext({});const Gt=kb;var Ab=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Db=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function ti(e){var n=e.prefixCls,t=e.levelIndent,r=d.useContext(Gt).collapse,a=e.level-1;return!r&&a>0?l.createElement("span",null,Db([],Ab(new Array(a)),!1).map(function(i,s){return l.createElement("span",{key:s,className:n+"-indent",style:{width:t}})})):null}var Mt=globalThis&&globalThis.__assign||function(){return Mt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Mt.apply(this,arguments)},Lb=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},zb=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},bd=["popup","triggerProps","selectable"],xd=function(e){var n=[];return l.Children.forEach(e,function(t){var r,a=(r=t==null?void 0:t.type)===null||r===void 0?void 0:r.menuType;a==="MenuItem"||a==="SubMenu"?n.push(t):a==="MenuGroup"&&(n=n.concat(xd(t.props.children)))}),n},Cd=function(e,n,t){n===void 0&&(n=[]),t===void 0&&(t={});var r=xd(e);return r.forEach(function(a,i){var s,o,u,c=a.key,f=a.type.menuType,v=zb([c],Lb(n),!1),m={keyPath:[],prev:((o=r[i-1])===null||o===void 0?void 0:o.key)||null,next:((u=r[i+1])===null||u===void 0?void 0:u.key)||null};if(i===0||i===r.length-1){var p=v[1],g=i===0?"firstChild":"lastChild";p&&(t[p]=Mt(Mt({},t[p]),(s={},s[g]=c,s)))}switch(f){case"SubMenu":m.keyPath=v,Cd(a.props.children,v,t);break;case"MenuItem":m.keyPath=v,m.disabled=a.props.disabled;break}t[c]=Mt(Mt({},t[c]),m)}),t},ri=function(e,n){return l.Children.map(e,function(t,r){if(!t||!t.props)return t;var a=typeof t.type=="string",i=t.type&&t.type.menuType;if(!i&&t.props.children){var s=a?{}:n,o=ri(t.props.children,n);return l.cloneElement(t,Mt(Mt({},s),{_key:t.key,children:o.length===1?o[0]:o}))}return a?t:l.cloneElement(t,Mt(Mt(Mt({},n),t.props),{_key:t.key||"$menu-"+r}))})};function Ed(e,n){var t=!1;function r(a){!a||t||l.Children.forEach(a,function(i){if(i&&i.props&&i.type&&!t){var s=i.type.menuType,o=i.props.selectable;(s==="MenuItem"||s==="SubMenu"&&o)&&(t=n.indexOf(i.key)!==-1),!t&&i.props.children&&r(i.props.children)}})}return r(e),t}var Lr=globalThis&&globalThis.__assign||function(){return Lr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Lr.apply(this,arguments)},$b=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function Vb(e,n){var t,r=e._key,a=e.children,i=e.level,s=e.disabled,o=e.className,u=e.style,c=e.wrapper,f=c===void 0?"div":c,v=e.onClick,m=e.renderItemInTooltip,p=$b(e,["_key","children","level","disabled","className","style","wrapper","onClick","renderItemInTooltip"]),g=d.useContext(Gt),h=g.prefixCls,b=g.mode,y=g.collapse,C=g.inDropdown,w=g.levelIndent,E=g.selectedKeys,O=g.autoScrollIntoView,x=g.scrollConfig,_=g.tooltipProps,T=g.onClickMenuItem,P=d.useRef(null),I=ll(),j=b==="vertical"&&i>1,M=y&&!C&&i===1,R=E&&~E.indexOf(r);d.useEffect(function(){var S=R&&O;P.current&&S&&setTimeout(function(){P.current&&Cf(P.current,Lr({behavior:"smooth",block:"start",scrollMode:"if-needed",boundary:document.body},x))},I?500:0)},[R,O]);var k=function(S){s||(T(r,S),v&&v(S))},L=l.createElement(f,Lr({tabIndex:s?-1:0,role:"menuitem",ref:function(S){n=S,P.current=n},style:u,className:K(h+"-item",(t={},t[h+"-disabled"]=s,t[h+"-selected"]=R,t[h+"-item-indented"]=j&&!y,t),o),onClick:k,onKeyDown:function(S){var N=S.keyCode||S.which;N===Lt.code&&k(S)}},Ge(p,["key","_key"].concat(bd))),j&&!y?l.createElement(l.Fragment,null,l.createElement(ti,{prefixCls:h,levelIndent:w,level:i}),l.createElement("span",{className:h+"-item-inner",style:{display:"block"}},a)):a,R&&b==="horizontal"?l.createElement("div",{className:h+"-selected-label"}):null);return M?l.createElement(Cr,Lr({trigger:"hover",position:"right",content:typeof m=="function"?m():l.createElement("span",null,a),triggerProps:Lr({className:h+"-item-tooltip"},(_==null?void 0:_.triggerProps)||{})},Ge(_,["triggerProps"])),L):L}var Hb=d.forwardRef(Vb),Ol=Hb;Ol.displayName="MenuItem";Ol.menuType="MenuItem";const Wb=Ol;function Fb(e,n){var t=e.children,r=e.title,a=e.level,i=e.className,s=e.style,o=d.useContext(Gt),u=o.prefixCls,c=o.levelIndent,f=a===1?a+1:a,v=ri(t,{level:f});return l.createElement("div",{ref:n,className:K(u+"-group",i),style:s},l.createElement("div",{className:u+"-group-title"},l.createElement(ti,{level:a,prefixCls:u,levelIndent:c}),l.createElement("span",null,r)),v)}var Bb=d.forwardRef(Fb),_l=Bb;_l.displayName="MenuItemGroup";_l.menuType="MenuGroup";const Kb=_l;var Nn=globalThis&&globalThis.__assign||function(){return Nn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Nn.apply(this,arguments)},hc=globalThis&&globalThis.__awaiter||function(e,n,t,r){function a(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function o(f){try{c(r.next(f))}catch(v){s(v)}}function u(f){try{c(r.throw(f))}catch(v){s(v)}}function c(f){f.done?i(f.value):a(f.value).then(o,u)}c((r=r.apply(e,n||[])).next())})},yc=globalThis&&globalThis.__generator||function(e,n){var t={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,a,i,s;return s={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function o(c){return function(f){return u([c,f])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,a&&(i=c[0]&2?a.return:c[0]?a.throw||((i=a.return)&&i.call(a),0):a.next)&&!(i=i.call(a,c[1])).done)return i;switch(a=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return t.label++,{value:c[1],done:!1};case 5:t.label++,a=c[1],c=[0];continue;case 7:c=t.ops.pop(),t.trys.pop();continue;default:if(i=t.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){t=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){t.label=c[1];break}if(c[0]===6&&t.label<i[1]){t.label=i[1],i=c;break}if(i&&t.label<i[2]){t.label=i[2],t.ops.push(c);break}i[2]&&t.ops.pop(),t.trys.pop();continue}c=n.call(e,t)}catch(f){c=[6,f],a=0}finally{r=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}},Gb=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Ub=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Ei={height:0,visibility:"hidden"},Zb=function(e){var n,t=e._key,r=e.children,a=e.style,i=e.className,s=e.title,o=e.level,u=e.forwardedRef,c=e.selectable,f=Gb(e,["_key","children","style","className","title","level","forwardedRef","selectable"]),v=d.useContext(Gt),m=v.id,p=v.prefixCls,g=v.levelIndent,h=v.openKeys,b=h===void 0?[]:h,y=v.selectedKeys,C=y===void 0?[]:y,w=v.icons,E=v.onClickSubMenu,O=v.onClickMenuItem,x=p+"-inline",_=(b==null?void 0:b.indexOf(t))>-1,T=c&&C.indexOf(e._key)>-1||Ed(r,C),P=Ub(If(_?{height:"auto"}:Ei),2),I=P[0],j=P[1],M=function(N){E(t,o,"inline"),c&&O(t,N)},R=an(m+"-submenu-inline-"),k=ri(r,Nn(Nn({},Ka(f,bd)),{level:o+1,selectable:c})),L=l.createElement("div",{tabIndex:0,"aria-expanded":_,"aria-controls":R,className:K(x+"-header",(n={},n[p+"-selected"]=T,n)),onClick:M,onKeyDown:function(N){var A=N.keyCode||N.which;A===Lt.code&&M(N)}},l.createElement(ti,{level:o,prefixCls:p,levelIndent:g}),l.createElement("span",null,s),l.createElement("span",{className:p+"-icon-suffix "+(_?"is-open":"")},w&&w.horizontalArrowDown?w.horizontalArrowDown:l.createElement(Bn,null))),S=l.createElement("div",{id:R,className:K(x+"-content"),style:I},k);return l.createElement("div",Nn({ref:u,className:K(x,i),style:a},Ge(f,["key","popup","triggerProps"])),L,l.createElement(nr,{in:_,timeout:200,classNames:x,unmountOnExit:!1,onEnter:function(N){return hc(void 0,void 0,void 0,function(){return yc(this,function(A){switch(A.label){case 0:return N?[4,j(Ei)]:[2];case 1:return A.sent(),[4,j({height:N.scrollHeight})];case 2:return A.sent(),[2]}})})},onEntered:function(){j({height:"auto"})},onExit:function(N){return hc(void 0,void 0,void 0,function(){return yc(this,function(A){switch(A.label){case 0:return N?[4,j({height:N.scrollHeight})]:[2];case 1:return A.sent(),[4,j(Ei)];case 2:return A.sent(),[2]}})})}},S))};const Xb=Zb;function bc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function xc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?bc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):bc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function Yb(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=xc(xc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-right")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"m16 39.513 15.556-15.557L16 8.4"}))}var Pl=l.forwardRef(Yb);Pl.defaultProps={isIcon:!0};Pl.displayName="IconRight";const Ur=Pl;function Cc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Ec(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Cc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Cc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function qb(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Ec(Ec({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-left")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M32 8.4 16.444 23.956 32 39.513"}))}var Tl=l.forwardRef(qb);Tl.defaultProps={isIcon:!0};Tl.displayName="IconLeft";const Zr=Tl;var zr=globalThis&&globalThis.__assign||function(){return zr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},zr.apply(this,arguments)},Jb=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Qb=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},e2=function(e){var n,t=e._key,r=e.children,a=e.style,i=e.className,s=e.title,o=e.level,u=e.selectable,c=e.forwardedRef,f=e.triggerProps,v=Jb(e,["_key","children","style","className","title","level","selectable","forwardedRef","triggerProps"]),m=d.useContext(Gt),p=m.id,g=m.prefixCls,h=m.mode,b=m.inDropdown,y=m.levelIndent,C=m.selectedKeys,w=C===void 0?[]:C,E=m.icons,O=m.triggerProps,x=m.onClickSubMenu,_=m.onClickMenuItem,T=d.useContext(ye).rtl,P=zr(zr({},O),f),I=Qb(d.useState(!1),2),j=I[0],M=I[1],R=g+"-pop",k=u&&w.indexOf(e._key)>-1,L=h==="horizontal"&&!b,S=an(p+"-submenu-pop-"),N=function(){var F=E&&E.popArrowRight?E.popArrowRight:T?l.createElement(Zr,null):l.createElement(Ur,null),H=E&&E.horizontalArrowDown?E.horizontalArrowDown:l.createElement(Bn,null);return l.createElement("span",{className:g+"-icon-suffix"},L?H:F)},A=Ed(r,w)||k,W=T?["br","lt"]:["bl","rt"],D=function(F){x(t,o,"pop"),u&&_(t,F)};return l.createElement(wl,{trigger:"hover",popupVisible:j,onVisibleChange:M,droplist:l.createElement(po,{id:S,selectedKeys:w,onClickMenuItem:function(F,H){_(F,H),M(!1)}},r),triggerProps:zr({position:L?W[0]:W[1],showArrow:!0,autoAlignPopupMinWidth:!0,classNames:"fadeIn",duration:100,mouseEnterDelay:50,mouseLeaveDelay:50,className:K(R+"-trigger",P&&P.className)},Ge(P,["className"]))},l.createElement("div",zr({tabIndex:0,"aria-haspopup":!0,"aria-expanded":j,"aria-controls":S,ref:c,style:a,className:K(R,R+"-header",(n={},n[g+"-selected"]=A,n),i),onClick:D,onKeyDown:function(F){var H=F.keyCode||F.which;H===Lt.code?D(F):H===Df.code?M(!1):H===Lf.code&&M(!0)}},Ge(v,["key","popup"])),l.createElement(ti,{prefixCls:g,levelIndent:y,level:o}),s,N(),A&&h==="horizontal"?l.createElement("div",{className:g+"-selected-label"}):null))};const t2=e2;var ao=globalThis&&globalThis.__assign||function(){return ao=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ao.apply(this,arguments)};function r2(e,n){var t=e.children,r=e.popup,a=e.level,i=d.useContext(Gt),s=i.mode,o=i.collapse,u=i.inDropdown,c=i.collectInlineMenuKeys,f=!!(typeof r=="function"?r(a):r),v=f||o||u||s!=="vertical",m=v?t2:Xb;return d.useEffect(function(){return c(e._key),function(){c(e._key,!0)}},[]),l.createElement(m,ao({forwardedRef:n},e),t)}var n2=d.forwardRef(r2),Sl=n2;Sl.displayName="SubMenu";Sl.menuType="SubMenu";const wd=Sl;var a2=["transform","WebkitTransform","msTransform","MozTransform","OTransform"];function i2(e){var n={};return a2.forEach(function(t){n[t]=e}),n}function wc(e,n){if(!e||!n)return null;var t=n;t==="float"&&(t="cssFloat");try{if(document.defaultView){var r=document.defaultView.getComputedStyle(e,"");return e.style[t]||r?r[t]:""}}catch{return e.style[t]}}var Oc=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},_c=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},o2=5;function Pc(e){return e&&Math.ceil(+e.getBoundingClientRect().width)}function Tc(e){var n=Number(e.replace("px",""));return isNaN(n)?0:n}var l2=function(e){var n=e.children,t=e.ellipsisText,r=t===void 0?"···":t,a=e.onEllipsisChange,i=d.useContext(Gt).prefixCls,s=d.useRef(null),o=Oc(d.useState(null),2),u=o[0],c=o[1],f=i+"-overflow-sub-menu",v=i+"-overflow-hidden-menu-item",m=i+"-overflow-sub-menu-mirror",p=function(y){if(y!==u){var C=l.Children.toArray(n),w=y===null;a==null||a({lastVisibleIndex:w?C.length-1:y,overflowNodes:w?[]:C.slice(y+1)}),c(y)}};function g(){if(s.current){for(var y=s.current,C=Pc(y)-o2,w=[].slice.call(y.children),E=0,O=0,x=0,_=0;_<w.length;_++){var T=w[_],P=T.className.split(" "),I=P.indexOf(f)>-1,j=P.indexOf(m)>-1;if(!I){var M=Pc(T)+Tc(wc(T,"marginLeft"))+Tc(wc(T,"marginRight"));if(j){x=M;continue}if(O+=M,O>C){p(E-(O-M+x<=C?1:2));return}E++}}p(null)}}var h=function(y,C){return C===void 0&&(C=!1),l.createElement(wd,{title:l.createElement("span",null,r),key:"arco-menu-overflow-sub-menu"+(C?"-mirror":""),className:C?m:f,children:y})},b=function(){var y=null,C=h(null,!0),w=l.Children.map(n,function(E,O){var x,_,T=E;if(u!==null&&(O>u&&(T=l.cloneElement(E,{className:K(v,(_=(x=E)===null||x===void 0?void 0:x.props)===null||_===void 0?void 0:_.className)})),O===u+1)){var P=l.Children.toArray(n).slice(u+1).map(function(I){return l.cloneElement(I,{key:I.props._key})});y=h(P)}return T});return _c(_c([C],Oc(w),!1),[y],!1)};return l.createElement(pt,{onResize:g,getTargetDOMNode:function(){return s.current}},l.createElement("div",{className:i+"-overflow-wrap",ref:s},b()))};const s2=l2;function Sc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Nc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Sc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Sc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function c2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Nc(Nc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-drag-dot-vertical")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M17 8h2v2h-2V8ZM17 23h2v2h-2v-2ZM17 38h2v2h-2v-2ZM29 8h2v2h-2V8ZM29 23h2v2h-2v-2ZM29 38h2v2h-2v-2Z"}),l.createElement("path",{d:"M17 8h2v2h-2V8ZM17 23h2v2h-2v-2ZM17 38h2v2h-2v-2ZM29 8h2v2h-2V8ZM29 23h2v2h-2v-2ZM29 38h2v2h-2v-2Z"}))}var Nl=l.forwardRef(c2);Nl.defaultProps={isIcon:!0};Nl.displayName="IconDragDotVertical";const u2=Nl;function Ic(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function jc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Ic(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ic(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function f2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=jc(jc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-drag-dot")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M40 17v2h-2v-2h2ZM25 17v2h-2v-2h2ZM10 17v2H8v-2h2ZM40 29v2h-2v-2h2ZM25 29v2h-2v-2h2ZM10 29v2H8v-2h2Z"}),l.createElement("path",{d:"M40 17v2h-2v-2h2ZM25 17v2h-2v-2h2ZM10 17v2H8v-2h2ZM40 29v2h-2v-2h2ZM25 29v2h-2v-2h2ZM10 29v2H8v-2h2Z"}))}var Il=l.forwardRef(f2);Il.defaultProps={isIcon:!0};Il.displayName="IconDragDot";const d2=Il;function Mc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Rc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Mc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Mc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function v2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Rc(Rc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-caret-right")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M34.829 23.063c.6.48.6 1.394 0 1.874L17.949 38.44c-.785.629-1.949.07-1.949-.937V10.497c0-1.007 1.164-1.566 1.95-.937l16.879 13.503Z"}))}var jl=l.forwardRef(v2);jl.defaultProps={isIcon:!0};jl.displayName="IconCaretRight";const kc=jl;function Ac(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Dc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Ac(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Ac(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function p2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Dc(Dc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-caret-left")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M13.171 24.937a1.2 1.2 0 0 1 0-1.874L30.051 9.56c.785-.629 1.949-.07 1.949.937v27.006c0 1.006-1.164 1.566-1.95.937L13.171 24.937Z"}))}var Ml=l.forwardRef(p2);Ml.defaultProps={isIcon:!0};Ml.displayName="IconCaretLeft";const Lc=Ml;function zc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function $c(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?zc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):zc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function m2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=$c($c({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-caret-down")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M24.938 34.829a1.2 1.2 0 0 1-1.875 0L9.56 17.949c-.628-.785-.069-1.949.937-1.949h27.007c1.006 0 1.565 1.164.937 1.95L24.937 34.829Z"}))}var Rl=l.forwardRef(m2);Rl.defaultProps={isIcon:!0};Rl.displayName="IconCaretDown";const g2=Rl;function Vc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Hc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Vc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Vc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function h2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Hc(Hc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-caret-up")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M23.063 13.171a1.2 1.2 0 0 1 1.875 0l13.503 16.88c.628.785.069 1.949-.937 1.949H10.497c-1.006 0-1.565-1.164-.937-1.95l13.503-16.879Z"}))}var kl=l.forwardRef(h2);kl.defaultProps={isIcon:!0};kl.displayName="IconCaretUp";const y2=kl;var gr=globalThis&&globalThis.__assign||function(){return gr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},gr.apply(this,arguments)},b2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function Al(e){var n,t,r=e.className,a=e.direction,i=e.icon,s=e.onMouseDown,o=e.onResize,u=e.children,c=e.collapsible,f=c===void 0?{}:c,v=e.resizable,m=v===void 0?!0:v,p=e.renderChildren,g=b2(e,["className","direction","icon","onMouseDown","onResize","children","collapsible","resizable","renderChildren"]),h=d.useContext(ye),b=h.getPrefixCls,y=h.rtl,C=b("resizebox-trigger"),w=a==="horizontal",E=y&&!w,O=K(C,C+"-"+(w?"horizontal":"vertical"),(n={},n[C+"-not-resizable"]=!m,n),(t={},t[C+"-rtl"]=y,t),r),x=d.useRef(),_=E?[l.createElement(kc,{key:"prev"}),l.createElement(Lc,{key:"next"})]:[l.createElement(Lc,{key:"prev"}),l.createElement(kc,{key:"next"})],T=xe(f.prev)?gr(gr({},f.prev),{icon:f.prev.icon||(w?l.createElement(y2,null):_[0])}):{},P=xe(f.next)?gr(gr({},f.next),{icon:f.next.icon||(w?l.createElement(g2,null):_[1])}):{},I=function(){return T.icon&&!T.collapsed||P.collapsed?l.createElement("span",{className:K(C+"-icon",K(C+"-prev")),onClick:T.onClick},T.icon):l.createElement("span",{className:K(C+"-icon-empty")})},j=function(){return P.icon&&!P.collapsed||T.collapsed?l.createElement("span",{className:K(C+"-icon",K(C+"-next")),onClick:P.onClick},P.icon):l.createElement("span",{className:K(C+"-icon-empty")})},M=function(){return m?l.createElement("span",{className:C+"-icon"},i||(w?l.createElement(d2,null):l.createElement(u2,null))):l.createElement("span",{className:K(C+"-icon-empty")})},R=I(),k=M(),L=j(),S=function(){return l.createElement("div",{className:C+"-icon-wrapper"},R,k,L)};return m?l.createElement(pt,{onResize:o,getTargetDOMNode:function(){return x.current}},l.createElement("div",gr({ref:x},Ge(g,["style"]),{className:O,onMouseDown:s}),Ke(p)?p(R,k,L):u||S())):l.createElement("div",{className:O},Ke(p)?p(R,k,L):u||S())}var wi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Oi="horizontal",Wc="vertical";function x2(e,n){var t,r=e.style,a=e.className,i=e.component,s=i===void 0?"div":i,o=e.direction,u=o===void 0?"horizontal":o,c=e.icon,f=e.size,v=f===void 0?.5:f,m=e.min,p=e.max,g=e.panes,h=e.disabled,b=e.trigger,y=d.useContext(ye),C=y.getPrefixCls,w=y.rtl,E=C("resizebox-split"),O=u.includes(Oi),x=u.includes("reverse"),_=O&&w,T=!O,P=K(E,E+"-"+(O?Oi:Wc),(t={},t[E+"-rtl"]=w,t),a),I=wi(g,2),j=I[0],M=I[1],R=typeof v=="string",k=wi(d.useState(parseFloat(v)),2),L=k[0],S=k[1],N=wi(d.useState(0),2),A=N[0],W=N[1],D=d.useRef({startX:0,startY:0,startWidth:0,startHeight:0,startOffset:0,moving:!1}),F=d.useRef(),H=d.useRef([]);d.useImperativeHandle(n,function(){return F.current},[]);function z(ne,G){return parseFloat(ne)/parseFloat(G)}function B(ne,G,U,oe){var ie=typeof m=="string"?z(parseFloat(m),ne):m||0,te=typeof p=="string"?z(parseFloat(p),ne):p||1,pe=x?-1:1,Te=_?-1:1;pe*=Te;var Q=R?G+(oe-U)*pe:z(ne*G+(oe-U)*pe,ne),me=R?ie*ne:ie,ee=R?te*ne:te;return Q=Math.max(Q,me),Q=Math.min(Q,ee),Q}function re(ne){var G,U;e.onMovingStart&&e.onMovingStart(),D.current.moving=!0,D.current.startX=ne.pageX,D.current.startY=ne.pageY,D.current.startWidth=(G=F.current)===null||G===void 0?void 0:G.offsetWidth,D.current.startHeight=(U=F.current)===null||U===void 0?void 0:U.offsetHeight,D.current.startOffset=L,it(window,"mousemove",V),it(window,"touchmove",V),it(window,"mouseup",$),it(window,"touchend",$),it(window,"contextmenu",$),document.body.style.cursor=T?"row-resize":"col-resize"}function V(ne){if(D.current.moving){var G=O?B(D.current.startWidth,D.current.startOffset,D.current.startX,ne.pageX):B(D.current.startHeight,D.current.startOffset,D.current.startY,ne.pageY);S(G),e.onMoving&&e.onMoving(ne,R?G+"px":G)}}function $(){D.current.moving=!1,ut(window,"mousemove",V),ut(window,"touchmove",V),ut(window,"mouseup",$),ut(window,"touchend",$),ut(window,"contextmenu",$),document.body.style.cursor="default",e.onMovingEnd&&e.onMovingEnd()}function X(ne){var G=ne[0].contentRect,U=G[T?"height":"width"];W(U)}function Z(){var ne=R?"px":"%";if(!L)return"0"+ne;var G=R?L:L*100;return"calc("+G+ne+" - "+A/2+"px)"}d.useEffect(function(){e.onPaneResize&&e.onPaneResize(H.current)},[L,A]),Fr(function(){var ne=parseFloat(v);L!==ne&&S(ne)},[v]);var Y=s,J=l.createElement("div",{className:K(E+"-pane","first-pane"),style:{flexBasis:Z()},ref:function(ne){H.current[0]=ne}},j),ae=l.createElement("div",{className:K(E+"-pane","second-pane"),ref:function(ne){H.current[1]=ne}},M),ve=x?[ae,J]:[J,ae];return l.createElement(Y,{style:r,className:P,ref:F},ve[0],!h&&l.createElement(Al,{className:E+"-trigger",direction:T?Oi:Wc,icon:c,onMouseDown:re,onResize:X},b),ve[1])}var Od=d.forwardRef(x2);Od.displayName="ResizeBoxSplit";const C2=Od;var io=globalThis&&globalThis.__assign||function(){return io=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},io.apply(this,arguments)},E2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},sr=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},_i=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},Pi="horizontal",Fc="vertical";function w2(e,n){var t,r,a=e.panes,i=e.style,s=e.className,o=e.component,u=o===void 0?"div":o,c=e.direction,f=c===void 0?"horizontal":c,v=e.icon,m=E2(e,["panes","style","className","component","direction","icon"]),p=d.useContext(ye),g=p.getPrefixCls,h=p.rtl,b=1/a.length,y=d.useRef(),C=d.useRef(new Array(a.length).fill({moving:!1,startOffset:0,startPosition:0})),w=d.useRef([]),E=d.useRef(0),O=d.useRef([]),x=sr(d.useState(new Array(a.length).fill(b)),2),_=x[0],T=x[1],P=sr(d.useState(!1),2),I=P[0],j=P[1],M=sr(d.useState(new Array(a.length).fill(0)),2),R=M[0],k=M[1],L=sr(d.useState(new Array(Math.max(a.length-1,0)).fill({prev:!1,next:!1})),2),S=L[0],N=L[1],A=g("resizebox-split-group"),W=f===Pi,D=W&&h,F=!W,H=K(A,A+"-"+(W?Pi:Fc),(t={},t[A+"-moving"]=I,t),(r={},r[A+"-rtl"]=h,r),s),z=u,B=function(){var G=[];a.forEach(function(te){var pe=te.size;ft(pe)?G.push(void 0):G.push(V(pe))});var U=G.filter(function(te){return!te}),oe=1-G.reduce(function(te,pe){var Te=te||0,Q=pe||0;return Te+Q},0),ie=oe/U.length;return G=G.map(function(te){return ft(te)?ie:te}),G},re=function(G){var U=R[G-1]||0,oe=R[G],ie=_[G]*100,te="%";return"calc("+ie+te+" - "+(U+oe)/2+"px)"};function V(G){var U=W?y.current.offsetWidth:y.current.offsetHeight;if(!G||Ye(G)&&G<0)return 0;var oe=ar(G)?parseFloat(G)/U:G;return Math.min(oe,1)}var $=function(G){var U=Math.min(G+1,a.length-1),oe=_[G]+_[U],ie=V(a[G].min)||0,te=V(a[G].max)||oe,pe=V(a[U].min)||0,Te=V(a[U].max)||oe;return te=Math.min(oe-pe,te),Te=Math.min(oe-ie,Te),{currentMin:ie,currentMax:te,nextMin:pe,nextMax:Te}},X=function(G,U,oe){var ie=E.current,te=ie+1,pe=_i([],sr(_),!1),Te=D?-1:1,Q=_[ie],me=_[te],ee=Q+me,de=$(ie),ce=de.currentMin,ge=de.currentMax,fe=G+V((oe-U)*Te+"px");return fe=Math.max(ce,fe),fe=Math.min(ge,fe),pe[ie]=fe,pe[te]=ee-fe,pe};function Z(G,U){var oe=G[0].contentRect,ie=oe[F?"height":"width"],te=_i([],sr(R),!1);te[U]=ie,k(te)}var Y=function(G){var U=a[G].collapsible;xe(U)||(U=U?{prev:!0,next:!0}:{});var oe=U.prev,ie=U.next;if(!oe&&!ie)return{};if(!S[G])return{};var te=!!oe||!oe&&S[G].next,pe=!!ie||!ie&&S[G].prev;return{hasPrev:te,hasNext:pe}};function J(G,U){e.onMovingStart&&e.onMovingStart(U),E.current=U;var oe=C.current[U];oe.moving=!0,oe.startOffset=_[U],oe.startPosition=W?G.pageX:G.pageY,j(!0),it(window,"mousemove",ae),it(window,"touchmove",ae),it(window,"mouseup",ve),it(window,"touchend",ve),it(window,"contextmenu",ve),document.body.style.cursor=F?"row-resize":"col-resize"}function ae(G){var U=E.current,oe=C.current[U],ie=W?y.current.offsetWidth:y.current.offsetHeight;if(oe.moving){var te=X(oe.startOffset,oe.startPosition,W?G.pageX:G.pageY);T(te),O.current=te,e.onMoving&&e.onMoving(G,te.map(function(pe){return pe*ie+"px"}),U)}}function ve(){var G=E.current;C.current[G].moving=!1,j(!1),ut(window,"mousemove",ae),ut(window,"touchmove",ae),ut(window,"mouseup",ve),ut(window,"touchend",ve),ut(window,"contextmenu",ve),document.body.style.cursor="default",e.onMovingEnd&&e.onMovingEnd(G)}function ne(G,U,oe,ie){var te=U+1,pe=_i([],sr(_),!1),Te=_[U],Q=_[te],me=Te+Q,ee=W?y.current.offsetWidth:y.current.offsetHeight,de=$(U),ce=de.currentMin,ge=de.nextMin,fe=O.current[U],ue=O.current[te],Pe=S[U][oe];oe==="prev"?(Q!==ge||ue===ge)&&(fe=ce,ue=me-ce,Pe=!0):(Te!==ce||fe===ce)&&(fe=me-ge,ue=ge,Pe=!0),pe[U]=fe,pe[te]=ue,e.onMoving&&e.onMoving(G,pe.map(function(Oe){return Oe*ee+"px"}),U),e.onMovingEnd&&e.onMovingEnd(U),T(pe),Ke(ie)&&ie(G,U,oe,Pe)}return d.useEffect(function(){var G=B();T(G),O.current=G},[JSON.stringify(a.map(function(G){return G.size}))]),d.useImperativeHandle(n,function(){return y.current},[]),d.useEffect(function(){var G=[];_.forEach(function(U,oe){var ie={prev:!1,next:!1},te=oe+1,pe=$(oe),Te=pe.currentMin,Q=pe.nextMin;U===Te?ie.prev=!0:_[te]===Q&&(ie.next=!0),G.push(ie)}),N(G)},[_]),l.createElement(z,io({},Ge(m,["onMovingStart","onPaneResize","onMoving","onMovingEnd"]),{style:i,className:H,ref:y}),a.map(function(G,U){var oe=G.content,ie=G.disabled,te=G.trigger,pe=G.resizable,Te=pe===void 0?!0:pe,Q=G.collapsible,me=Q===void 0?{}:Q,ee=Y(U),de=ee.hasPrev,ce=ee.hasNext,ge=xe(me)&&xe(me.prev)?me.prev:{},fe=xe(me)&&xe(me.next)?me.next:{};return l.createElement(l.Fragment,{key:U},l.createElement("div",{className:A+"-pane",style:{flexBasis:re(U)},ref:function(ue){return w.current[U]=ue}},oe),!ie&&U!==a.length-1&&l.createElement(Al,{className:A+"-trigger",direction:F?Pi:Fc,icon:v,onResize:function(ue){return Z(ue,U)},onMouseDown:function(ue){return J(ue,U)},collapsible:{prev:de?{onClick:function(ue){return ne(ue,U,"prev",ge.onClick)},icon:ge.icon,collapsed:S[U].prev}:void 0,next:ce?{onClick:function(ue){return ne(ue,U,"next",fe.onClick)},icon:fe.icon,collapsed:S[U].next}:void 0},resizable:Te,renderChildren:te}))}))}var _d=d.forwardRef(w2);_d.displayName="ResizeBoxSplitGroup";const O2=_d;var Ft=globalThis&&globalThis.__assign||function(){return Ft=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ft.apply(this,arguments)},_2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Ti=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Pd="left",oo="right",lo="top",so="bottom",P2=[Pd,oo,lo,so],T2={component:"div",directions:["right"],resizeIcons:{},resizeTriggers:{}},S2=function(e){switch(e){case"left":return"right";case"right":return"left";default:return e}};function N2(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,T2,i==null?void 0:i.ResizeBox),u=o.style,c=o.className,f=o.component,v=o.directions,m=o.resizeIcons,p=o.children,g=o.resizeTriggers,h=o.width,b=o.height,y=_2(o,["style","className","component","directions","resizeIcons","children","resizeTriggers","width","height"]),C=s?v.map(function(V){return S2(V)}):v,w=a("resizebox"),E=K(w,(t={},t[w+"-rtl"]=s,t),c),O=Ti(d.useState({}),2),x=O[0],_=O[1],T=Ti(ot(void 0,{value:h}),2),P=T[0],I=T[1],j=Ti(ot(void 0,{value:b}),2),M=j[0],R=j[1],k=d.useRef({startX:0,startY:0,startWidth:0,startHeight:0,direction:oo,moving:!1,padding:{top:0,bottom:0,left:0,right:0}}),L=d.useRef();d.useImperativeHandle(n,function(){return L.current},[]);function S(V){return[lo,so].indexOf(V)>-1}function N(V,$){if(V===0)return 0;var X=V-$;return X<=0?0:X}function A(V){var $="data-arco-origin-cursor";if(V)document.body.setAttribute($,document.body.style.cursor),document.body.style.cursor=V;else{var X=document.body.getAttribute($);document.body.style.cursor=X||"",document.body.removeAttribute($)}}function W(V,$){var X,Z;o.onMovingStart&&o.onMovingStart(),k.current.moving=!0,k.current.startX=$.pageX,k.current.startY=$.pageY,k.current.direction=V;var Y=k.current.padding,J=Y.top,ae=Y.left,ve=Y.right,ne=Y.bottom;k.current.startWidth=N((X=L.current)===null||X===void 0?void 0:X.clientWidth,ae+ve),k.current.startHeight=N((Z=L.current)===null||Z===void 0?void 0:Z.clientHeight,J+ne),it(window,"mousemove",D),it(window,"touchmove",D),it(window,"mouseup",F),it(window,"touchend",F),it(window,"contextmenu",F),A(S(V)?"row-resize":"col-resize")}function D(V){if(!k.current.moving)return!1;var $=k.current,X=$.startX,Z=$.startY,Y=$.startWidth,J=$.startHeight,ae=Y,ve=J,ne=V.pageX-X,G=V.pageY-Z;switch(k.current.direction){case Pd:ae=Y-ne,I(ae);break;case oo:ae=Y+ne,I(ae);break;case lo:ve=J-G,R(ve);break;case so:ve=J+G,R(ve);break}o.onMoving&&o.onMoving(V,{width:ae,height:ve})}function F(){k.current.moving=!1,H(),A(),o.onMovingEnd&&o.onMovingEnd()}function H(){ut(window,"mousemove",D),ut(window,"touchmove",D),ut(window,"mouseup",F),ut(window,"touchend",F),ut(window,"contextmenu",F)}function z(V,$){var X=S(V),Z=$[0].contentRect,Y=""+V.slice(0,1).toUpperCase()+V.slice(1),J=Z[X?"height":"width"];k.current.padding[V]=J,_(function(ae){var ve;return Ft(Ft({},ae),(ve={},ve["padding"+Y]=J,ve))})}var B=Ft(Ft(Ft(Ft({},x),u||{}),Ye(P)?{width:P}:{}),Ye(M)?{height:M}:{}),re=f;return l.createElement(re,Ft({},Ge(y,["onMovingStart","onMoving","onMovingEnd"]),{style:B,className:E,ref:L}),p,C.map(function(V){if(P2.indexOf(V)!==-1)return l.createElement(Al,{key:V,className:w+"-direction-"+V,direction:S(V)?"horizontal":"vertical",icon:m[V],onMouseDown:function($){W(V,$)},onResize:function($){z(V,$)}},g[V])}))}var I2=d.forwardRef(N2),ni=I2;ni.Split=C2;ni.SplitGroup=O2;ni.displayName="ResizeBox";const j2=ni;var Ea=globalThis&&globalThis.__assign||function(){return Ea=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ea.apply(this,arguments)},wa=["xxxl","xxl","xl","lg","md","sm","xs"],En={xs:"(max-width: 575px)",sm:"(min-width: 576px)",md:"(min-width: 768px)",lg:"(min-width: 992px)",xl:"(min-width: 1200px)",xxl:"(min-width: 1600px)",xxxl:"(min-width: 2000px)"},cr=[],M2=-1,Jn={},R2={matchHandlers:{},dispatch:function(e,n){return Jn=e,cr.length<1?!1:(cr.forEach(function(t){t.func(Jn,n)}),!0)},subscribe:function(e){cr.length===0&&this.register();var n=(++M2).toString();return cr.push({token:n,func:e}),e(Jn,null),n},unsubscribe:function(e){cr=cr.filter(function(n){return n.token!==e}),cr.length===0&&this.unregister()},unregister:function(){var e=this;Object.keys(En).forEach(function(n){var t=En[n],r=e.matchHandlers[t];r&&r.mql&&r.listener&&r.mql.removeListener(r.listener)})},register:function(){var e=this;Object.keys(En).forEach(function(n){var t=En[n],r=function(i){var s,o=i.matches;e.dispatch(Ea(Ea({},Jn),(s={},s[n]=o,s)),n)},a=window.matchMedia(t);a.addListener(r),e.matchHandlers[t]={mql:a,listener:r},r(a)})}};const Xr=R2;var $r=globalThis&&globalThis.__assign||function(){return $r=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},$r.apply(this,arguments)},Bc=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Td=d.createContext({siderCollapsed:!1,collapsedWidth:64}),k2=function(){var e=0;return function(n){return n===void 0&&(n=""),e+=1,""+n+e}}();function A2(e,n){var t,r,a=e.children,i=e.className,s=e.style,o=e.theme,u=o===void 0?"light":o,c=e.trigger,f=e.reverseArrow,v=e.collapsedWidth,m=v===void 0?48:v,p=e.width,g=p===void 0?200:p,h=e.collapsible,b=e.resizeDirections,y=e.onSiderMount,C=e.onSiderUnmount,w=e.breakpoint,E=e.onBreakpoint,O=e.onCollapse,x=e.resizeBoxProps,_=x===void 0?{}:x,T=k2("arco-sider-"),P=d.useContext(ye).getPrefixCls,I=P("layout-sider"),j=Bc(ot(!1,{value:"collapsed"in e?e.collapsed:void 0,defaultValue:e.defaultCollapsed}),2),M=j[0],R=j[1],k=Ye(g)?g+"px":String(g),L=Ye(m)?""+m:String(m),S=Bc(d.useState(M?L:k),2),N=S[0],A=S[1],W=d.useRef(null),D=d.useRef(null);D.current={breakpoint:w,collapsed:M,onCollapse:O,onBreakpoint:E},d.useEffect(function(){return y&&y(T),h&&w in En&&(W.current=Xr.subscribe(function(re,V){var $=D.current,X=$.breakpoint,Z=$.collapsed,Y=$.onCollapse,J=$.onBreakpoint;if(!V||V===X){var ae=!re[X];ae!==Z&&(R(ae),Y&&Y(ae,"responsive")),J&&J(ae)}})),function(){C&&C(T),W.current&&Xr.unsubscribe(W.current)}},[]),d.useEffect(function(){var re=Ye(m)?m+"px":String(m);A(M?re:k)},[M,k,m]);var F=b&&ct(b)||((r=_.directions)===null||r===void 0?void 0:r.length),H=F?j2:"aside",z=function(){var re,V=c||(M?f?l.createElement(Zr,null):l.createElement(Ur,null):f?l.createElement(Ur,null):l.createElement(Zr,null));return h&&c!==null?l.createElement("div",{style:{width:N},className:K(I+"-trigger",(re={},re[I+"-trigger-light"]=u==="light",re)),onClick:function(){R(!M),O&&O(!M,"clickTrigger")}},V):null},B=d.useMemo(function(){return F?$r($r({component:"aside"},_),{width:N,directions:b,onMoving:function(re,V){var $;A(V.width+"px"),($=_==null?void 0:_.onMoving)===null||$===void 0||$.call(_,re,V)}}):{}},[F,b,N,_]);return l.createElement(Td.Provider,{value:{siderCollapsed:M,collapsedWidth:m}},l.createElement(H,$r({ref:n,style:$r({width:N},s),className:K(I,(t={},t[I+"-light"]=u==="light",t[I+"-has-trigger"]=c!==null&&h,t[I+"-collapsed"]=M,t),i)},B),l.createElement("div",{className:I+"-children"},a),z()))}var D2=d.forwardRef(A2),Dl=D2;Dl.displayName="LayoutSider";Dl.__ARCO_SIGN__="sider";const L2=Dl;var co=globalThis&&globalThis.__assign||function(){return co=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},co.apply(this,arguments)},z2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function $2(e,n){var t=d.useContext(ye).getPrefixCls,r=e.className,a=e.children,i=z2(e,["className","children"]),s=t("layout-header"),o=K(s,r);return l.createElement("header",co({ref:n},i,{className:o}),a)}var Sd=d.forwardRef($2);Sd.displayName="LayoutHeader";const V2=Sd;var uo=globalThis&&globalThis.__assign||function(){return uo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},uo.apply(this,arguments)},H2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function W2(e,n){var t=e.className,r=e.children,a=H2(e,["className","children"]),i=d.useContext(ye).getPrefixCls,s=i("layout-footer"),o=K(s,t);return l.createElement("footer",uo({ref:n},a,{className:o}),r)}var Nd=d.forwardRef(W2);Nd.displayName="LayoutFooter";const F2=Nd;var fo=globalThis&&globalThis.__assign||function(){return fo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},fo.apply(this,arguments)},B2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function K2(e,n){var t=e.className,r=e.children,a=B2(e,["className","children"]),i=d.useContext(ye).getPrefixCls,s=i("layout-content"),o=K(s,t);return l.createElement("main",fo({ref:n},a,{className:o}),r)}var Id=d.forwardRef(K2);Id.displayName="LayoutContent";const G2=Id;var vo=globalThis&&globalThis.__assign||function(){return vo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},vo.apply(this,arguments)},U2=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Kc=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Gc=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function Z2(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=He(e,{},i==null?void 0:i.Layout),o=s.className,u=s.hasSider,c=s.children,f=U2(s,["className","hasSider","children"]),v=Kc(d.useState([]),2),m=v[0],p=v[1],g=a("layout"),h=K(g,(t={},t[g+"-has-sider"]=typeof u=="boolean"?u:m.length>0,t),o);return l.createElement("section",vo({ref:n},f,{className:h}),l.Children.map(c,function(b){var y=Rt(b,"type.__ARCO_SIGN__")||Rt(b,"props.sign");return b&&y==="sider"?l.cloneElement(b,{onSiderMount:function(C){return p(Gc(Gc([],Kc(m),!1),[C],!1))},onSiderUnmount:function(C){return p(m.filter(function(w){return w!==C}))}}):b}))}var X2=d.forwardRef(Z2),on=X2;on.displayName="Layout";on.Sider=L2;on.Header=V2;on.Footer=F2;on.Content=G2;const tw=on;function Uc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Zc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Uc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Uc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function Y2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Zc(Zc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-menu-fold")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M42 11H6M42 24H22M42 37H6M13.66 26.912l-4.82-3.118 4.82-3.118v6.236Z"}))}var Ll=l.forwardRef(Y2);Ll.defaultProps={isIcon:!0};Ll.displayName="IconMenuFold";const q2=Ll;function Xc(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Yc(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Xc(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Xc(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function J2(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Yc(Yc({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-menu-unfold")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M6 11h36M22 24h20M6 37h36M8 20.882 12.819 24 8 27.118v-6.236Z"}))}var zl=l.forwardRef(J2);zl.defaultProps={isIcon:!0};zl.displayName="IconMenuUnfold";const Q2=zl;var In=globalThis&&globalThis.__assign||function(){return In=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},In.apply(this,arguments)},e4=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Qn=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},t4=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},r4="light",n4={mode:"vertical",selectable:!0,ellipsis:!0};function a4(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,n4,i==null?void 0:i.Menu),u=o.style,c=o.children,f=o.className,v=o.prefixCls,m=o.mode,p=o.theme,g=o.icons,h=o.levelIndent,b=o.collapse,y=o.inDropdown,C=o.selectable,w=o.triggerProps,E=o.tooltipProps,O=o.ellipsis,x=o.accordion,_=o.autoOpen,T=o.autoScrollIntoView,P=o.scrollConfig,I=o.hasCollapseButton,j=o.defaultOpenKeys,M=o.defaultSelectedKeys,R=o.openKeys,k=o.selectedKeys,L=o.onClickSubMenu,S=o.onClickMenuItem,N=o.onCollapseChange,A=o.onEllipsisChange,W=e4(o,["style","children","className","prefixCls","mode","theme","icons","levelIndent","collapse","inDropdown","selectable","triggerProps","tooltipProps","ellipsis","accordion","autoOpen","autoScrollIntoView","scrollConfig","hasCollapseButton","defaultOpenKeys","defaultSelectedKeys","openKeys","selectedKeys","onClickSubMenu","onClickMenuItem","onCollapseChange","onEllipsisChange"]),D=Qn(ot([],{defaultValue:j,value:R}),2),F=D[0],H=D[1],z=Qn(ot([],{defaultValue:M,value:k}),2),B=z[0],re=z[1],V=Qn(ot(!1,{value:b}),2),$=V[0],X=V[1],Z=d.useContext(Gt),Y=d.useContext(Td).siderCollapsed,J=v||a("menu"),ae=Y||$||y||m==="popButton",ve=p||Z.theme||r4,ne=d.useRef([]),G=d.useRef([]),U=gl(),oe=Er(),ie=d.useMemo(function(){return Cd(c)},[c]),te=an(J+"-"),pe=W.id||te;d.useEffect(function(){var ee=F.filter(function(ce){return ne.current.indexOf(ce)!==-1});if(_){var de=ne.current.filter(function(ce){return G.current.indexOf(ce)===-1});ee=F.concat(de)}H(x?ee.slice(0,1):ee),G.current=ne.current.slice()},[ne.current.toString()]);var Te=m!=="horizontal"&&m!=="popButton"&&!y&&I,Q=function(){var ee=ri(c,{level:1}),de=$?g&&g.collapseActive||l.createElement(Q2,null):g&&g.collapseDefault||l.createElement(q2,null),ce=function(){var ge=!$;X(ge),N&&N(ge)};return l.createElement(l.Fragment,null,l.createElement("div",{className:J+"-inner"},m==="horizontal"&&O!==!1?l.createElement(s2,{ellipsisText:xe(O)?O.text:"···",onEllipsisChange:A},ee):ee),Te&&l.createElement("div",In({tabIndex:0,role:"button","aria-controls":pe,"aria-expanded":!$,className:J+"-collapse-button",onClick:ce},oe({onPressEnter:ce})),de))},me=In({},u);return ae&&!y&&delete me.width,l.createElement("div",In({id:Te?pe:void 0,role:"menu"},Ge(W,["isMenu"]),{ref:n,style:me,className:K(J,J+"-"+ve,J+"-"+(m==="horizontal"?"horizontal":"vertical"),(t={},t[J+"-collapse"]=ae,t[J+"-pop"]=m==="pop"||ae,t[J+"-pop-button"]=m==="popButton",t[J+"-rtl"]=s,t),f)}),l.createElement(Gt.Provider,{value:{mode:m,theme:ve,collapse:ae,levelIndent:h,inDropdown:y,selectedKeys:B,openKeys:F,icons:g,triggerProps:w,tooltipProps:E,autoScrollIntoView:T,scrollConfig:P,id:pe,prefixCls:J,collectInlineMenuKeys:function(ee,de){de?ne.current=ne.current.filter(function(ce){return ce!==ee}):ne.current.push(ee),U()},onClickMenuItem:function(ee,de){var ce;C&&re([ee]),S&&S(ee,de,(ce=ie[ee])===null||ce===void 0?void 0:ce.keyPath)},onClickSubMenu:function(ee,de,ce){var ge,fe=t4([],Qn(F),!1);ce==="inline"&&((F==null?void 0:F.indexOf(ee))>-1?x&&de===1?fe=[]:fe=F.filter(function(ue){return ue!==ee}):x&&de===1?fe=[ee]:fe=F.concat([ee])),H(fe),L&&L(ee,fe,(ge=ie[ee])===null||ge===void 0?void 0:ge.keyPath)}}},Q()))}var i4=d.forwardRef(a4),ln=i4;ln.displayName="Menu";ln.Item=Wb;ln.SubMenu=wd;ln.ItemGroup=Kb;ln.__ARCO_MENU__=!0;const po=ln;var o4=Ja;function l4(){this.__data__=new o4,this.size=0}var s4=l4;function c4(e){var n=this.__data__,t=n.delete(e);return this.size=n.size,t}var u4=c4;function f4(e){return this.__data__.get(e)}var d4=f4;function v4(e){return this.__data__.has(e)}var p4=v4,m4=Ja,g4=hl,h4=yl,y4=200;function b4(e,n){var t=this.__data__;if(t instanceof m4){var r=t.__data__;if(!g4||r.length<y4-1)return r.push([e,n]),this.size=++t.size,this;t=this.__data__=new h4(r)}return t.set(e,n),this.size=t.size,this}var x4=b4,C4=Ja,E4=s4,w4=u4,O4=d4,_4=p4,P4=x4;function sn(e){var n=this.__data__=new C4(e);this.size=n.size}sn.prototype.clear=E4;sn.prototype.delete=w4;sn.prototype.get=O4;sn.prototype.has=_4;sn.prototype.set=P4;var T4=sn,S4=zt,N4=S4.Uint8Array,I4=N4;function j4(e,n){return function(t){return e(n(t))}}var M4=j4,R4=Object.prototype;function k4(e){var n=e&&e.constructor,t=typeof n=="function"&&n.prototype||R4;return e===t}var A4=k4,D4=Wn,L4=Fn,z4="[object Arguments]";function $4(e){return L4(e)&&D4(e)==z4}var V4=$4,qc=V4,H4=Fn,jd=Object.prototype,W4=jd.hasOwnProperty,F4=jd.propertyIsEnumerable,B4=qc(function(){return arguments}())?qc:function(e){return H4(e)&&W4.call(e,"callee")&&!F4.call(e,"callee")},K4=B4,G4=9007199254740991;function U4(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=G4}var Md=U4,Z4=od,X4=Md;function Y4(e){return e!=null&&X4(e.length)&&!Z4(e)}var q4=Y4,Oa={exports:{}};function J4(){return!1}var Q4=J4;Oa.exports;(function(e,n){var t=zt,r=Q4,a=n&&!n.nodeType&&n,i=a&&!0&&e&&!e.nodeType&&e,s=i&&i.exports===a,o=s?t.Buffer:void 0,u=o?o.isBuffer:void 0,c=u||r;e.exports=c})(Oa,Oa.exports);var Rd=Oa.exports,ex=Wn,tx=Md,rx=Fn,nx="[object Arguments]",ax="[object Array]",ix="[object Boolean]",ox="[object Date]",lx="[object Error]",sx="[object Function]",cx="[object Map]",ux="[object Number]",fx="[object Object]",dx="[object RegExp]",vx="[object Set]",px="[object String]",mx="[object WeakMap]",gx="[object ArrayBuffer]",hx="[object DataView]",yx="[object Float32Array]",bx="[object Float64Array]",xx="[object Int8Array]",Cx="[object Int16Array]",Ex="[object Int32Array]",wx="[object Uint8Array]",Ox="[object Uint8ClampedArray]",_x="[object Uint16Array]",Px="[object Uint32Array]",et={};et[yx]=et[bx]=et[xx]=et[Cx]=et[Ex]=et[wx]=et[Ox]=et[_x]=et[Px]=!0;et[nx]=et[ax]=et[gx]=et[ix]=et[hx]=et[ox]=et[lx]=et[sx]=et[cx]=et[ux]=et[fx]=et[dx]=et[vx]=et[px]=et[mx]=!1;function Tx(e){return rx(e)&&tx(e.length)&&!!et[ex(e)]}var Sx=Tx;function Nx(e){return function(n){return e(n)}}var Ix=Nx,_a={exports:{}};_a.exports;(function(e,n){var t=hf,r=n&&!n.nodeType&&n,a=r&&!0&&e&&!e.nodeType&&e,i=a&&a.exports===r,s=i&&t.process,o=function(){try{var u=a&&a.require&&a.require("util").types;return u||s&&s.binding&&s.binding("util")}catch{}}();e.exports=o})(_a,_a.exports);var jx=_a.exports,Mx=Sx,Rx=Ix,Jc=jx,Qc=Jc&&Jc.isTypedArray,kx=Qc?Rx(Qc):Mx,kd=kx;function Ax(e,n){for(var t=-1,r=Array(e);++t<e;)r[t]=n(t);return r}var Dx=Ax,Lx=9007199254740991,zx=/^(?:0|[1-9]\d*)$/;function $x(e,n){var t=typeof e;return n=n??Lx,!!n&&(t=="number"||t!="symbol"&&zx.test(e))&&e>-1&&e%1==0&&e<n}var Vx=$x,Hx=Dx,Wx=K4,Fx=Qr,Bx=Rd,Kx=Vx,Gx=kd,Ux=Object.prototype,Zx=Ux.hasOwnProperty;function Xx(e,n){var t=Fx(e),r=!t&&Wx(e),a=!t&&!r&&Bx(e),i=!t&&!r&&!a&&Gx(e),s=t||r||a||i,o=s?Hx(e.length,String):[],u=o.length;for(var c in e)(n||Zx.call(e,c))&&!(s&&(c=="length"||a&&(c=="offset"||c=="parent")||i&&(c=="buffer"||c=="byteLength"||c=="byteOffset")||Kx(c,u)))&&o.push(c);return o}var Yx=Xx,mo=globalThis&&globalThis.__assign||function(){return mo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},mo.apply(this,arguments)},qx=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function Jx(e,n){var t,r=e.className,a=e.title,i=e.avatar,s=e.description,o=e.actionList,u=qx(e,["className","title","avatar","description","actionList"]),c=d.useContext(ye).getPrefixCls,f=c("card-meta"),v=K(f,r);return l.createElement("div",mo({},u,{ref:n,className:v}),a||s?l.createElement("div",{className:f+"-content"},a&&l.createElement("div",{className:f+"-title"},a),s&&l.createElement("div",{className:f+"-description"},s)):null,i||o?l.createElement("div",{className:K(f+"-footer ",(t={},t[f+"-footer-only-actions"]=!i,t))},i?l.createElement("div",{className:f+"-avatar"},i):null,o):null)}var Ad=l.forwardRef(Jx);Ad.displayName="CardMeta";const Dd=Ad;function Qx(e,n){var t,r=e.children,a=e.style,i=e.className,s=e.hoverable,o=d.useContext(ye).getPrefixCls,u=o("card-grid");return l.createElement("div",{ref:n,style:a,className:K(u,(t={},t[u+"-hoverable"]=s,t),i)},r)}var Ld=l.forwardRef(Qx);Ld.displayName="CardGrid";const zd=Ld;var eC=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},tC=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function rC(e){var n=d.useContext(ye).getPrefixCls,t=n("spin")+"-dot",r={width:e.size,height:e.size},a=e.size?parseInt(String(e.size)):0;return l.createElement("div",{className:t+"-list",style:{height:e.size,width:Ye(a)&&a>0?a*7:""}},tC([],eC(new Array(5)),!1).map(function(i,s){return l.createElement("div",{key:s,className:t,style:r})}))}var go=globalThis&&globalThis.__assign||function(){return go=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},go.apply(this,arguments)},nC=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},aC=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function iC(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=He(e,{},i==null?void 0:i.Spin),o=s.style,u=s.className,c=s.children,f=s.loading,v=s.size,m=s.icon,p=s.element,g=s.tip,h=s.dot,b=s.delay,y=s.block,C=y===void 0?!1:y,w=nC(s,["style","className","children","loading","size","icon","element","tip","dot","delay","block"]),E=aC(d.useState(b?!1:f),2),O=E[0],x=E[1],_=d.useCallback(Em(x,b),[b]),T=b?O:f,P=a("spin");d.useEffect(function(){return b&&_(f),function(){_&&_.cancel()}},[f]);var I=l.createElement("span",{className:P+"-icon"},m?l.cloneElement(m,{className:K(P.replace("-spin","-icon")+"-loading"),style:{fontSize:v}}):p||(h?l.createElement(rC,{size:v}):l.createElement(Yr,{style:{fontSize:v}})));return l.createElement("div",go({ref:n,className:K(P,(t={},t[P+"-block"]=C,t[P+"-loading"]=T,t[P+"-with-tip"]=g&&!c,t),u),style:o},w),sa(c)?l.createElement(l.Fragment,null,I,g?l.createElement("div",{className:P+"-tip"},g):null):l.createElement(l.Fragment,null,l.createElement("div",{className:P+"-children"},c),T&&l.createElement("div",{className:P+"-loading-layer",style:{fontSize:v}},l.createElement("span",{className:P+"-loading-layer-inner"},I,g?l.createElement("div",{className:P+"-tip"},g):null))))}var $d=l.forwardRef(iC);$d.displayName="Spin";const ho=$d;var yo=globalThis&&globalThis.__assign||function(){return yo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},yo.apply(this,arguments)},oC=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},lC={size:"default",bordered:!0};function sC(e,n){var t,r,a=d.useContext(ye),i=a.getPrefixCls,s=a.loadingElement,o=a.componentConfig,u=a.rtl,c=He(e,lC,o==null?void 0:o.Card),f=c.className,v=c.children,m=c.bordered,p=c.loading,g=c.hoverable,h=c.size,b=c.title,y=c.extra,C=c.cover,w=c.actions,E=c.headerStyle,O=c.bodyStyle,x=oC(c,["className","children","bordered","loading","hoverable","size","title","extra","cover","actions","headerStyle","bodyStyle"]),_=i("card"),T=w&&w.length?l.createElement("div",{className:_+"-actions"},l.createElement("div",{className:_+"-actions-right"},w.map(function(M,R){return l.createElement("span",{key:"action-"+R,className:_+"-actions-item"},M)}))):null,P=!1,I=!1,j=l.Children.map(v,function(M){if(M&&M.type){if(M.type===zd)P=!0;else if(M.type===Dd)return I=!0,l.cloneElement(M,{actionList:T})}return M});return l.createElement("div",yo({},x,{ref:n,className:K(_,_+"-size-"+h,(t={},t[_+"-loading"]=p,t[_+"-bordered"]=m,t[_+"-hoverable"]=g,t[_+"-contain-grid"]=P,t[_+"-rtl"]=u,t),f)}),b||y?l.createElement("div",{className:K(_+"-header",(r={},r[_+"-header-no-title"]=!b,r)),style:E},b&&l.createElement("div",{className:_+"-header-title"},b),y&&l.createElement("div",{className:_+"-header-extra"},y)):null,C?l.createElement("div",{className:_+"-cover"},C):null,l.createElement("div",{className:_+"-body",style:O},p?s||l.createElement(ho,null):j,I?null:T))}var cC=l.forwardRef(sC),ai=cC;ai.Meta=Dd;ai.Grid=zd;ai.displayName="Card";const rw=ai;function eu(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function tu(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?eu(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):eu(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function uC(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=tu(tu({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-up")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M39.6 30.557 24.043 15 8.487 30.557"}))}var $l=l.forwardRef(uC);$l.defaultProps={isIcon:!0};$l.displayName="IconUp";const fC=$l;var dC="__lodash_hash_undefined__";function vC(e){return this.__data__.set(e,dC),this}var pC=vC;function mC(e){return this.__data__.has(e)}var gC=mC,hC=yl,yC=pC,bC=gC;function Pa(e){var n=-1,t=e==null?0:e.length;for(this.__data__=new hC;++n<t;)this.add(e[n])}Pa.prototype.add=Pa.prototype.push=yC;Pa.prototype.has=bC;var xC=Pa;function CC(e,n){for(var t=-1,r=e==null?0:e.length;++t<r;)if(n(e[t],t,e))return!0;return!1}var EC=CC;function wC(e,n){return e.has(n)}var OC=wC,_C=xC,PC=EC,TC=OC,SC=1,NC=2;function IC(e,n,t,r,a,i){var s=t&SC,o=e.length,u=n.length;if(o!=u&&!(s&&u>o))return!1;var c=i.get(e),f=i.get(n);if(c&&f)return c==n&&f==e;var v=-1,m=!0,p=t&NC?new _C:void 0;for(i.set(e,n),i.set(n,e);++v<o;){var g=e[v],h=n[v];if(r)var b=s?r(h,g,v,n,e,i):r(g,h,v,e,n,i);if(b!==void 0){if(b)continue;m=!1;break}if(p){if(!PC(n,function(y,C){if(!TC(p,C)&&(g===y||a(g,y,t,r,i)))return p.push(C)})){m=!1;break}}else if(!(g===h||a(g,h,t,r,i))){m=!1;break}}return i.delete(e),i.delete(n),m}var Vd=IC;function jC(e){var n=-1,t=Array(e.size);return e.forEach(function(r,a){t[++n]=[a,r]}),t}var MC=jC;function RC(e){var n=-1,t=Array(e.size);return e.forEach(function(r){t[++n]=r}),t}var kC=RC,ru=Fa,nu=I4,AC=sd,DC=Vd,LC=MC,zC=kC,$C=1,VC=2,HC="[object Boolean]",WC="[object Date]",FC="[object Error]",BC="[object Map]",KC="[object Number]",GC="[object RegExp]",UC="[object Set]",ZC="[object String]",XC="[object Symbol]",YC="[object ArrayBuffer]",qC="[object DataView]",au=ru?ru.prototype:void 0,Si=au?au.valueOf:void 0;function JC(e,n,t,r,a,i,s){switch(t){case qC:if(e.byteLength!=n.byteLength||e.byteOffset!=n.byteOffset)return!1;e=e.buffer,n=n.buffer;case YC:return!(e.byteLength!=n.byteLength||!i(new nu(e),new nu(n)));case HC:case WC:case KC:return AC(+e,+n);case FC:return e.name==n.name&&e.message==n.message;case GC:case ZC:return e==n+"";case BC:var o=LC;case UC:var u=r&$C;if(o||(o=zC),e.size!=n.size&&!u)return!1;var c=s.get(e);if(c)return c==n;r|=VC,s.set(e,n);var f=DC(o(e),o(n),r,a,i,s);return s.delete(e),f;case XC:if(Si)return Si.call(e)==Si.call(n)}return!1}var QC=JC;function e3(e,n){for(var t=-1,r=n.length,a=e.length;++t<r;)e[a+t]=n[t];return e}var t3=e3,r3=t3,n3=Qr;function a3(e,n,t){var r=n(e);return n3(e)?r:r3(r,t(e))}var i3=a3;function o3(e,n){for(var t=-1,r=e==null?0:e.length,a=0,i=[];++t<r;){var s=e[t];n(s,t,e)&&(i[a++]=s)}return i}var l3=o3;function s3(){return[]}var c3=s3,u3=l3,f3=c3,d3=Object.prototype,v3=d3.propertyIsEnumerable,iu=Object.getOwnPropertySymbols,p3=iu?function(e){return e==null?[]:(e=Object(e),u3(iu(e),function(n){return v3.call(e,n)}))}:f3,m3=p3,g3=M4,h3=g3(Object.keys,Object),y3=h3,b3=A4,x3=y3,C3=Object.prototype,E3=C3.hasOwnProperty;function w3(e){if(!b3(e))return x3(e);var n=[];for(var t in Object(e))E3.call(e,t)&&t!="constructor"&&n.push(t);return n}var O3=w3,_3=Yx,P3=O3,T3=q4;function S3(e){return T3(e)?_3(e):P3(e)}var N3=S3,I3=i3,j3=m3,M3=N3;function R3(e){return I3(e,M3,j3)}var k3=R3,ou=k3,A3=1,D3=Object.prototype,L3=D3.hasOwnProperty;function z3(e,n,t,r,a,i){var s=t&A3,o=ou(e),u=o.length,c=ou(n),f=c.length;if(u!=f&&!s)return!1;for(var v=u;v--;){var m=o[v];if(!(s?m in n:L3.call(n,m)))return!1}var p=i.get(e),g=i.get(n);if(p&&g)return p==n&&g==e;var h=!0;i.set(e,n),i.set(n,e);for(var b=s;++v<u;){m=o[v];var y=e[m],C=n[m];if(r)var w=s?r(C,y,m,n,e,i):r(y,C,m,e,n,i);if(!(w===void 0?y===C||a(y,C,t,r,i):w)){h=!1;break}b||(b=m=="constructor")}if(h&&!b){var E=e.constructor,O=n.constructor;E!=O&&"constructor"in e&&"constructor"in n&&!(typeof E=="function"&&E instanceof E&&typeof O=="function"&&O instanceof O)&&(h=!1)}return i.delete(e),i.delete(n),h}var $3=z3,V3=en,H3=zt,W3=V3(H3,"DataView"),F3=W3,B3=en,K3=zt,G3=B3(K3,"Promise"),U3=G3,Z3=en,X3=zt,Y3=Z3(X3,"Set"),q3=Y3,J3=en,Q3=zt,e5=J3(Q3,"WeakMap"),t5=e5,bo=F3,xo=hl,Co=U3,Eo=q3,wo=t5,Hd=Wn,cn=ld,lu="[object Map]",r5="[object Object]",su="[object Promise]",cu="[object Set]",uu="[object WeakMap]",fu="[object DataView]",n5=cn(bo),a5=cn(xo),i5=cn(Co),o5=cn(Eo),l5=cn(wo),dr=Hd;(bo&&dr(new bo(new ArrayBuffer(1)))!=fu||xo&&dr(new xo)!=lu||Co&&dr(Co.resolve())!=su||Eo&&dr(new Eo)!=cu||wo&&dr(new wo)!=uu)&&(dr=function(e){var n=Hd(e),t=n==r5?e.constructor:void 0,r=t?cn(t):"";if(r)switch(r){case n5:return fu;case a5:return lu;case i5:return su;case o5:return cu;case l5:return uu}return n});var s5=dr,Ni=T4,c5=Vd,u5=QC,f5=$3,du=s5,vu=Qr,pu=Rd,d5=kd,v5=1,mu="[object Arguments]",gu="[object Array]",ea="[object Object]",p5=Object.prototype,hu=p5.hasOwnProperty;function m5(e,n,t,r,a,i){var s=vu(e),o=vu(n),u=s?gu:du(e),c=o?gu:du(n);u=u==mu?ea:u,c=c==mu?ea:c;var f=u==ea,v=c==ea,m=u==c;if(m&&pu(e)){if(!pu(n))return!1;s=!0,f=!1}if(m&&!f)return i||(i=new Ni),s||d5(e)?c5(e,n,t,r,a,i):u5(e,n,u,t,r,a,i);if(!(t&v5)){var p=f&&hu.call(e,"__wrapped__"),g=v&&hu.call(n,"__wrapped__");if(p||g){var h=p?e.value():e,b=g?n.value():n;return i||(i=new Ni),a(h,b,t,r,i)}}return m?(i||(i=new Ni),f5(e,n,t,r,a,i)):!1}var g5=m5,h5=g5,yu=Fn;function Wd(e,n,t,r,a){return e===n?!0:e==null||n==null||!yu(e)&&!yu(n)?e!==e&&n!==n:h5(e,n,t,r,Wd,a)}var y5=Wd,b5=y5;function x5(e,n,t){t=typeof t=="function"?t:void 0;var r=t?t(e,n):void 0;return r===void 0?b5(e,n,void 0,t):!!r}var C5=x5;const E5=Vn(C5);function Fd(e,n){n===void 0&&(n=[]);var t=d.useRef(!1);d.useEffect(function(){t.current?e():t.current=!0},n)}function bu(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function xu(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?bu(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):bu(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function w5(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=xu(xu({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-check")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M41.678 11.05 19.05 33.678 6.322 20.95"}))}var Vl=l.forwardRef(w5);Vl.defaultProps={isIcon:!0};Vl.displayName="IconCheck";const Oo=Vl;function Cu(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Eu(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Cu(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Cu(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function O5(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Eu(Eu({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-plus")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M5 24h38M24 5v38"}))}var Hl=l.forwardRef(O5);Hl.defaultProps={isIcon:!0};Hl.displayName="IconPlus";const _5=Hl;var _o=globalThis&&globalThis.__assign||function(){return _o=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},_o.apply(this,arguments)},P5=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},T5={type:"horizontal",orientation:"center"};function S5(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=He(e,T5,i==null?void 0:i.Divider),o=s.children,u=s.style,c=s.className,f=s.type,v=s.orientation,m=P5(s,["children","style","className","type","orientation"]),p=a("divider"),g=K(p,p+"-"+f,(t={},t[p+"-with-text"]=o,t[p+"-with-text-"+v]=o&&v,t),c);return l.createElement("div",_o({role:"separator",ref:n,className:g,style:u},m),o&&f==="horizontal"?l.createElement("span",{className:p+"-text "+p+"-text-"+v},o):null)}var Bd=d.forwardRef(S5);Bd.displayName="Divider";const nw=Bd;var Kd=d.createContext({}),Gd=d.createContext({}),Ud=d.createContext({}),Vr=globalThis&&globalThis.__assign||function(){return Vr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Vr.apply(this,arguments)},N5=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},I5={span:24};function wu(e){return typeof e=="string"&&/\d+[px|%|em|rem|]{1}/.test(e)?"0 0 "+e:e}function j5(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,I5,i==null?void 0:i["Grid.Col"]),u=d.useContext(Kd),c=u.gutter,f=u.div,v=o.className,m=o.style,p=o.children,g=o.span,h=o.offset,b=o.order,y=o.pull,C=o.push,w=o.xs,E=o.sm,O=o.md,x=o.lg,_=o.xl,T=o.xxl,P=o.xxxl,I=o.flex,j=N5(o,["className","style","children","span","offset","order","pull","push","xs","sm","md","lg","xl","xxl","xxxl","flex"]);function M(D,F){var H={xs:w,sm:E,md:O,lg:x,xl:_,xxl:T,xxxl:P};return Object.keys(H).forEach(function(z){var B=H[z];Ye(B)?B>=0&&(F[D+"-"+z+"-"+B]=!0):xe(B)&&(F[D+"-"+z+"-"+B.span]=B.span,F[D+"-"+z+"-offset-"+B.offset]=B.offset,F[D+"-"+z+"-order-"+B.order]=B.order,F[D+"-"+z+"-pull-"+B.pull]=B.pull,F[D+"-"+z+"-push-"+B.push]=B.push)}),F}var R=a("col"),k=(t={},t[""+R]=!f,t[R+"-order-"+b]=b,t[R+"-"+g]=!f&&!w&&!E&&!O&&!x&&!_&&!T&&!P,t[R+"-offset-"+h]=h,t[R+"-pull-"+y]=y,t[R+"-push-"+C]=C,t[R+"-rtl"]=s,t);k=M(R,k);var L=K(I?R:k,v),S={};if(Array.isArray(c)&&!f){var N=c[0]&&c[0]/2||0,A=c[1]&&c[1]/2||0;N&&(S.paddingLeft=N,S.paddingRight=N),A&&(S.paddingTop=A,S.paddingBottom=A)}var W=d.useMemo(function(){return wu(I)?{flex:wu(I)}:{}},[I]);return l.createElement("div",Vr({ref:n},j,{style:Vr(Vr(Vr({},m),S),W),className:L}),p)}var Zd=d.forwardRef(j5);Zd.displayName="Col";const Po=Zd;var jn=globalThis&&globalThis.__assign||function(){return jn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},jn.apply(this,arguments)},M5=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},R5=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},k5={gutter:0,align:"start",justify:"start"};function A5(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,k5,i==null?void 0:i["Grid.Row"]),u=o.className,c=o.style,f=o.children,v=o.div,m=o.align,p=o.justify,g=o.gutter,h=M5(o,["className","style","children","div","align","justify","gutter"]),b=R5(d.useState({xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0,xxxl:!0}),2),y=b[0],C=b[1],w=d.useRef();d.useEffect(function(){return w.current=Xr.subscribe(function(M){(!Array.isArray(g)&&typeof g=="object"||Array.isArray(g)&&(typeof g[0]=="object"||typeof g[1]=="object"))&&C(M)}),function(){Xr.unsubscribe(w.current)}},[]);function E(M){var R=0;if(typeof M=="object")for(var k=0;k<wa.length;k++){var L=wa[k];if(y[L]&&M[L]!==void 0){R=M[L];break}}else R=M;return R}var O=a("row"),x=K((t={},t[""+O]=!v,t[O+"-align-"+m]=m,t[O+"-justify-"+p]=p,t[O+"-rtl"]=s,t),u),_={},T=E(Array.isArray(g)?g[0]:g),P=E(Array.isArray(g)?g[1]:0);if((T||P)&&!v){var I=-T/2,j=-P/2;I&&(_.marginLeft=I,_.marginRight=I),j&&(_.marginTop=j,_.marginBottom=j)}return l.createElement("div",jn({ref:n},Ge(h,["gutter"]),{style:jn(jn({},c),_),className:x}),l.createElement(Kd.Provider,{value:{gutter:[T,P],div:v}},f))}var Xd=d.forwardRef(A5);Xd.displayName="Row";const To=Xd;var D5=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function Ou(e){return xe(e)}var Mn=function(e,n,t){t===void 0&&(t=!1);var r=d.useRef(),a=D5(d.useState({xs:!0,sm:!0,md:!0,lg:!0,xl:!0,xxl:!0,xxxl:!0}),2),i=a[0],s=a[1];d.useEffect(function(){return r.current=Xr.subscribe(function(u){Ou(e)&&s(u)}),function(){Xr.unsubscribe(r.current)}},[]);var o=d.useMemo(function(){var u=n;if(Ou(e))for(var c=0;c<wa.length;c++){var f=wa[c];if((i[f]||f==="xs"&&t)&&e[f]!==void 0){u=e[f];break}}else u=e;return u},[i,e,n,t]);return o};function L5(e,n){var t=n.span,r=n.offset,a=n.suffix,i=t??1,s=r??0,o=Math.min(s,e),u=Math.min(o>0?i+s:i,e);return{span:u,offset:o,suffix:a}}function z5(e){var n=e.cols,t=e.collapsed,r=e.collapsedRows,a=e.itemDataList,i=!1,s=[];function o(m){return Math.ceil(m/n)>r}if(t){for(var u=0,c=0;c<a.length;c++)a[c].suffix&&(u+=a[c].span,s.push(c));if(!o(u))for(var f=0;f<a.length;){var v=a[f];if(!v.suffix){if(u+=v.span,o(u))break;s.push(f)}f++}i=a.some(function(m,p){return!m.suffix&&!s.includes(p)})}else s=a.map(function(m,p){return p});return{overflow:i,displayIndexList:s}}var Rn=globalThis&&globalThis.__assign||function(){return Rn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Rn.apply(this,arguments)},ta=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},$5=globalThis&&globalThis.__values||function(e){var n=typeof Symbol=="function"&&Symbol.iterator,t=n&&e[n],r=0;if(t)return t.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(n?"Object is not iterable.":"Symbol.iterator is not defined.")},_u=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},V5={collapsed:!1,collapsedRows:1,cols:24,colGap:0,rowGap:0};function H5(e,n){var t,r=ta(d.useState(new Map),2),a=r[0],i=r[1],s=d.useContext(ye),o=s.getPrefixCls,u=s.componentConfig,c=s.rtl,f=He(e,V5,u==null?void 0:u.Grid),v=f.children,m=f.className,p=f.style,g=f.cols,h=f.colGap,b=f.rowGap,y=f.collapsed,C=f.collapsedRows,w=Mn(g,24),E=Mn(h,0),O=Mn(b,0),x={gap:O+"px "+E+"px",gridTemplateColumns:"repeat("+w+", minmax(0px, 1fr))"},_=o("grid"),T=(t={},t[""+_]=!0,t[_+"-rtl"]=c,t),P=K(T,m),I=function(){var R,k,L=[];try{for(var S=$5(a.entries()),N=S.next();!N.done;N=S.next()){var A=ta(N.value,2),W=A[0],D=A[1];L[W]=D}}catch(F){R={error:F}}finally{try{N&&!N.done&&(k=S.return)&&k.call(S)}finally{if(R)throw R.error}}return L},j=I(),M=z5({cols:w,collapsed:y,collapsedRows:C,itemDataList:j});return l.createElement("div",{ref:n,className:P,style:Rn(Rn({},x),p)},l.createElement(Ud.Provider,{value:{collectItemData:function(R,k){a.set(R,k),i(new Map(_u([],ta(a),!1)))},removeItemData:function(R){a.delete(R),i(new Map(_u([],ta(a),!1)))}}},l.createElement(Gd.Provider,{value:{cols:w,colGap:E,collapsed:y,overflow:M.overflow,displayIndexList:M.displayIndexList}},l.Children.map(v,function(R,k){if(R){var L=Rn({__index__:k},R.props);return l.cloneElement(R,L)}return null}).filter(function(R){return Rt(R,"type.__ARCO_GRID_ITEM__")}))))}var Yd=d.forwardRef(H5);Yd.displayName="Grid";const W5=Yd;var hr=globalThis&&globalThis.__assign||function(){return hr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},hr.apply(this,arguments)},F5={suffix:!1,offset:0,span:1};function B5(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,F5,i==null?void 0:i["Grid.GridItem"]),u=o.children,c=o.className,f=o.style,v=o.offset,m=o.span,p=o.__index__,g=d.useContext(Gd),h=d.useContext(Ud),b=h.collectItemData,y=h.removeItemData,C=g.colGap,w=g.cols,E=g.displayIndexList,O=g.overflow,x=Mn(v,0),_=Mn(m,1),T=a("grid-item"),P=E==null?void 0:E.includes(p),I=(t={},t[""+T]=!0,t[T+"-rtl"]=s,t),j=K(I,c),M=d.useMemo(function(){return L5(g.cols,{suffix:!!o.suffix,span:_,offset:x})},[g.cols,o.suffix,_,x]);d.useEffect(function(){return b(p,M),function(){y(p)}},[p,M]);var R=d.useMemo(function(){var N=M.offset,A=M.span;if(N>0){var W="(100% - "+C*(A-1)+"px) / "+A;return{marginLeft:"calc(("+W+" * "+N+") + "+C*N+"px)"}}return{}},[M,C]),k=d.useMemo(function(){var N=M.suffix,A=M.span;return N?""+(w-A+1):"span "+A},[M,w]),L=!P||_===0?{display:"none"}:{},S=hr(hr({gridColumn:k+" / span "+_},R),L);return l.createElement("div",{ref:n,className:j,style:hr(hr({},S),f)},Ke(u)?u({overflow:O}):l.Children.map(u,function(N){return N&&g.collapsed&&l.isValidElement(N)&&!ar(N.type)?l.cloneElement(N,hr({overflow:O},N.props)):N}))}var K5=d.forwardRef(B5),Wl=K5;Wl.displayName="GridItem";Wl.__ARCO_GRID_ITEM__=!0;const G5=Wl;var ii=W5;ii.Col=Po;ii.Row=To;ii.GridItem=G5;const aw=ii;var Br=globalThis&&globalThis.__assign||function(){return Br=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Br.apply(this,arguments)},U5=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},_t;(function(e){e[e.previous=0]="previous",e[e.next=1]="next"})(_t||(_t={}));function ra(e){var n,t=d.useContext(ye).locale,r=Er(),a=e.pageNum,i=e.current,s=e.rootPrefixCls,o=e.pageItemStyle,u=e.activePageItemStyle,c=e.itemRender,f=s+"-item",v=i===a,m=K(f,v?f+"-active":""),p=o;v&&(p=Br(Br({},p),u));var g=v?{"aria-current":!0}:{},h=function(b){var y=e.pageNum,C=e.onClick,w=e.disabled;b.currentTarget.dataset.active!=="true"&&(b.stopPropagation(),w||C&&C(y))};return l.createElement("li",Br({style:p,className:m,onClick:h,tabIndex:e.disabled?-1:0,"aria-label":(n=t.Pagination.currentPage)===null||n===void 0?void 0:n.replace("{0}",a)},r({onPressEnter:h}),g),c?c(a,"page",a):a)}function So(e,n){switch(e){case"prev":return n&&n.prev?n.prev:l.createElement(Zr,null);case"next":return n&&n.next?n.next:l.createElement(Ur,null);case"more":return n&&n.more?n.more:l.createElement(Cl,null);default:return null}}var Ii=function(e){var n,t,r=d.useContext(ye).locale,a=e.rootPrefixCls,i=e.current,s=e.allPages,o=e.jumpPage,u=e.icons,c=e.disabled,f=e.pageItemStyle,v=e.itemRender,m=s>0?1:0,p=Math.min(s,Math.max(m,i+o)),g=a+"-item "+a+"-item-jumper",h=K(g),b=function(){!c&&e.onClick&&e.onClick(p)},y=So("more",u),C=o>0?(n=r.Pagination.nextSomePages)===null||n===void 0?void 0:n.replace("{0}",o):(t=r.Pagination.prevSomePages)===null||t===void 0?void 0:t.replace("{0}",-o);return l.createElement("li",{style:f,className:h,onClick:b,"aria-label":C},v?v(void 0,"more",y):y)},na=function(e){var n,t=d.useContext(ye),r=t.locale,a=t.rtl,i=Er(),s=e.rootPrefixCls,o=e.current,u=e.allPages,c=e.type,f=e.icons,v=e.disabled,m=e.pageItemStyle,p=e.itemRender,g=s+"-item",h=U5(a?["next","prev"]:["prev","next"],2),b=h[0],y=h[1],C=c===_t.previous?So(b,f):So(y,f),w=!1;u===0?w=!0:c===_t.previous?w=o<=1:w=o===u;var E=v||w,O=o+(c===_t.previous?-1:1);O=Math.max(0,Math.min(u,O));var x=_t.previous===c?"prev":"next",_=K(g,g+"-"+x,(n={},n[g+"-disabled"]=E,n)),T=function(){E||e.onClick&&e.onClick(O)};return l.createElement("li",Br({style:m,className:_,onClick:T,tabIndex:E?-1:0,"aria-label":r.Pagination[x]},i({onPressEnter:T})),p?p(void 0,x,C):C)},No=globalThis&&globalThis.__assign||function(){return No=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},No.apply(this,arguments)},Z5=function(){},X5=gd.Option,Y5=[10,20,30,40,50];function q5(e){var n=d.useRef(),t=d.useContext(ye).locale,r=e.sizeCanChange,a=r===void 0?!1:r,i=e.onPageSizeChange,s=i===void 0?Z5:i,o=e.rootPrefixCls,u=e.sizeOptions,c=u===void 0?Y5:u,f=e.pageSize,v=f===void 0?10:f,m=e.size,p=e.selectProps,g=e.disabled;return a&&l.createElement("div",{ref:n,className:o+"-option","aria-label":t.Pagination.pageSize},l.createElement(gd,No({value:c.indexOf(v)!==-1?v:c[0],onChange:function(h){s(h)},size:m,getPopupContainer:function(){return n.current},disabled:g},p),c.map(function(h){return l.createElement(X5,{key:h,value:h},h+" "+t.Pagination.countPerPage)})))}var Io=globalThis&&globalThis.__assign||function(){return Io=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Io.apply(this,arguments)},J5=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function Pu(e){var n=e.simple?e.current:void 0,t=d.useContext(ye).locale,r=J5(d.useState(n),2),a=r[0],i=r[1],s=d.useRef();d.useEffect(function(){e.simple&&i(e.current)},[e.simple,e.current]);var o=function(y){var C=parseInt(y,10);i(isNaN(C)?void 0:C)},u=function(){var y=e.onPageChange,C=e.totalPages,w=e.current,E=e.simple;if(!ft(a)){if(a===w){E||i(void 0);return}var O=isNaN(Number(a))?w:Number(a);O<1?O=1:O>C&&(O=C),i(E?O:void 0),Ke(y)&&y(O)}},c=function(){var y=s.current.dom;String(a)&&y&&y.setSelectionRange(0,String(a).length)},f=e.rootPrefixCls,v=e.totalPages,m=e.simple,p=e.size,g=e.disabled,h=f+"-jumper",b=Io({showJumper:!0},xe(m)?m:{});return l.createElement("div",{className:""+h},!m&&l.createElement("span",{className:h+"-text-goto"},t.Pagination.goto),b.showJumper?l.createElement(Za,{_ignorePropsFromGlobal:!0,ref:function(y){return s.current=y},className:h+"-input",value:ft(a)?void 0:a.toString(),size:p,disabled:g||!v,onChange:o,onPressEnter:u,onFocus:c,onBlur:u}):l.createElement("span",null,a),!m&&l.createElement("span",{className:h+"-text-goto-suffix"},t.Pagination.page),m&&l.createElement(l.Fragment,null,l.createElement("span",{className:h+"-separator"},"/"),l.createElement("span",null,v)))}var yt=globalThis&&globalThis.__assign||function(){return yt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},yt.apply(this,arguments)},Tu=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Q5=1,qd=10;function ji(e,n){return e===void 0&&(e=qd),Math.ceil(n/e)}function eE(e,n){var t=0,r=Math.floor(n/2)-1,a=Math.max(e,t);return Math.min(a,r)}function Su(e){return e&&e.length?e[0]:qd}var tE={total:0,pageSizeChangeResetCurrent:!0,bufferSize:2};function rE(e,n){var t,r,a=d.useContext(ye),i=a.getPrefixCls,s=a.size,o=a.locale,u=a.componentConfig,c=a.rtl,f=He(e,tE,u==null?void 0:u.Pagination),v=f.total,m=f.pageSize,p=f.current,g=f.showMore,h=f.sizeOptions,b=f.pageSizeChangeResetCurrent,y=f.defaultCurrent,C=f.defaultPageSize,w=Tu(ot(Q5,{defaultValue:y,value:p}),2),E=w[0],O=w[1],x=Tu(ot(Su(h),{defaultValue:C,value:m}),2),_=x[0],T=x[1],P=v,I=!!g;p&&!f.onChange&&console.warn("Warning: you have provide current prop for pagination but without onChange handler , this will cause no-change when you change page. ");function j(Ce,Ne){var Ae=ji(Ce,Ne),De=E>Ae?Ae:E;return De}d.useEffect(function(){var Ce=h&&!h.includes(_);if(Ce){var Ne=Su(h);"pageSize"in f||T(Ne)}},[h]),d.useEffect(function(){var Ce=j(_,P);Ce!==E&&!("current"in f)&&O(Ce)},[P,E,_]);var M=function(Ce,Ne){Ce===void 0&&(Ce=E),Ne===void 0&&(Ne=_);var Ae=f.onChange;Ae&&Ae(Ce,Ne)},R=function(Ce){var Ne=f.onPageSizeChange,Ae=ji(Ce,P),De={pageSize:Ce};b?De.current=1:De.current=E>Ae?Ae:E,"pageSize"in f||T(De.pageSize),!("current"in f)&&E!==De.current&&O(De.current),Ne&&Ne(Ce,De.current),M(b?1:De.current,Ce)},k=function(Ce){"current"in f||O(Ce),M(Ce)},L=f.className,S=f.style,N=f.pageItemStyle,A=f.activePageItemStyle,W=f.showTotal,D=f.sizeCanChange,F=f.sizeOptions,H=f.simple,z=f.mini,B=f.showJumper,re=f.selectProps,V=f.icons,$=f.disabled,X=f.itemRender,Z=f.hideOnSinglePage,Y=f.size||s,J=i("pagination"),ae=z?"mini":Y,ve=K(J,J+"-size-"+ae,(t={},t[J+"-simple"]=H,t[J+"-disabled"]=$,t[J+"-rtl"]=c,t),L),ne,G=[],U=ji(_,P),oe=eE(f.bufferSize,U);if(Z&&U<=1)return null;var ie={onClick:k,rootPrefixCls:J,simple:H,current:E,allPages:U,icons:V,disabled:$,pageItemStyle:N,activePageItemStyle:A,itemRender:X};if(H){var te=J+"-item-simple";ne=l.createElement("ul",{className:J+"-list"},l.createElement(na,yt({key:"previous"},ie,{type:_t.previous})),l.createElement("li",{className:te+"-pager"},l.createElement(Pu,{disabled:$,rootPrefixCls:J,totalPages:U,current:E,onPageChange:k,simple:{showJumper:typeof B=="boolean"?B:!0},size:ae})),l.createElement(na,yt({key:"next"},ie,{type:_t.next})))}else{var pe=3+oe,Te=U-2-oe;if(U<=4+oe*2||E===pe&&E===Te)for(var Q=1;Q<=U;Q++)G.push(l.createElement(ra,yt({},ie,{key:Q,pageNum:Q})));else{var me=1,ee=U,de=!0,ce=!0;E>pe&&E<Te?(ee=E+oe,me=E-oe):E<=pe?(de=!1,me=1,ee=Math.max(pe,oe+E)):E>=Te&&(ce=!1,ee=U,me=Math.min(Te,E-oe));for(var Q=me;Q<=ee;Q++)G.push(l.createElement(ra,yt({key:Q,pageNum:Q},ie)));var ge=l.createElement(Ii,yt({},ie,{key:me-1,type:_t.previous,jumpPage:-(oe*2+1)})),fe=l.createElement(Ii,yt({},ie,{key:ee+1,type:_t.next,jumpPage:oe*2+1})),ue=l.createElement(ra,yt({key:1,pageNum:1},ie)),Pe=l.createElement(ra,yt({},ie,{key:U,pageNum:U}));de&&(G[0]=l.cloneElement(G[0],{className:J+"-item-after-pre"}),G.unshift(ge),G.unshift(ue)),ce&&(G[G.length-1]=l.cloneElement(G[G.length-1],{className:J+"-item-before-next"}),G.push(fe),G.push(Pe))}ne=l.createElement("ul",{className:J+"-list"},l.createElement(na,yt({},ie,{key:"previous",type:_t.previous})),G,I&&l.createElement(Ii,yt({},ie,{key:U+1,type:_t.next,jumpPage:oe*2+1})),l.createElement(na,yt({key:"next"},ie,{type:_t.next})))}var Oe=null;return typeof W=="boolean"&&W&&(Oe=l.createElement("div",{className:J+"-total-text"},(r=o.Pagination.total)===null||r===void 0?void 0:r.replace("{0}",P))),typeof W=="function"&&(Oe=l.createElement("div",{className:J+"-total-text"},W(P,[(E-1)*_+1,E*_]))),l.createElement("div",yt({},ir(f),{className:ve,style:S,ref:n}),Oe,ne,l.createElement(q5,{disabled:$,rootPrefixCls:J,sizeCanChange:D,sizeOptions:F,onPageSizeChange:R,pageSize:_,size:ae,selectProps:re}),!H&&B&&l.createElement(Pu,{disabled:$,rootPrefixCls:J,totalPages:U,current:E,onPageChange:k,size:ae}))}var Jd=d.forwardRef(rE);Jd.displayName="Pagination";const nE=Jd;var jo=globalThis&&globalThis.__assign||function(){return jo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},jo.apply(this,arguments)},aE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function iE(e,n){var t=d.useContext(ye).getPrefixCls,r=e.className,a=e.avatar,i=e.title,s=e.description,o=aE(e,["className","avatar","title","description"]),u=t("list"),c=u+"-item-meta",f=!!a,v=!!(i||s);return l.createElement("div",jo({ref:n},o,{className:K(c,r)}),f&&l.createElement("div",{className:c+"-avatar"},a),v&&l.createElement("div",{className:c+"-content"},i&&l.createElement("div",{className:c+"-title"},i),s&&l.createElement("div",{className:c+"-description"},s)))}var Qd=l.forwardRef(iE);Qd.displayName="ListItemMeta";const ev=Qd;var Mo=globalThis&&globalThis.__assign||function(){return Mo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Mo.apply(this,arguments)},oE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},lE={actionLayout:"horizontal"};function sE(e,n){var t=d.useContext(ye),r=t.getPrefixCls,a=t.componentConfig,i=He(e,lE,a&&a["List.Item"]),s=i.children,o=i.className,u=i.actions,c=i.extra,f=i.actionLayout,v=oE(i,["children","className","actions","extra","actionLayout"]),m=r("list"),p=m+"-item",g=[],h=[];l.Children.forEach(s,function(w){w&&w.type&&w.type===ev?g.push(w):h.push(w)});var b=h.length?l.createElement("div",{className:p+"-content"},h):null,y=c?l.createElement("div",{className:p+"-extra-content"},c):null,C=u&&u.length?l.createElement("div",{className:p+"-action"},u.map(function(w,E){return l.createElement("li",{key:p+"-action-"+E},w)})):null;return l.createElement("div",Mo({role:"listitem",ref:n,className:K(p,o)},v),l.createElement("div",{className:p+"-main"},g,b,f==="vertical"?C:null),f==="horizontal"?C:null,y)}var cE=l.forwardRef(sE),Fl=cE;Fl.displayName="ListItem";Fl.Meta=ev;const uE=Fl;var Ro=globalThis&&globalThis.__assign||function(){return Ro=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ro.apply(this,arguments)};function fE(e,n){if(e){Cf(e,Ro({block:"start",behavior:"auto",scrollMode:"if-needed"},n));var t=e.offsetHeight,r=e.getBoundingClientRect().height;if(n&&n.boundary&&t!==r){var a=Ke(n.boundary)?n.boundary(e):n.boundary;a.scrollTop=Math.round(a.scrollTop*(t/r))}}}var At=globalThis&&globalThis.__assign||function(){return At=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},At.apply(this,arguments)},dE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Mi=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Nu=10,Iu=1,vE=["small","default","large"],pE={split:!0,bordered:!0,defaultCurrent:1,offsetBottom:0,throttleDelay:500};function mE(e,n){var t=d.useContext(ye),r=t.getPrefixCls,a=t.loadingElement,i=t.size,s=t.renderEmpty,o=t.componentConfig,u=t.rtl,c=He(e,pE,o==null?void 0:o.List),f=c.style,v=c.wrapperStyle,m=c.className,p=c.wrapperClassName,g=c.children,h=g===void 0?[]:g,b=c.dataSource,y=b===void 0?[]:b,C=c.size,w=c.footer,E=c.header,O=c.pagination,x=c.bordered,_=c.split,T=c.render,P=c.grid,I=c.loading,j=c.hoverable,M=c.scrollLoading,R=c.paginationInFooter,k=c.offsetBottom,L=c.throttleDelay,S=c.defaultCurrent,N=c.noDataElement,A=c.listRef,W=c.onReachBottom,D=c.onListScroll,F=C||(vE.indexOf(i)>-1?i:"default"),H=r("list"),z=d.useRef(null),B=d.useRef(null),re=d.useRef(null),V=d.useRef(null),$=d.useRef(!0),X=Mi(d.useState(O&&typeof O=="object"&&(O.pageSize||O.defaultPageSize)||Nu),2),Z=X[0],Y=X[1],J=Mi(d.useState(O&&typeof O=="object"&&(O.current||O.defaultCurrent)||Iu),2),ae=J[0],ve=J[1],ne=Mi(d.useState(S),2),G=ne[0],U=ne[1],oe=l.Children.count(h);d.useImperativeHandle(A,function(){return{dom:z.current,scrollIntoView:function(ee,de){if(B.current)B.current.scrollTo({index:ee,options:de});else if(V.current){var ce=V.current.children[ee];ce&&fE(ce,At({boundary:re.current},de))}},getRootDOMNode:function(){return z.current}}});var ie=c.virtualListProps?c.virtualListProps:c.height?{height:c.height}:void 0,te=At(At({pageSize:Z,current:ae,total:y.length>0?y.length:oe},typeof O=="object"?O:{}),{onPageSizeChange:function(ee,de){Y(ee),O&&typeof O=="object"&&O.onPageSizeChange&&O.onPageSizeChange(ee,de)},onChange:function(ee,de){ve(ee),O&&typeof O=="object"&&O.onChange&&O.onChange(ee,de)}});te.current=Math.min(te.current,Math.ceil(te.total/te.pageSize));var pe=!!(D||W),Te=d.useCallback(ol(function(){if(D){D(re.current);return}if(re.current){var ee=re.current,de=ee.scrollTop,ce=ee.scrollHeight,ge=ee.clientHeight,fe=ce-(de+ge);Math.abs(fe)<k+1?$.current&&(U(G+1),W&&W(G+1),$.current=!1):$.current=!0}},L),[L,G,D,W]),Q=function(){var ee=function(ge){var fe=te.current,ue=te.pageSize,Pe=(fe-1)*ue;return O&&ge.length>Pe?ge.slice(Pe,Pe+ue):ge},de=function(ge,fe){var ue=ee(ge);return fe?ue.map(fe):ue},ce=function(ge,fe){var ue=ee(ge);if(P.column||P.span){for(var Pe=[],Oe=P.gutter,Ce=P.justify,Ne=P.align,Ae=P.column,De=dE(P,["gutter","justify","align","column"]),Fe=Ae||Math.floor(24/P.span),tt=De.span||Math.floor(24/Fe),Be=0,dt=function(){var rt=Be+Fe,he=~~(Be/Fe);Pe.push(l.createElement(To,{key:he,className:H+"-row",gutter:Oe,justify:Ce,align:Ne},ue.slice(Be,rt).map(function(Ie,qe){return l.createElement(Po,At({key:he+"_"+qe,className:H+"-row-col"},De,{span:tt}),fe?fe(Ie,Be+qe):Ie)}))),Be=rt};Be<ue.length;)dt();return Pe}return l.createElement(To,{className:H+"-row",gutter:P.gutter},ue.map(function(rt,he){return l.createElement(Po,At({className:H+"-row-col"},Ge(P,["gutter"]),{key:he}),fe?fe(rt,he):rt)}))};return y.length>0&&T?P?ce(y,T):de(y,T):oe>0?P?ce(h):de(h):M?null:N||s("List")},me=function(){var ee,de,ce=Q(),ge=ie&&ie.threshold!==null&&Array.isArray(ce),fe=O?l.createElement(nE,At({},te,{className:K(H+"-pagination",te&&te.className)})):null,ue=R?fe:null,Pe=R?null:fe,Oe=M!=null?l.createElement("div",{className:H+"-item "+H+"-scroll-loading"},M):null;return l.createElement("div",{ref:function(Ce){n=Ce,z.current=n},style:v,className:K(H+"-wrapper",(ee={},ee[H+"-wrapper-rtl"]=u,ee),p)},l.createElement("div",At({},ir(c),{style:f,className:K(H,H+"-"+F,(de={},de[H+"-no-border"]=!x,de[H+"-no-split"]=!_,de[H+"-hoverable"]=j,de[H+"-rtl"]=u,de),m),ref:function(Ce){ge||(re.current=Ce)},onScroll:!ge&&pe?Te:void 0}),E?l.createElement("div",{className:H+"-header"},E):null,ge?l.createElement(l.Fragment,null,l.createElement(id,At({role:"list",ref:function(Ce){Ce&&(B.current=Ce,re.current=Ce.dom)},className:H+"-content "+H+"-virtual",data:Oe?ce.concat(Oe):ce,isStaticItemHeight:!1,onScroll:pe?Te:void 0},ie),function(Ce){return Ce})):l.createElement("div",{role:"list",className:H+"-content",ref:V},ce,Oe),w||ue?l.createElement("div",{className:H+"-footer"},w,ue):null),Pe)};return"loading"in c?l.createElement(ho,{style:{display:"block"},loading:I,element:a||l.createElement(ho,null)},me()):me()}var Bl=l.forwardRef(mE);Bl.displayName="List";Bl.Item=uE;const iw=Bl;var zn=globalThis&&globalThis.__assign||function(){return zn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},zn.apply(this,arguments)},ju=function(e,n){if(xe(e)){var t=Object.keys(e).map(function(a){return e[a]+" "+a}).join(","),r=n?{backgroundSize:100*100/n+"%"}:{};return zn({backgroundImage:"linear-gradient(to right, "+t+")"},r)}return{backgroundColor:e}},gE={small:3,default:4,large:8};function hE(e){var n,t,r=e.type,a=e.prefixCls,i=e.buffer,s=e.percent,o=e.color,u=e.animation,c=e.bufferColor,f=e.formatText,v=e.trailColor,m=e.showText,p=m===void 0?!0:m,g=e.size,h=g===void 0?"default":g,b=e.status,y=b===void 0?"normal":b,C=e.strokeWidth||gE[h],w=a+"-"+r,E=C,O=y==="success"||y==="error"||s>=100,x=d.useCallback(function(){if(Ke(f))return f(s);switch(y){case"error":return l.createElement("span",null,s,"% ",l.createElement(Na,null));default:return s+"%"}},[f,s,y]);return l.createElement("div",{className:w+"-wrapper"},l.createElement("div",{className:w+"-outer",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":s,style:{height:E,backgroundColor:v}},i&&!O&&l.createElement("div",{className:w+"-inner-buffer",style:zn({width:(s>0?s+10:0)+"%"},ju(c))}),l.createElement("div",{className:K(w+"-inner",(n={},n[w+"-inner-animate"]=u,n)),style:zn({width:s+"%"},ju(o,s))})),p&&l.createElement("div",{className:K(w+"-text",(t={},t[w+"-text-with-icon"]=y,t))},x()))}function Mu(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Ru(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Mu(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Mu(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function yE(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Ru(Ru({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-exclamation")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M23 9H25V30H23z"}),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M23 9H25V30H23z"}),l.createElement("path",{d:"M23 37H25V39H23z"}),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M23 37H25V39H23z"}))}var Kl=l.forwardRef(yE);Kl.defaultProps={isIcon:!0};Kl.displayName="IconExclamation";const tv=Kl;var bE={mini:4,small:3,default:4,large:4},xE={mini:16,small:48,default:64,large:80},CE=function(e){var n=e.size,t=e.percent,r=t===void 0?0:t,a=e.prefixCls,i=e.showText,s=e.status,o=e.formatText,u=xe(e.color),c=e.width||xE[n],f=e.strokeWidth||(n==="mini"?c/2:bE[n]),v=(c-f)/2,m=Math.PI*2*v,p=c/2,g=a+"-circle",h=g+"-svg",b=d.useCallback(function(E){if(Ke(o))return o(r);switch(E){case"success":return l.createElement(Oo,null);case"error":return l.createElement(tv,null);default:return r+"%"}},[o,r]),y=an(a+"-linear-gradient-"),C=u?"url(#"+y+")":e.color,w=l.createElement("div",{className:g+"-wrapper",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":r,style:{width:c,height:c}},l.createElement("svg",{viewBox:"0 0 "+c+" "+c,className:""+h},u&&l.createElement("defs",null,l.createElement("linearGradient",{id:y,x1:"0",y1:"1",x2:"0",y2:"0"},Object.keys(e.color).sort().map(function(E){return l.createElement("stop",{offset:E,key:E,stopColor:e.color[E]})}))),l.createElement("circle",{className:g+"-mask",fill:"none",cx:p,cy:p,r:v,strokeWidth:e.pathStrokeWidth||(n==="mini"?f:Math.max(2,f-2)),style:{stroke:e.pathStrokeColor}}),l.createElement("circle",{className:g+"-path",fill:"none",cx:p,cy:p,r:v,strokeWidth:f,style:{stroke:C,strokeDasharray:m,strokeDashoffset:(r>100?100:1-r/100)*m}})),i&&n!=="mini"&&l.createElement("div",{className:g+"-text"},b(s)));return n==="mini"&&s==="success"&&e.type==="circle"&&(w=l.createElement("div",{className:g+"-wrapper",style:{width:c,height:c}},l.createElement(Oo,{style:{fontSize:c-2,color:C}}))),n==="mini"&&i?l.createElement(Cr,{content:l.createElement("div",{className:g+"-text"},b("normal"))},w):w};const ku=CE;var EE=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},wE=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))},OE=function(e){var n,t=e.prefixCls,r=e.percent,a=e.color,i=e.type,s=e.formatText,o=e.trailColor,u=e.showText,c=u===void 0?!0:u,f=e.size,v=f===void 0?"default":f,m=e.status,p=m===void 0?"normal":m,g=e.strokeWidth||(v==="small"?8:4),h=t+"-"+i,b=g,y=d.useCallback(function(){if(Ke(s))return s(r);switch(p){case"error":return l.createElement("span",null,r,"% ",l.createElement(Na,null));default:return r+"%"}},[s,r,p]);return l.createElement("div",{className:h+"-wrapper"},l.createElement("div",{className:h+"-outer",role:"progressbar","aria-valuemin":0,"aria-valuemax":100,"aria-valuenow":r,style:{height:b}},wE([],EE(new Array(e.steps)),!1).map(function(C,w){var E,O=r>100/e.steps*w;return l.createElement("div",{key:w,className:K(h+"-item",(E={},E[h+"-item-active"]=O,E)),style:{backgroundColor:O?a:o||""}})})),c&&l.createElement("div",{className:K(h+"-text",(n={},n[h+"-text-with-icon"]=p,n))},y()))};const _E=OE;var Bt=globalThis&&globalThis.__assign||function(){return Bt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Bt.apply(this,arguments)},PE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},TE={type:"line",showText:!0,percent:0,size:"default"};function SE(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,TE,i==null?void 0:i.Progress),u=o.className,c=o.style,f=o.size,v=o.width,m=o.strokeWidth,p=o.steps,g=o.percent,h=PE(o,["className","style","size","width","strokeWidth","steps","percent"]),b=p&&o.type!=="circle"?"steps":o.type,y=a("progress"),C="status"in o?o.status:g>=100?"success":"normal",w={width:v};return f==="mini"&&b==="line"&&(w.width=v||16,w.height=v||16),l.createElement("div",Bt({ref:n,className:K(y,y+"-"+b,y+"-"+f,(t={},t[y+"-is-"+C]=C!=="normal",t[y+"-rtl"]=s,t),u),style:Bt(Bt({},w),c)},Ge(h,["type","animation","status","color","trailColor","showText","formatText","buffer","bufferColor"])),b==="steps"&&l.createElement(_E,Bt({},o,{type:b,status:C,prefixCls:y})),b==="circle"&&l.createElement(ku,Bt({width:o.width},o,{pathStrokeColor:o.trailColor,status:C,prefixCls:y})),b==="line"&&(f==="mini"?l.createElement(ku,Bt({pathStrokeColor:o.trailColor},o,{pathStrokeWidth:m||4,width:v||16,strokeWidth:m||4,prefixCls:y,status:C})):l.createElement(hE,Bt({},o,{status:C,prefixCls:y}))))}var rv=d.forwardRef(SE);rv.displayName="Progress";const ow=rv;function Au(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Du(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Au(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Au(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function NE(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Du(Du({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-info")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M25 39H27V60H25z",transform:"rotate(180 25 39)"}),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M25 39H27V60H25z",transform:"rotate(180 25 39)"}),l.createElement("path",{d:"M25 11H27V13H25z",transform:"rotate(180 25 11)"}),l.createElement("path",{fill:"currentColor",stroke:"none",d:"M25 11H27V13H25z",transform:"rotate(180 25 11)"}))}var Gl=l.forwardRef(NE);Gl.defaultProps={isIcon:!0};Gl.displayName="IconInfo";const IE=Gl;function jE(){return l.createElement("svg",{width:"100%",height:"100%",viewBox:"0 0 213 213",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},l.createElement("g",{transform:"matrix(1,0,0,1,-1241.95,-445.62)"},l.createElement("g",null,l.createElement("g",{transform:"matrix(1,0,0,1,295.2,-87.3801)"},l.createElement("circle",{cx:"1053.23",cy:"639.477",r:"106.477",style:{fill:"rgb(235, 238, 246)"}})),l.createElement("g",{transform:"matrix(0.38223,0,0,0.38223,1126.12,238.549)"},l.createElement("g",{transform:"matrix(0.566536,0.327089,-1.28774,0.74348,763.4,317.171)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fillOpacity:.1}})),l.createElement("g",{transform:"matrix(0.29595,0.170867,-0.91077,0.525833,873.797,588.624)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fillOpacity:.1}})),l.createElement("g",{transform:"matrix(1,0,0,1,275,-15)"},l.createElement("path",{d:"M262.077,959.012L276.923,959.012L273.388,1004.01C273.388,1004.59 273.009,1005.16 272.25,1005.6C270.732,1006.48 268.268,1006.48 266.75,1005.6C265.991,1005.16 265.612,1004.59 265.612,1004.01L262.077,959.012Z",style:{fill:"rgb(196, 173, 142)"}}),l.createElement("g",{transform:"matrix(0.866025,-0.5,1,0.57735,0,-45)"},l.createElement("ellipse",{cx:"-848.416",cy:"1004.25",rx:"6.062",ry:"5.25",style:{fill:"rgb(255, 125, 0)"}}))),l.createElement("g",{transform:"matrix(1,0,0,1,183.952,-67.5665)"},l.createElement("path",{d:"M262.077,959.012L276.923,959.012L273.388,1004.01C273.388,1004.59 273.009,1005.16 272.25,1005.6C270.732,1006.48 268.268,1006.48 266.75,1005.6C265.991,1005.16 265.612,1004.59 265.612,1004.01L262.077,959.012Z",style:{fill:"rgb(196, 173, 142)"}}),l.createElement("g",{transform:"matrix(0.866025,-0.5,1,0.57735,0,-45)"},l.createElement("ellipse",{cx:"-848.416",cy:"1004.25",rx:"6.062",ry:"5.25",style:{fill:"rgb(255, 125, 0)"}}))),l.createElement("g",{transform:"matrix(1,0,0,1,414,-95.2517)"},l.createElement("path",{d:"M262.077,959.012L276.923,959.012L273.388,1004.01C273.388,1004.59 273.009,1005.16 272.25,1005.6C270.732,1006.48 268.268,1006.48 266.75,1005.6C265.991,1005.16 265.612,1004.59 265.612,1004.01L262.077,959.012Z",style:{fill:"rgb(196, 173, 142)"}}),l.createElement("g",{transform:"matrix(0.866025,-0.5,1,0.57735,0,-45)"},l.createElement("ellipse",{cx:"-848.416",cy:"1004.25",rx:"6.062",ry:"5.25",style:{fill:"rgb(255, 125, 0)"}}))),l.createElement("g",{transform:"matrix(1,0,0,1,322.952,-147.818)"},l.createElement("path",{d:"M262.077,959.012L276.923,959.012L273.388,1004.01C273.388,1004.59 273.009,1005.16 272.25,1005.6C270.732,1006.48 268.268,1006.48 266.75,1005.6C265.991,1005.16 265.612,1004.59 265.612,1004.01L262.077,959.012Z",style:{fill:"rgb(196, 173, 142)"}}),l.createElement("g",{transform:"matrix(0.866025,-0.5,1,0.57735,0,-45)"},l.createElement("ellipse",{cx:"-848.416",cy:"1004.25",rx:"6.062",ry:"5.25",style:{fill:"rgb(255, 125, 0)"}}))),l.createElement("g",null,l.createElement("g",{transform:"matrix(1.42334,-0.821763,1.11271,0.642426,-1439.64,459.621)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(1.40786,-0.812831,6.60237e-16,1.99081,-2052.17,-84.7286)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(1.26159,-0.728382,5.91642e-16,1.78397,-1774.67,11.2303)"},l.createElement("path",{d:"M1950.29,1194.38C1950.29,1193.37 1949.41,1192.54 1948.34,1192.54L1846.01,1192.54C1844.93,1192.54 1844.06,1193.37 1844.06,1194.38L1844.06,1282.7C1844.06,1283.72 1844.93,1284.54 1846.01,1284.54L1948.34,1284.54C1949.41,1284.54 1950.29,1283.72 1950.29,1282.7L1950.29,1194.38Z",style:{fill:"rgb(132, 97, 51)"}})),l.createElement("g",{transform:"matrix(1.2198,-0.704254,5.72043e-16,1.72488,-1697.6,37.2103)"},l.createElement("path",{d:"M1950.29,1194.38C1950.29,1193.37 1949.41,1192.54 1948.34,1192.54L1846.01,1192.54C1844.93,1192.54 1844.06,1193.37 1844.06,1194.38L1844.06,1282.7C1844.06,1283.72 1844.93,1284.54 1846.01,1284.54L1948.34,1284.54C1949.41,1284.54 1950.29,1283.72 1950.29,1282.7L1950.29,1194.38Z",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.707187,0.408295,9.06119e-17,1.54833,-733.949,683.612)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.64553,-0.950049,1.17482,0.678285,-1632.45,473.879)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(0.74666,0.431085,2.3583e-17,0.135259,-816.63,57.1397)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.64553,-0.950049,1.17482,0.678285,-1632.45,473.879)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(0.750082,0,0,0.750082,163.491,354.191)"},l.createElement("g",{transform:"matrix(1.75943,-1.01581,1.75879e-16,0.632893,-2721.54,1876.43)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.290956,-0.167984,2.90849e-17,0.104661,69.4195,919.311)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1944.73,1192.54 1937.88,1192.54L1856.47,1192.54C1849.62,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1849.62,1284.54 1856.47,1284.54L1937.88,1284.54C1944.73,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(132, 97, 51)"}})),l.createElement("g",{transform:"matrix(0.262716,-0.151679,8.27418e-18,0.0364999,121.496,970.53)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1948.14,1192.54 1945.5,1192.54L1848.85,1192.54C1846.2,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1846.2,1284.54 1848.85,1284.54L1945.5,1284.54C1948.14,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(246, 220, 185)"}})),l.createElement("g",{transform:"matrix(1.77877,-1.02697,0.0581765,0.0335882,-425.293,1228.27)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(0.0369741,0.021347,4.72735e-17,0.492225,456.143,919.985)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}}))),l.createElement("g",{transform:"matrix(0.750082,0,0,0.750082,163.491,309.191)"},l.createElement("g",{transform:"matrix(1.75943,-1.01581,1.75879e-16,0.632893,-2721.54,1876.43)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.290956,-0.167984,2.90849e-17,0.104661,69.4195,919.311)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1944.73,1192.54 1937.88,1192.54L1856.47,1192.54C1849.62,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1849.62,1284.54 1856.47,1284.54L1937.88,1284.54C1944.73,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(132, 97, 51)"}})),l.createElement("g",{transform:"matrix(0.262716,-0.151679,8.27418e-18,0.0364999,121.496,970.53)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1948.14,1192.54 1945.5,1192.54L1848.85,1192.54C1846.2,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1846.2,1284.54 1848.85,1284.54L1945.5,1284.54C1948.14,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(246, 220, 185)"}})),l.createElement("g",{transform:"matrix(1.77877,-1.02697,0.0581765,0.0335882,-425.293,1228.27)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(0.0369741,0.021347,4.72735e-17,0.492225,456.143,919.985)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}}))),l.createElement("g",{transform:"matrix(0.750082,0,0,0.750082,163.491,263.931)"},l.createElement("g",{transform:"matrix(1.75943,-1.01581,1.75879e-16,0.632893,-2721.54,1876.43)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.290956,-0.167984,2.90849e-17,0.104661,69.4195,919.311)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1944.73,1192.54 1937.88,1192.54L1856.47,1192.54C1849.62,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1849.62,1284.54 1856.47,1284.54L1937.88,1284.54C1944.73,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(132, 97, 51)"}})),l.createElement("g",{transform:"matrix(0.262716,-0.151679,8.27418e-18,0.0364999,121.496,970.53)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1948.14,1192.54 1945.5,1192.54L1848.85,1192.54C1846.2,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1846.2,1284.54 1848.85,1284.54L1945.5,1284.54C1948.14,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(246, 220, 185)"}})),l.createElement("g",{transform:"matrix(1.77877,-1.02697,0.0581765,0.0335882,-425.293,1228.27)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(0.0369741,0.021347,4.72735e-17,0.492225,456.143,919.985)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}}))),l.createElement("path",{d:"M555.753,832.474L555.753,921.408L630.693,878.141L630.693,789.207L555.753,832.474Z",style:{fillOpacity:.1}}),l.createElement("g",{transform:"matrix(0.750082,0,0,0.750082,236.431,272.852)"},l.createElement("g",{transform:"matrix(1.64553,-0.950049,1.14552,0.661368,-1606.78,467.933)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(1.54477,-0.891873,1.05847,0.611108,-1456.84,490.734)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(132, 97, 51)"}})),l.createElement("g",{transform:"matrix(1.27607,-0.736739,0.751435,0.433841,-970.952,617.519)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.62765,-0.939723,1.42156e-16,0.5,-2476.81,1893.62)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(1.62765,-0.939723,1.42156e-16,0.5,-2476.81,1893.62)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.728038,0.420333,3.52595e-17,0.377589,-790.978,151.274)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.75943,-1.01581,1.75879e-16,0.632893,-2726.83,1873.38)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",null,l.createElement("g",{transform:"matrix(1.75943,-1.01581,1.75879e-16,0.632893,-2721.54,1876.43)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.290956,-0.167984,2.90849e-17,0.104661,69.4195,919.311)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1944.73,1192.54 1937.88,1192.54L1856.47,1192.54C1849.62,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1849.62,1284.54 1856.47,1284.54L1937.88,1284.54C1944.73,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(132, 97, 51)"}})),l.createElement("g",{transform:"matrix(0.262716,-0.151679,8.27418e-18,0.0364999,121.496,970.53)"},l.createElement("path",{d:"M1950.29,1238.54C1950.29,1213.15 1948.14,1192.54 1945.5,1192.54L1848.85,1192.54C1846.2,1192.54 1844.06,1213.15 1844.06,1238.54C1844.06,1263.93 1846.2,1284.54 1848.85,1284.54L1945.5,1284.54C1948.14,1284.54 1950.29,1263.93 1950.29,1238.54Z",style:{fill:"rgb(246, 220, 185)"}})),l.createElement("g",{transform:"matrix(1.77877,-1.02697,0.0581765,0.0335882,-425.293,1228.27)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(0.0369741,0.021347,4.72735e-17,0.492225,456.143,919.985)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})))),l.createElement("g",{transform:"matrix(1.62765,-0.939723,4.80984e-17,0.173913,-2468.81,2307.87)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}}))),l.createElement("g",null,l.createElement("g",{transform:"matrix(0.479077,0.276595,-0.564376,0.325843,598.357,-129.986)"},l.createElement("path",{d:"M1776.14,1326C1776.14,1321.19 1772.15,1317.28 1767.24,1317.28L1684.37,1317.28C1679.46,1317.28 1675.47,1321.19 1675.47,1326L1675.47,1395.75C1675.47,1400.56 1679.46,1404.46 1684.37,1404.46L1767.24,1404.46C1772.15,1404.46 1776.14,1400.56 1776.14,1395.75L1776.14,1326Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(2.61622,0,0,2.61622,-2305.73,162.161)"},l.createElement("g",{transform:"matrix(1.09915,-0.634597,1.26919,0.73277,-299.167,-62.4615)"},l.createElement("ellipse",{cx:"412.719",cy:"770.575",rx:"6.303",ry:"5.459",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.238212,-0.137532,0.178659,0.103149,875.064,207.93)"},l.createElement("text",{x:"413.474px",y:"892.067px",style:{fontFamily:"NunitoSans-Bold, Nunito Sans",fontWeight:700,fontSize:41.569,fill:"white"}},"?"))))))))}function ME(){return l.createElement("svg",{viewBox:"0 0 213 213",height:"100%",width:"100%",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},l.createElement("g",{transform:"matrix(1,0,0,1,-871.485,-445.62)"},l.createElement("g",null,l.createElement("g",{transform:"matrix(1,0,0,1,-75.2684,-87.3801)"},l.createElement("circle",{cx:"1053.23",cy:"639.477",r:"106.477",style:{fill:"rgb(235, 238, 246)"}})),l.createElement("g",{transform:"matrix(1,0,0,1,246.523,295.575)"},l.createElement("g",{transform:"matrix(0.316667,0,0,0.316667,277.545,71.0298)"},l.createElement("g",{transform:"matrix(0.989011,-0.571006,1.14201,0.659341,-335.171,81.4498)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(0.164835,-0.0951676,1.14201,0.659341,116.224,-179.163)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(202, 174, 136)"}})),l.createElement("g",{transform:"matrix(0.978261,-0.564799,1.26804e-16,1.30435,-337.046,42.0327)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.267591,-0.154493,3.46856e-17,0.356787,992.686,475.823)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(102, 102, 102)"}})),l.createElement("g",{transform:"matrix(1.28257,-0.740494,1.23317e-16,1.7101,1501.14,624.071)"},l.createElement("g",{transform:"matrix(1,0,0,1,-6,-6)"},l.createElement("path",{d:"M2.25,10.5C2.25,10.5 1.5,10.5 1.5,9.75C1.5,9 2.25,6.75 6,6.75C9.75,6.75 10.5,9 10.5,9.75C10.5,10.5 9.75,10.5 9.75,10.5L2.25,10.5ZM6,6C7.234,6 8.25,4.984 8.25,3.75C8.25,2.516 7.234,1.5 6,1.5C4.766,1.5 3.75,2.516 3.75,3.75C3.75,4.984 4.766,6 6,6Z",style:{fill:"white"}}))),l.createElement("g",{transform:"matrix(0.725806,0.419045,1.75755e-17,1.01444,155.314,212.138)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.58977,-0.917857,1.15976e-16,2.2425,-1270.46,-614.379)"},l.createElement("rect",{x:"1748.87",y:"1226.67",width:"10.895",height:"13.378",style:{fill:"rgb(132, 97, 0)"}}))),l.createElement("g",{transform:"matrix(0.182997,0.105653,-0.494902,0.285732,814.161,66.3087)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fillOpacity:.1}})),l.createElement("g",{transform:"matrix(0.316667,0,0,0.316667,237.301,94.2647)"},l.createElement("g",{transform:"matrix(0.989011,-0.571006,1.14201,0.659341,-335.171,81.4498)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(0.164835,-0.0951676,1.14201,0.659341,116.224,-179.163)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(202, 174, 136)"}})),l.createElement("g",{transform:"matrix(0.978261,-0.564799,1.26804e-16,1.30435,-337.046,42.0327)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.267591,-0.154493,3.46856e-17,0.356787,992.686,475.823)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(102, 102, 102)"}})),l.createElement("g",{transform:"matrix(1.28257,-0.740494,1.23317e-16,1.7101,1501.14,624.071)"},l.createElement("g",{transform:"matrix(1,0,0,1,-6,-6)"},l.createElement("path",{d:"M2.25,10.5C2.25,10.5 1.5,10.5 1.5,9.75C1.5,9 2.25,6.75 6,6.75C9.75,6.75 10.5,9 10.5,9.75C10.5,10.5 9.75,10.5 9.75,10.5L2.25,10.5ZM6,6C7.234,6 8.25,4.984 8.25,3.75C8.25,2.516 7.234,1.5 6,1.5C4.766,1.5 3.75,2.516 3.75,3.75C3.75,4.984 4.766,6 6,6Z",style:{fill:"white"}}))),l.createElement("g",{transform:"matrix(0.725806,0.419045,1.75755e-17,1.01444,155.314,212.138)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.58977,-0.917857,1.15976e-16,2.2425,-1270.46,-614.379)"},l.createElement("rect",{x:"1748.87",y:"1226.67",width:"10.895",height:"13.378",style:{fill:"rgb(132, 97, 0)"}}))),l.createElement("g",{transform:"matrix(0.474953,0,0,0.474953,538.938,8.95289)"},l.createElement("g",{transform:"matrix(0.180615,0.104278,-0.973879,0.562269,790.347,286.159)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fillOpacity:.1}})),l.createElement("g",{transform:"matrix(0.473356,0,0,0.473356,294.481,129.741)"},l.createElement("g",null,l.createElement("g",{transform:"matrix(0.1761,-0.101671,1.73518e-16,1.22207,442.564,7.31508)"},l.createElement("rect",{x:"202.62",y:"575.419",width:"124.002",height:"259.402",style:{fill:"rgb(235, 235, 235)"}})),l.createElement("g",{transform:"matrix(0.0922781,0.0532768,2.03964e-16,2.20569,405.236,-248.842)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(34, 34, 34)"}})),l.createElement("g",{transform:"matrix(0.147541,-0.0851831,1.52371e-16,1.23446,454.294,-3.8127)"},l.createElement("rect",{x:"202.62",y:"575.419",width:"124.002",height:"259.402",style:{fill:"rgb(51, 51, 51)"}})),l.createElement("g",{transform:"matrix(0.0921286,0.0531905,-0.126106,0.0728076,474.688,603.724)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(102, 102, 102)"}})))),l.createElement("g",{transform:"matrix(0.473356,0,0,0.473356,192.621,188.549)"},l.createElement("g",null,l.createElement("g",{transform:"matrix(0.1761,-0.101671,1.73518e-16,1.22207,442.564,7.31508)"},l.createElement("rect",{x:"202.62",y:"575.419",width:"124.002",height:"259.402",style:{fill:"rgb(235, 235, 235)"}})),l.createElement("g",{transform:"matrix(0.0922781,0.0532768,2.03964e-16,2.20569,405.236,-248.842)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(34, 34, 34)"}})),l.createElement("g",{transform:"matrix(0.147541,-0.0851831,1.52371e-16,1.23446,454.294,-3.8127)"},l.createElement("rect",{x:"202.62",y:"575.419",width:"124.002",height:"259.402",style:{fill:"rgb(51, 51, 51)"}})),l.createElement("g",{transform:"matrix(0.0921286,0.0531905,-0.126106,0.0728076,474.688,603.724)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(102, 102, 102)"}})))),l.createElement("g",{transform:"matrix(0.668111,0,0,0.668111,-123.979,-49.2109)"},l.createElement("g",{transform:"matrix(0.0349225,0.0201625,1.81598e-17,0.220789,974.758,729.412)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(235, 235, 235)"}})),l.createElement("g",{transform:"matrix(1.1164,-0.644557,0,0.220789,42.5091,1294.14)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(235, 235, 235)"}})),l.createElement("g",{transform:"matrix(0.0349225,0.0201625,-1.52814,0.882275,1593.11,461.746)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(102, 102, 102)"}})),l.createElement("g",{transform:"matrix(1.1164,-0.644557,0,0.220789,49.4442,1298.14)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(51, 51, 51)"}})),l.createElement("g",{transform:"matrix(0.0349225,0.0201625,1.81598e-17,0.220789,753.056,857.412)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(34, 34, 34)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,898.874,529.479)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,930.12,511.44)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,961.365,493.4)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,992.61,475.361)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,1023.86,457.321)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,1056.25,438.617)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,1085.74,421.589)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}}))),l.createElement("g",{transform:"matrix(0.668111,0,0,0.668111,-123.979,-91.97)"},l.createElement("g",{transform:"matrix(0.0349225,0.0201625,1.81598e-17,0.220789,974.758,729.412)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(235, 235, 235)"}})),l.createElement("g",{transform:"matrix(1.1164,-0.644557,0,0.220789,42.5091,1294.14)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(235, 235, 235)"}})),l.createElement("g",{transform:"matrix(0.0349225,0.0201625,-1.52814,0.882275,1593.11,461.746)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(102, 102, 102)"}})),l.createElement("g",{transform:"matrix(1.1164,-0.644557,0,0.220789,49.4442,1298.14)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(51, 51, 51)"}})),l.createElement("g",{transform:"matrix(0.0349225,0.0201625,1.81598e-17,0.220789,753.056,857.412)"},l.createElement("rect",{x:"657.012",y:"404.643",width:"198.586",height:"145.08",style:{fill:"rgb(34, 34, 34)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,898.874,529.479)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,930.12,511.44)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,961.365,493.4)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,992.61,475.361)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,1023.86,457.321)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,1056.25,438.617)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}})),l.createElement("g",{transform:"matrix(0.142968,-0.0825428,-0.207261,0.478709,1085.74,421.589)"},l.createElement("rect",{x:"831",y:"1023.79",width:"89.214",height:"89.214",style:{fill:"rgb(255, 125, 0)"}}))),l.createElement("g",{transform:"matrix(0.701585,5.16096e-35,-5.16096e-35,0.701585,-546.219,-21.3487)"},l.createElement("g",{transform:"matrix(0.558202,-0.322278,0,0.882275,1033.27,615.815)"},l.createElement("path",{d:"M855.598,410.446C855.598,407.244 852.515,404.643 848.718,404.643L663.891,404.643C660.094,404.643 657.012,407.244 657.012,410.446L657.012,543.92C657.012,547.123 660.094,549.723 663.891,549.723L848.718,549.723C852.515,549.723 855.598,547.123 855.598,543.92L855.598,410.446Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.558202,-0.322278,0,0.882275,1035.25,616.977)"},l.createElement("path",{d:"M855.598,410.446C855.598,407.244 852.515,404.643 848.718,404.643L663.891,404.643C660.094,404.643 657.012,407.244 657.012,410.446L657.012,543.92C657.012,547.123 660.094,549.723 663.891,549.723L848.718,549.723C852.515,549.723 855.598,547.123 855.598,543.92L855.598,410.446Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(1,0,0,1,418.673,507.243)"},l.createElement("path",{d:"M1088.34,192.063C1089.79,191.209 1090.78,191.821 1090.78,191.821L1092.71,192.944C1092.71,192.944 1092.29,192.721 1091.7,192.763C1090.99,192.813 1090.34,193.215 1090.34,193.215C1090.34,193.215 1088.85,192.362 1088.34,192.063Z",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(1,0,0,1,235.984,-39.1315)"},l.createElement("path",{d:"M1164.02,805.247C1164.05,802.517 1165.64,799.379 1167.67,798.118L1169.67,799.272C1167.58,800.648 1166.09,803.702 1166.02,806.402L1164.02,805.247Z",style:{fill:"url(#_Linear1)"}})),l.createElement("g",{transform:"matrix(0.396683,0,0,0.396683,1000.22,516.921)"},l.createElement("path",{d:"M1011.2,933.14C1009.31,932.075 1008.05,929.696 1007.83,926.324L1012.87,929.235C1012.87,929.235 1012.96,930.191 1013.04,930.698C1013.16,931.427 1013.42,932.344 1013.62,932.845C1013.79,933.255 1014.59,935.155 1016.22,936.046C1015.83,935.781 1011.19,933.139 1011.19,933.139L1011.2,933.14Z",style:{fill:"rgb(238, 238, 238)"}})),l.createElement("g",{transform:"matrix(0.253614,-0.146424,4.87691e-17,0.338152,1209.98,830.02)"},l.createElement("circle",{cx:"975.681",cy:"316.681",r:"113.681",style:{fill:"rgb(245, 63, 63)"}}),l.createElement("g",{transform:"matrix(1.08844,0,0,0.61677,-99.9184,125.436)"},l.createElement("path",{d:"M1062,297.556C1062,296.697 1061.61,296 1061.12,296L915.882,296C915.395,296 915,296.697 915,297.556L915,333.356C915,334.215 915.395,334.912 915.882,334.912L1061.12,334.912C1061.61,334.912 1062,334.215 1062,333.356L1062,297.556Z",style:{fill:"white"}}))),l.createElement("g",{transform:"matrix(5.57947,-3.22131,0.306277,0.176829,-6260.71,4938.32)"},l.createElement("rect",{x:"1335.54",y:"694.688",width:"18.525",height:"6.511",style:{fill:"rgb(248, 248, 248)"}})),l.createElement("g",{transform:"matrix(0.10726,0.0619268,-1.83335e-14,18.1609,1256.76,-11932.8)"},l.createElement("rect",{x:"1335.54",y:"694.688",width:"18.525",height:"6.511",style:{fill:"rgb(238, 238, 238)"}})))),l.createElement("g",{transform:"matrix(0.316667,0,0,0.316667,269.139,37.8829)"},l.createElement("g",{transform:"matrix(0.989011,-0.571006,1.14201,0.659341,-335.171,81.4498)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(253, 243, 228)"}})),l.createElement("g",{transform:"matrix(0.164835,-0.0951676,1.14201,0.659341,116.224,-179.163)"},l.createElement("rect",{x:"495.52",y:"1057.87",width:"105.078",height:"91",style:{fill:"rgb(202, 174, 136)"}})),l.createElement("g",{transform:"matrix(0.978261,-0.564799,1.26804e-16,1.30435,-337.046,42.0327)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(196, 173, 142)"}})),l.createElement("g",{transform:"matrix(0.267591,-0.154493,3.46856e-17,0.356787,992.686,475.823)"},l.createElement("rect",{x:"1844.06",y:"1192.54",width:"106.232",height:"92",style:{fill:"rgb(102, 102, 102)"}})),l.createElement("g",{transform:"matrix(1.28257,-0.740494,1.23317e-16,1.7101,1501.14,624.071)"},l.createElement("g",{transform:"matrix(1,0,0,1,-6,-6)"},l.createElement("path",{d:"M2.25,10.5C2.25,10.5 1.5,10.5 1.5,9.75C1.5,9 2.25,6.75 6,6.75C9.75,6.75 10.5,9 10.5,9.75C10.5,10.5 9.75,10.5 9.75,10.5L2.25,10.5ZM6,6C7.234,6 8.25,4.984 8.25,3.75C8.25,2.516 7.234,1.5 6,1.5C4.766,1.5 3.75,2.516 3.75,3.75C3.75,4.984 4.766,6 6,6Z",style:{fill:"white"}}))),l.createElement("g",{transform:"matrix(0.725806,0.419045,1.75755e-17,1.01444,155.314,212.138)"},l.createElement("rect",{x:"1663.92",y:"-407.511",width:"143.183",height:"118.292",style:{fill:"rgb(240, 218, 183)"}})),l.createElement("g",{transform:"matrix(1.58977,-0.917857,1.15976e-16,2.2425,-1270.46,-614.379)"},l.createElement("rect",{x:"1748.87",y:"1226.67",width:"10.895",height:"13.378",style:{fill:"rgb(132, 97, 0)"}})))))),l.createElement("defs",null,l.createElement("linearGradient",{id:"_Linear1",x1:"0",y1:"0",x2:"1",y2:"0",gradientUnits:"userSpaceOnUse",gradientTransform:"matrix(-2.64571,4.04098,-4.04098,-2.64571,1167.67,799.269)"},l.createElement("stop",{offset:"0",style:{stopColor:"rgb(248, 248, 248)",stopOpacity:1}}),l.createElement("stop",{offset:"1",style:{stopColor:"rgb(248, 248, 248)",stopOpacity:1}}))))}function RE(){return l.createElement("svg",{width:"100%",height:"100%",viewBox:"0 0 213 213",style:{fillRule:"evenodd",clipRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:2}},l.createElement("g",{transform:"matrix(1,0,0,1,-483.054,-445.448)"},l.createElement("g",null,l.createElement("g",{transform:"matrix(1,0,0,1,-463.699,-87.5516)"},l.createElement("circle",{cx:"1053.23",cy:"639.477",r:"106.477",style:{fill:"rgb(235, 238, 246)"}})),l.createElement("g",{transform:"matrix(0.384532,-0.222009,0.444019,0.256354,-0.569781,260.021)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fillOpacity:.1}})),l.createElement("g",{transform:"matrix(0.384532,-0.222009,0.444019,0.256354,-0.569781,218.845)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.361496,-0.20871,0.41742,0.240997,34.7805,238.807)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(0, 85, 255)"}})),l.createElement("g",{transform:"matrix(0.341853,-0.197369,0.394738,0.227902,64.9247,257.804)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(29, 105, 255)"}})),l.createElement("g",{transform:"matrix(0.428916,0,0,0.428916,19.0588,329.956)"},l.createElement("clipPath",{id:"_clip1"},l.createElement("path",{d:"M1461.07,528.445C1461.07,530.876 1459.6,533.196 1456.6,534.928L1342.04,601.072C1335.41,604.896 1323.83,604.415 1316.18,600L1205.33,536C1201.14,533.585 1199,530.489 1199,527.555L1199,559.555C1199,562.489 1201.14,565.585 1205.33,568L1316.18,632C1323.83,636.415 1335.41,636.896 1342.04,633.072L1456.6,566.928C1459.6,565.196 1461.07,562.876 1461.07,560.445L1461.07,528.445Z"})),l.createElement("g",{clipPath:"url(#_clip1)"},l.createElement("g",{transform:"matrix(2.33146,-0,-0,2.33146,1081.79,269.266)"},l.createElement("use",{href:"#_Image2",x:"50.54",y:"112.301",width:"112.406px",height:"46.365px",transform:"matrix(0.99474,0,0,0.98649,0,0)"})))),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,265.448)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,268.45)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,271.452)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.360289,-0.208013,-4.39887e-18,0.576941,37.5847,124.262)"},l.createElement("rect",{x:"1621.2",y:"1370.57",width:"57.735",height:"5.947",style:{fill:"rgb(106, 161, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,307.505,420.796)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,310.507,419.062)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,313.509,417.329)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,316.512,415.595)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,319.514,413.862)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.384532,-0.222009,0.444019,0.256354,-0.569781,196.542)"},l.createElement("clipPath",{id:"_clip3"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z"})),l.createElement("g",{clipPath:"url(#_clip3)"},l.createElement("g",{transform:"matrix(1.30028,1.12608,-2.25216,1.95042,68.2716,1030.07)"},l.createElement("use",{href:"#_Image4",x:"50.54",y:"56.312",width:"112.406px",height:"64.897px",transform:"matrix(0.99474,0,0,0.998422,0,0)"})))),l.createElement("g",{transform:"matrix(0.361496,-0.20871,0.41742,0.240997,34.7805,216.764)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(0, 85, 255)"}})),l.createElement("g",{transform:"matrix(0.341853,-0.197369,0.394738,0.227902,64.9247,235.762)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(29, 105, 255)"}})),l.createElement("g",{transform:"matrix(0.428916,0,0,0.428916,19.0588,307.652)"},l.createElement("clipPath",{id:"_clip5"},l.createElement("path",{d:"M1461.07,528.445C1461.07,530.876 1459.6,533.196 1456.6,534.928L1342.04,601.072C1335.41,604.896 1323.83,604.415 1316.18,600L1205.33,536C1201.14,533.585 1199,530.489 1199,527.555L1199,559.555C1199,562.489 1201.14,565.585 1205.33,568L1316.18,632C1323.83,636.415 1335.41,636.896 1342.04,633.072L1456.6,566.928C1459.6,565.196 1461.07,562.876 1461.07,560.445L1461.07,528.445Z"})),l.createElement("g",{clipPath:"url(#_clip5)"},l.createElement("g",{transform:"matrix(2.33146,-0,-0,2.33146,1081.79,321.266)"},l.createElement("use",{href:"#_Image2",x:"50.54",y:"89.692",width:"112.406px",height:"46.365px",transform:"matrix(0.99474,0,0,0.98649,0,0)"})))),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,243.144)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,246.146)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,249.149)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.360289,-0.208013,-4.39887e-18,0.576941,37.5847,101.958)"},l.createElement("rect",{x:"1621.2",y:"1370.57",width:"57.735",height:"5.947",style:{fill:"rgb(106, 161, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,307.505,398.492)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,310.507,396.759)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,313.509,395.025)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,316.512,393.292)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,319.514,391.558)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.384532,-0.222009,0.444019,0.256354,-0.569781,171.832)"},l.createElement("clipPath",{id:"_clip6"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z"})),l.createElement("g",{clipPath:"url(#_clip6)"},l.createElement("g",{transform:"matrix(1.30028,1.12608,-2.25216,1.95042,12.6215,1078.27)"},l.createElement("use",{href:"#_Image7",x:"50.54",y:"31.563",width:"112.406px",height:"64.897px",transform:"matrix(0.99474,0,0,0.998422,0,0)"})))),l.createElement("g",{transform:"matrix(0.361496,-0.20871,0.41742,0.240997,34.7805,192.055)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(0, 85, 255)"}})),l.createElement("g",{transform:"matrix(0.341853,-0.197369,0.394738,0.227902,64.9247,211.052)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"rgb(29, 105, 255)"}})),l.createElement("g",{transform:"matrix(0.428916,0,0,0.428916,19.0588,282.943)"},l.createElement("clipPath",{id:"_clip8"},l.createElement("path",{d:"M1461.07,528.445C1461.07,530.876 1459.6,533.196 1456.6,534.928L1342.04,601.072C1335.41,604.896 1323.83,604.415 1316.18,600L1205.33,536C1201.14,533.585 1199,530.489 1199,527.555L1199,559.555C1199,562.489 1201.14,565.585 1205.33,568L1316.18,632C1323.83,636.415 1335.41,636.896 1342.04,633.072L1456.6,566.928C1459.6,565.196 1461.07,562.876 1461.07,560.445L1461.07,528.445Z"})),l.createElement("g",{clipPath:"url(#_clip8)"},l.createElement("g",{transform:"matrix(2.33146,-0,-0,2.33146,1081.79,378.876)"},l.createElement("use",{href:"#_Image2",x:"50.54",y:"64.644",width:"112.406px",height:"46.365px",transform:"matrix(0.99474,0,0,0.98649,0,0)"})))),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,218.434)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,221.437)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.347769,0.200785,3.44852e-18,0.545466,52.0929,224.439)"},l.createElement("path",{d:"M1480.33,34.813C1480.33,34.162 1479.7,33.634 1478.94,33.634L1396.27,33.634C1395.5,33.634 1394.88,34.162 1394.88,34.813C1394.88,35.464 1395.5,35.993 1396.27,35.993L1478.94,35.993C1479.7,35.993 1480.33,35.464 1480.33,34.813Z",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.360289,-0.208013,-4.39887e-18,0.576941,37.5847,77.2484)"},l.createElement("rect",{x:"1621.2",y:"1370.57",width:"57.735",height:"5.947",style:{fill:"rgb(106, 161, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,307.505,373.782)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"white"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,310.507,372.049)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,313.509,370.316)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,316.512,368.582)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.185726,-0.107229,-1.84168e-18,0.247635,319.514,366.849)"},l.createElement("ellipse",{cx:"1566.31",cy:"1372.3",rx:"4",ry:"3.464",style:{fill:"rgb(64, 128, 255)"}})),l.createElement("g",{transform:"matrix(0.365442,-0.210988,0.421976,0.243628,28.7259,185.45)"},l.createElement("clipPath",{id:"_clip9"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z"})),l.createElement("g",{clipPath:"url(#_clip9)"},l.createElement("g",{transform:"matrix(1.36821,1.1849,-2.36981,2.05231,5.46929,1071.93)"},l.createElement("use",{href:"#_Image10",x:"53.151",y:"30.14",width:"106.825px",height:"61.676px",transform:"matrix(0.998367,0,0,0.994768,0,0)"})))),l.createElement("g",{transform:"matrix(0.365442,-0.210988,0.421976,0.243628,28.7259,183.729)"},l.createElement("path",{d:"M84.299,1269.38C84.299,1261.99 78.301,1256 70.913,1256L-56.874,1256C-64.261,1256 -70.259,1261.99 -70.259,1269.38L-70.259,1376.46C-70.259,1383.85 -64.261,1389.85 -56.874,1389.85L70.913,1389.85C78.301,1389.85 84.299,1383.85 84.299,1376.46L84.299,1269.38Z",style:{fill:"url(#_Linear11)"}})),l.createElement("g",{transform:"matrix(0.407622,0,0,0.407622,47.38,278)"},l.createElement("clipPath",{id:"_clip12"},l.createElement("path",{d:"M1461.07,554.317C1461.07,556.747 1459.6,559.067 1456.6,560.8L1342.04,626.943C1335.41,630.767 1323.83,630.287 1316.18,625.871L1205.33,561.871C1201.14,559.456 1199,556.361 1199,553.426L1199,559.555C1199,562.489 1201.14,565.585 1205.33,568L1316.18,632C1323.83,636.415 1335.41,636.896 1342.04,633.072L1456.6,566.928C1459.6,565.196 1461.07,562.876 1461.07,560.445L1461.07,554.317Z"})),l.createElement("g",{clipPath:"url(#_clip12)"},l.createElement("g",{transform:"matrix(2.45325,-0,-0,2.45325,1068.82,410.793)"},l.createElement("use",{href:"#_Image13",x:"53.151",y:"58.978",width:"106.825px",height:"33.517px",transform:"matrix(0.998367,0,0,0.985808,0,0)"})))),l.createElement("g",{transform:"matrix(0.371452,-0.214458,2.38096e-17,0.495269,-19.3677,248.256)"},l.createElement("clipPath",{id:"_clip14"},l.createElement("path",{d:"M1776.14,1326C1776.14,1321.19 1772.23,1317.28 1767.42,1317.28L1684.19,1317.28C1679.38,1317.28 1675.47,1321.19 1675.47,1326L1675.47,1395.75C1675.47,1400.56 1679.38,1404.46 1684.19,1404.46L1767.42,1404.46C1772.23,1404.46 1776.14,1400.56 1776.14,1395.75L1776.14,1326Z"})),l.createElement("g",{clipPath:"url(#_clip14)"},l.createElement("g",{transform:"matrix(2.69214,1.16573,-1.29422e-16,2.0191,1352.59,983.841)"},l.createElement("use",{href:"#_Image15",x:"121.882",y:"76.034",width:"37.393px",height:"61.803px",transform:"matrix(0.984021,0,0,0.996825,0,0)"})))),l.createElement("g",{transform:"matrix(0.371452,-0.214458,2.38096e-17,0.495269,-15.0786,249.972)"},l.createElement("path",{d:"M1776.14,1326C1776.14,1321.19 1772.23,1317.28 1767.42,1317.28L1684.19,1317.28C1679.38,1317.28 1675.47,1321.19 1675.47,1326L1675.47,1395.75C1675.47,1400.56 1679.38,1404.46 1684.19,1404.46L1767.42,1404.46C1772.23,1404.46 1776.14,1400.56 1776.14,1395.75L1776.14,1326Z",style:{fill:"white",stopOpacity:.9}})),l.createElement("g",{transform:"matrix(0.220199,-0.127132,1.41145e-17,0.293599,339.708,327.53)"},l.createElement("path",{d:"M1306.5,1286.73C1307.09,1285.72 1308.6,1285.48 1310.36,1286.12C1312.13,1286.76 1313.84,1288.16 1314.73,1289.7C1326.44,1309.98 1355.4,1360.15 1363.73,1374.57C1364.33,1375.61 1364.49,1376.61 1364.18,1377.35C1363.87,1378.09 1363.11,1378.5 1362.07,1378.5C1346.41,1378.5 1288.17,1378.5 1264.07,1378.5C1262.42,1378.5 1260.37,1377.48 1258.9,1375.94C1257.44,1374.41 1256.88,1372.67 1257.5,1371.6C1268.1,1353.25 1296.8,1303.53 1306.5,1286.73Z",style:{fill:"rgb(245, 63, 63) ;fill-opacity:0.9"}})),l.createElement("g",{transform:"matrix(0.254264,-0.1468,1.22235e-17,0.254264,329.57,364.144)"},l.createElement("text",{x:"1170.88px",y:"1451.42px",style:{fontFamily:"NunitoSans-Bold, Nunito Sans",fontWeight:700,fontSize:41.569,fill:"white",fillOpacity:.9}},"!")))),l.createElement("defs",null,l.createElement("image",{id:"_Image2",width:"113px",height:"47px",href:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAHEAAAAvCAYAAADU+iVXAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABVUlEQVR4nO2aQRKCMAxFxUN4O+9/DNw4CoiTliZN8vPfQlm00ykvP3aQ5fFc11sjy/L+/nx8r3ffm7Fn845jz+aJa23XOJvfs9Zh7NBawv3YrSGtdbj+x10egkFzpRrNt+SSxMgbqkiZJCJDiQDoSmSfdYFJ3JD18GMmcXhDTHUzNZIIXhA1JIJDib0MptqiKbhKzHqQiAaT6IlSFVIiAJQIACUGpLfLhpfIw49Ml8T2v4/JTPySyIJQI3w7JTIYEp2fong3FXWJ3huqCEYSNUlYhZRoyaSCoEQAKHESlqF0kZj9NBgNJhEASgSAEgNx9WfCTmLxpygzYRIBmCORsTIlXxJZED/kk0h+KC1x9E2FKG86qEkMsh8/HG9A6SSGYqAIKDEinUIpUSDDYXiqxAw3JCNMIgDXJTIWYdBJIvukK2ynARit4XASUZ6izCScRFWKCH0BfLM84oTw1Z8AAAAASUVORK5CYII="}),l.createElement("image",{id:"_Image4",width:"113px",height:"65px",href:"data:image/png;base64,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"}),l.createElement("image",{id:"_Image7",width:"113px",height:"65px",href:"data:image/png;base64,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"}),l.createElement("image",{id:"_Image10",width:"107px",height:"62px",href:"data:image/png;base64,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"}),l.createElement("linearGradient",{id:"_Linear11",x1:"0",y1:"0",x2:"1",y2:"0",gradientUnits:"userSpaceOnUse",gradientTransform:"matrix(-118.47,-106.79,210.785,-180.125,69.2121,1372.7)"},l.createElement("stop",{offset:"0",style:{stopColor:"rgb(64, 128, 255)",stopOpacity:1}}),l.createElement("stop",{offset:"1",style:{stopColor:"rgb(64, 128, 255)",stopOpacity:1}})),l.createElement("image",{id:"_Image13",width:"107px",height:"34px",href:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAGsAAAAiCAYAAABY6CeoAAAACXBIWXMAAA7EAAAOxAGVKw4bAAABFElEQVRoge2aQRKDMAhFmx6it/P+x7Ab64xOmaAG8vnwFnWhiOGFOG3TPsu6vpS0djpuH61zXoz5F3s6r4rRxipiftddeUbp3t18QozEu3/JfdSzgCy5VWpTWcVYSlaPqcvDEUpZQPUdCqUsSAbMoJIViJIVCBNZrO+MHtbjrs4KRMkKBJUs9uXXTxZ7JR2g6ix27sly6BIxReIOHdpZWevoNe68y2DAmTVXFmDBAB9pJ29nBYRCln5jgkVyv1QUsrIAJyvtvg1F7iGykF/KlniPG66zKDCyWLI2IqwOJSsQz2URbqZEpTorEDCypn6xnciVYT+SlbS+08Zt01lJfv7xBmYZLPpgy6p/pA9gyxIArKMLXxexLNiBCThLAAAAAElFTkSuQmCC"}),l.createElement("image",{id:"_Image15",width:"38px",height:"62px",href:"data:image/png;base64,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"})))}var ko=globalThis&&globalThis.__assign||function(){return ko=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},ko.apply(this,arguments)},kE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},AE={success:l.createElement(Oo,null),info:l.createElement(IE,null),warning:l.createElement(tv,null),error:l.createElement(Dt,null),404:l.createElement(jE,null),403:l.createElement(ME,null),500:l.createElement(RE,null)},DE={status:"info"};function LE(e,n){var t,r,a=d.useContext(ye),i=a.getPrefixCls,s=a.componentConfig,o=a.rtl,u=He(e,DE,s==null?void 0:s.Result),c=u.className,f=u.style,v=u.status,m=u.title,p=u.subTitle,g=u.extra,h=u.children,b=u.icon,y=kE(u,["className","style","status","title","subTitle","extra","children","icon"]),C=i("result"),w="icon"in u?b:AE[v];return l.createElement("div",ko({ref:n,className:K(C,(t={},t[C+"-is-"+v]=v,t[C+"-rtl"]=o,t),c),style:f},y),w&&l.createElement("div",{className:C+"-icon"},l.createElement("span",{className:K(C+"-icon-tip",(r={},r[C+"-icon-"+v]=v,r[C+"-icon-custom"]=v===null,r))},w)),m&&l.createElement("div",{className:C+"-title"},m),p&&l.createElement("div",{className:C+"-subtitle"},p),g&&l.createElement("div",{className:C+"-extra"},g),h&&l.createElement("div",{className:C+"-content"},h))}var nv=d.forwardRef(LE);nv.displayName="Result";const lw=nv;function zE(e){var n=e.style,t=e.width,r=t===void 0?"60%":t,a=e.rows,i=a===void 0?3:a,s=e.className,o=e.prefixCls,u=K(o+"-text",s),c=[];function f(m){if(ct(r))return r[m];if(i-1===m)return r}for(var v=0;v<i;v++)c.push(l.createElement("li",{className:o+"-text-row",key:v,style:{width:f(v)}}));return l.createElement("ul",{className:u,style:n},c)}function $E(e){var n,t=e.style,r=e.shape,a=r===void 0?"square":r,i=e.size,s=e.position,o=s===void 0?"left":s,u=e.className,c=e.prefixCls,f=K(c+"-image",(n={},n[c+"-image-"+o]=o,n[c+"-image-"+a]=a,n[c+"-image-"+i]=i,n),u);return l.createElement("div",{className:f,style:t})}var kn=globalThis&&globalThis.__assign||function(){return kn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},kn.apply(this,arguments)};function Lu(e){return xe(e)?e:{}}var VE={text:!0,loading:!0};function HE(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,VE,i==null?void 0:i.Skeleton),u=o.style,c=o.className,f=o.animation,v=o.loading,m=o.image,p=o.text,g=o.children,h=Lu(m),b=Lu(p),y=a("skeleton"),C=K(y,(t={},t[y+"-animate"]=f,t[y+"-rtl"]=s,t),c);function w(){return m&&l.createElement("div",{className:y+"-header"},l.createElement($E,kn({prefixCls:y},h)))}function E(){return p&&l.createElement("div",{className:y+"-content"},l.createElement(zE,kn({prefixCls:y},b)))}return l.createElement(l.Fragment,null,v?l.createElement("div",kn({},ir(o),{className:C,style:u,ref:n}),h.position!=="right"&&w(),E(),h.position==="right"&&w()):g)}var av=d.forwardRef(HE);av.displayName="Skeleton";const sw=av;function iv(e){var n=[];return l.Children.forEach(e,function(t){Ju.isFragment(t)&&t.props?n=n.concat(iv(t.props.children)):t!=null&&n.push(t)}),n}var Ao=globalThis&&globalThis.__assign||function(){return Ao=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ao.apply(this,arguments)},WE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},FE={size:"small",direction:"horizontal"};function BE(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.componentConfig,s=r.rtl,o=He(e,FE,i==null?void 0:i.Space),u=o.className,c=o.style,f=o.children,v=o.size,m=o.direction,p=o.align,g=o.wrap,h=o.split,b=WE(o,["className","style","children","size","direction","align","wrap","split"]),y=a("space"),C=p||(m==="horizontal"?"center":""),w=K(y,(t={},t[y+"-"+m]=m,t[y+"-align-"+C]=C,t[y+"-wrap"]=g,t[y+"-rtl"]=s,t),u);function E(_){if(Ye(_))return _;switch(_){case"mini":return 4;case"small":return 8;case"medium":return 16;case"large":return 24;default:return 8}}var O=iv(f);function x(_){var T,P,I,j,M=O.length===_+1,R=s?"marginLeft":"marginRight";if(typeof v=="string"||typeof v=="number"){var k=E(v);return g?M?{marginBottom:k}:(T={},T[""+R]=k,T.marginBottom=k,T):M?{}:(P={},P[m==="vertical"?"marginBottom":R]=k,P)}if(ct(v)){var L=E(v[0]),S=E(v[1]);return g?M?{marginBottom:S}:(I={},I[""+R]=L,I.marginBottom=S,I):m==="vertical"?{marginBottom:S}:(j={},j[""+R]=L,j)}}return l.createElement("div",Ao({ref:n,className:w,style:c},b),O.map(function(_,T){var P,I=((P=_)===null||P===void 0?void 0:P.key)||T,j=h!=null&&T>0;return l.createElement(d.Fragment,{key:I},j&&l.createElement("div",{className:y+"-item-split"},h),l.createElement("div",{className:y+"-item",style:x(T)},_))}))}var ov=d.forwardRef(BE);ov.displayName="Space";const cw=ov;var Do=globalThis&&globalThis.__assign||function(){return Do=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Do.apply(this,arguments)},KE=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function GE(e,n){var t=d.useRef(!1),r=d.useContext(ye).getPrefixCls,a=e.children,i=e.className,s=e.style,o=e.lazyload,u=e.isActive,c=KE(e,["children","className","style","lazyload","isActive"]),f=r("tabs");return t.current=o?t.current||u:!0,t.current&&l.createElement("div",Do({ref:n},Ge(c,["destroyOnHide","title","closable"]),{className:K(f+"-pane",i),style:s}),a)}var Ul=l.forwardRef(GE);Ul.displayName="TabPane";Ul.isTabPane=!0;const UE=Ul;function ZE(e){var n=e.prefixCls,t=e.currentOffset,r=e.headerSize,a=e.headerWrapperSize,i=e.getTitleRef,s=e.paneChildren,o=e.direction,u=e.icon,c=s.map(function(g){return g.key}),f=o==="vertical"?r.height:r.width,v=o==="vertical"?a.height:a.width,m=d.useMemo(function(){var g={},h=a.domRect;return c.map(function(b){var y=i(b);if(y){var C=y.getBoundingClientRect();g[b]={left:C.left-h.left,right:C.left-h.left+C.width,top:C.top-h.top,bottom:C.top-h.top+C.height}}}),g},[c.join(","),f,v]),p=d.useMemo(function(){var g=-1,h=-1;for(var b in m){var y=m[b],C=y.left,w=y.right;if(C>=t&&w-t<=v&&g===-1&&(g=c.indexOf(b),h=g),C>=t&&w-t>v){h=c.indexOf(b);break}}return[g,h]},[m,c.join(","),t]);return Qu(u)?null:l.createElement(wl,{trigger:"click",droplist:l.createElement(po,{onClickMenuItem:e.onClickTab},s.map(function(g,h){if(h<p[0]||h>=p[1])return l.createElement(po.Item,{key:g.key,disabled:g.disabled},g.props.title)}))},l.createElement(Nt,{role:"button","aria-label":"expand tabs",prefix:n+"-dropdown",className:n+"-dropdown-icon"},u||l.createElement(Bn,null)))}var XE={prev:"left",next:"right"},YE={prev:"up",next:"down"},qE=function(e){var n,t=e.direction,r=e.headerSize,a=e.headerWrapperSize,i=e.prefixCls,s=e.iconPos,o=e.currentOffset,u=e.align,c=e.rtl,f=e.icon,v=a.height,m=a.width,p=r.height,g=r.width,h=p-v,b=g-m,y={up:l.createElement(fC,null),down:l.createElement(Bn,null),left:c?l.createElement(Ur,null):l.createElement(Zr,null),right:c?l.createElement(Zr,null):l.createElement(Ur,null)},C=function(j){j!==e.currentOffset&&e.onChange&&e.onChange(j)},w=function(j,M){j.preventDefault();var R;u==="left"?R=M==="left"?o-m:o+m:R=M==="left"?o+m:o-m,C(R)},E=function(j,M){j.preventDefault();var R;if(M==="up")R=o-v;else if(R=o+v,R>=p)return;C(R)},O=d.useMemo(function(){return u==="left"?o<=0:t==="vertical"?o>=h:o>=b},[u,t,o,b,o]),x=d.useMemo(function(){return u==="left"?t==="vertical"?o>=h:o>=b:o<=0},[u,t,h,b,o]);if(Qu(f))return null;var _=t==="horizontal"?XE[s]:YE[s],T=s==="prev"?O:x,P=K(i+"-"+_+"-icon",(n={},n[i+"-nav-icon-disabled"]=T,n)),I=t==="vertical"?E:w;return l.createElement(Nt,{disabled:T,className:P,prefix:i,onClick:function(j){return I(j,_)}},f||y[_])};const zu=qE;var Zl=function(e,n){var t=e.getBoundingClientRect(),r=n.getBoundingClientRect(),a=n.offsetWidth/r.width,i=n.offsetHeight/r.height;return{left:(t.left-r.left)*a,top:(t.top-r.top)*i,right:(t.right-r.right)*a,bottom:(t.bottom-r.bottom)*i}},JE=function(e,n){var t=e.scrollLeft,r=e.scrollTop;n==="horizontal"&&t&&e.scrollTo({left:-1*t}),n==="vertical"&&r&&e.scrollTo({top:-1*r})},lv=function(e){var n=e.onPressEnter;return{onKeyDown:function(t){var r=t.keyCode||t.which;r===Lt.code&&n(t)}}},Lo=globalThis&&globalThis.__assign||function(){return Lo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Lo.apply(this,arguments)},QE=function(e,n){var t,r=e.prefixCls,a=e.onDeleteTab,i=e.tabKey,s=e.isActive,o=e.onClickTab,u=e.disabled,c=u===void 0?!1:u,f=e.title,v=e.editable,m=e.renderTitle,p=e.deleteIcon,g=e.deleteButton,h=e.getIdPrefix,b=e.index,y=Ke(m)?m:function(E){return E},C=function(E){E.stopPropagation(),!c&&a()},w=function(E){c||o(E)};return y(l.createElement("div",{ref:n,key:i,className:K(r+"-header-title",(t={},t[r+"-header-title-active"]=s,t[r+"-header-title-editable"]=v,t[r+"-header-title-disabled"]=c,t)),role:"tab","aria-selected":s,tabIndex:c?-1:0,"aria-disabled":c||void 0,id:h(b).tab,"aria-controls":h(b).tabpane,onClick:w,onKeyDown:function(E){var O=E.keyCode||E.which;O===Lt.code&&w(E)}},l.createElement("span",{className:r+"-header-title-text"},f),v&&l.createElement("span",Lo({role:"button","aria-label":"remove tab","aria-disabled":c||void 0,tabIndex:c?-1:0,className:r+"-close-icon"},lv({onPressEnter:C}),{onClick:C}),g||l.createElement(Nt,{prefix:r},p||l.createElement(Dt,null)))),{key:i,isActive:s,disabled:c,editable:v})};const e6=l.forwardRef(QE);var t6=function(e,n,t){var r={left:"",width:"",top:"",height:""};if(n){var a=Zl(n,t);e==="vertical"?r={top:a.top+"px",height:n.offsetHeight+"px",left:"",width:""}:r={left:a.left+"px",width:n.offsetWidth+"px",top:"",height:""}}return r},r6=function(e){var n,t=e.prefixCls,r=e.animation,a=e.disabled,i=e.direction,s=e.getTitleRef,o=e.activeTab,u=e.getHeaderRef,c=e.inkBarSize,f=d.useRef(),v=d.useRef();return d.useEffect(function(){var m=Dn(function(){var p,g=s(o),h=t6(i,g,u("headerRef").current);h&&!E5(v.current,h)&&(v.current=h,!((p=f.current)===null||p===void 0)&&p.style&&Object.keys(h).forEach(function(b){f.current.style[b]=h[b]}))});return m(),function(){m.cancel&&m.cancel()}}),l.createElement("div",{className:K(t+"-header-ink",(n={},n[t+"-header-ink-no-animation"]=!r,n[t+"-header-ink-disabled"]=a,n[t+"-header-ink-custom"]=c,n)),ref:f},c&&l.createElement("div",{style:c,className:t+"-header-ink-inner"}))};const n6=r6;var a6=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function bn(){var e=d.useRef(),n=a6(d.useState({height:0,width:0}),2),t=n[0],r=n[1];return d.useEffect(function(){e.current&&r({height:e.current.offsetHeight,width:e.current.offsetWidth,domRect:e.current.getBoundingClientRect()})},[]),[e,t,r]}function i6(e){var n=e.headerWrapperRef,t=e.headerOffset,r=e.align,a=e.isScrollable,i=e.direction,s=e.onScroll;function o(b,y){var C=0;i==="vertical"?C=t+y:C=r==="left"?t+b:t-b,s&&s(C)}var u=d.useRef("x");function c(b){if(a){b.preventDefault();var y=b.deltaX,C=b.deltaY,w=0,E=Math.abs(y),O=Math.abs(C);E===O?w=u.current==="x"?y:C:E>O?(w=y,u.current="x"):(w=C,u.current="y"),o(w,w)}}var f=d.useRef({clientX:0,clientY:0}),v=function(b){return b&&b.touches&&b.touches.length&&b.touches[0]},m=function(b){b.cancelable&&b.preventDefault();var y=v(b);if(y){var C=f.current,w=C.clientX,E=C.clientY,O=y.clientX-w,x=y.clientY-E;o(-O,-x)}},p=function(){ut(document.documentElement,"touchmove",m),ut(document.documentElement,"touchend",p)},g=function(b){if(a){var y=v(b);y&&(f.current={clientX:y.clientX,clientY:y.clientY},it(document.documentElement,"touchmove",m,{passive:!1}),it(window,"touchend",p,{passive:!1}))}},h=d.useRef(null);h.current={onWheel:c,onTouchStart:g},d.useEffect(function(){it(n.current,"wheel",function(b){h.current.onWheel(b)},{passive:!1}),it(n.current,"touchstart",function(b){h.current.onTouchStart(b)},{passive:!0})},[n.current])}var er=globalThis&&globalThis.__assign||function(){return er=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},er.apply(this,arguments)},Qt=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Xl="vertical",Ta="right",Sa="left",$u={delete:!0,add:!0},o6=function(e){var n=e.direction,t=e.align,r=t===void 0?Sa:t,a=e.headerOffset,i="translateX("+-a+"px)";return r===Ta&&(i="translateX("+a+"px)"),n===Xl&&(i="translateY("+-a+"px)"),i2(i)},l6=function(e){var n=e.direction,t=e.align,r=t===void 0?Sa:t,a=e.headerDom,i=e.headerWrapperDom,s=Zl(a,i);return n===Xl?-s.top:r===Ta?s.right:-s.left},sv=l.forwardRef(function(e,n){var t,r,a=d.useContext(Yl),i=d.useContext(ye).rtl,s=er(er({},e),a),o=Qt(bn(),3),u=o[0],c=o[1],f=o[2],v=Qt(bn(),3),m=v[0],p=v[1],g=v[2],h=Qt(bn(),3),b=h[0],y=h[1],C=h[2],w=Qt(bn(),3),E=w[0],O=w[1],x=w[2],_=Qt(bn(),3),T=_[0],P=_[1],I=_[2],j=d.useRef({}),M=Qt(d.useState(0),2),R=M[0],k=M[1],L=Qt(d.useState(!0),2),S=L[0],N=L[1],A=s.paneChildren,W=s.editable,D=s.prefixCls,F=s.onAddTab,H=s.direction,z=s.type,B=z===void 0?"line":z,re=s.overflow,V=re===void 0?"scroll":re,$=s.activeTab,X=s.showAddButton,Z=s.size,Y=Z===void 0?"default":Z,J=s.style,ae=s.tabPosition,ve=s.className,ne=s.extra,G=s.animation,U=s.icons,oe=s.deleteButton,ie=s.addButton,te=s.renderTabTitle,pe=s.scrollAfterEdit,Te=s.scrollPosition,Q=Te===void 0?"auto":Te,me=s.inkBarSize,ee=xe(pe)?er(er({},$u),pe):$u,de=Qt(i?[Ta,Sa]:[Sa,Ta],2),ce=de[0],ge=de[1],fe=B==="capsule"?ge:ce,ue=d.useMemo(function(){var we=y.height-O.height-P.height,le=y.width-O.width-P.width,se=s.direction==="vertical"?we<p.height:le<p.width;return se},[s.direction,y,O,p,P]),Pe=function(){if(b.current){var we=b.current;C({height:we.offsetHeight,width:we.offsetWidth})}},Oe=function(we){return ol(function(le){Pe();var se=le[0]&&le[0].target;se&&we({height:se.offsetHeight,width:se.offsetWidth,domRect:se.getBoundingClientRect()})},200)},Ce=Oe(f),Ne=Oe(g),Ae=Oe(x),De=Oe(I),Fe=d.useCallback(function(we){var le=H===Xl?p.height-c.height:p.width-c.width,se=we;return se=Math.min(le,se),se=Math.max(se,0),se},[H,p,c]),tt=function(we){var le=Fe(we);le!==R&&k(le)};d.useEffect(function(){return function(){var we,le,se,Ee;(we=Ne==null?void 0:Ne.cancel)===null||we===void 0||we.call(Ne),(le=Ce==null?void 0:Ce.cancel)===null||le===void 0||le.call(Ce),(se=Ae==null?void 0:Ae.cancel)===null||se===void 0||se.call(Ae),(Ee=De==null?void 0:De.cancel)===null||Ee===void 0||Ee.call(De)}},[]),d.useEffect(function(){if(!S){N(!0);return}var we=function(){var se=j.current[$];if(!se||!ue)return 0;var Ee=Zl(se,u.current),Se=l6({direction:H,align:fe,headerDom:m.current,headerWrapperDom:u.current});if(H==="vertical"){var ze=Se,_e=Q,Me=Se+Ee.top,Re=Se+Ee.bottom;return _e==="auto"&&(_e=Ee.top<0?"start":Ee.bottom>0?"end":Q),_e==="start"?ze=Me:_e==="end"?ze=Re:_e==="center"?ze=Me-(Ee.top-Ee.bottom)/2:Ye(_e)&&(ze=Math.max(Me-_e,Re)),ze}if(fe==="right"){var Xe=Se-Ee.left,Je=Se-Ee.right,vt=Q,or=Se;return Q==="auto"&&(vt=Ee.left<0?"start":Ee.right>0?"end":Q),vt==="start"?or=Xe:vt==="end"?or=Je:vt==="center"?or=Xe+(Ee.left-Ee.right)/2:Ye(vt)&&(or=Math.min(Xe+vt,Je)),or}var $t=Se,Zt=Q,wr=Se+Ee.left,Or=Se+Ee.right;return Q==="auto"&&(Zt=Ee.left<0?"start":Ee.right>0?"end":Q),Zt==="start"?$t=wr:Zt==="end"?$t=Or:Zt==="center"?$t=wr-(Ee.left-Ee.right)/2:Ye(Zt)&&($t=Math.max(wr-Zt,Or)),$t};JE(u.current,H);var le=we();le=Fe(le),k(le)},[$,H,V,ue,B,Fe,Q]);var Be=o6({direction:H,align:fe,headerOffset:R}),dt=ue&&V==="dropdown"&&H!=="vertical",rt=ue&&!dt,he=W&&(B==="card"||B==="card-gutter"||B==="line"),Ie=function(we){s.onDeleteTab&&s.onDeleteTab(we.key),N(ee.delete)},qe=function(){F==null||F(),N(ee.add)},Le=function(we){return we&&X&&l.createElement(pt,{onResize:De},l.createElement("div",er({className:D+"-add-icon","aria-label":"add tab",tabIndex:0,role:"button",ref:T,onClick:qe},lv({onPressEnter:qe})),ie||l.createElement(Nt,{prefix:D+"-add"},l.createElement("span",{className:D+"-add"},(U==null?void 0:U.add)||l.createElement(_5,null)))))};return i6({headerWrapperRef:u,headerOffset:R,align:fe,direction:H,isScrollable:ue,onScroll:function(we){tt(we)}}),l.createElement("div",{className:K(D+"-header-nav",D+"-header-nav-"+H,D+"-header-nav-"+ae,D+"-header-size-"+Y,D+"-header-nav-"+B,ve),style:J,ref:n},l.createElement("div",{className:K(D+"-header-scroll",(t={},t[D+"-header-overflow-scroll"]=rt,t[D+"-header-overflow-dropdown"]=dt,t)),ref:b},rt&&l.createElement(zu,{iconPos:"prev",rtl:i,icon:U==null?void 0:U.prev,prefixCls:D,currentOffset:R,headerSize:p,headerWrapperSize:c,direction:H,align:fe,onChange:tt}),l.createElement(pt,{onResize:Ce},l.createElement("div",{className:D+"-header-wrapper",ref:u},l.createElement(pt,{onResize:Ne},l.createElement("div",{className:K(D+"-header",(r={},r[D+"-header-no-padding"]=!e.headerPadding&&H==="horizontal"&&["line","text"].indexOf(B)>-1,r)),ref:m,style:Be},A.map(function(we,le){return l.createElement(e6,er({key:le,ref:function(se){j.current[we.key]=se},tabKey:we.key},we.props,{prefixCls:D,onDeleteTab:function(){return Ie(we)},renderTitle:e.children||te,onClickTab:function(){s.onClickTab&&s.onClickTab(we.key)},isActive:$===we.key,editable:he&&we.props.closable!==!1,deleteIcon:U==null?void 0:U.delete,deleteButton:oe,getIdPrefix:a.getIdPrefix,index:le}))}),B==="line"&&l.createElement(n6,{disabled:!!A.find(function(we){return we&&we.props&&we.props.disabled&&we.key===$}),prefixCls:D,animation:G,direction:H,getTitleRef:function(we){return j.current[we]},activeTab:$,getHeaderRef:function(){return m},inkBarSize:me}))),!ue&&Le(he))),rt&&l.createElement(zu,{prefixCls:D,rtl:i,iconPos:"next",icon:U==null?void 0:U.next,currentOffset:R,headerSize:p,headerWrapperSize:c,direction:H,align:fe,onChange:tt}),dt&&l.createElement(ZE,{onClickTab:s.onClickTab,paneChildren:A,prefixCls:D,currentOffset:R,headerSize:p,icon:U==null?void 0:U.dropdown,headerWrapperSize:c,getTitleRef:function(we){return j.current[we]},direction:H}),(he&&ue||ne)&&l.createElement(pt,{onResize:Ae},l.createElement("div",{className:D+"-header-extra",ref:E},ue&&Le(he),ne))))});sv.displayName="TabHeader";const Vu=sv;function s6(e){var n,t=e.animation,r=e.activeTab,a=e.prefixCls,i=e.paneChildren,s=e.direction,o=e.lazyload,u=e.destroyOnHide,c=i.findIndex(function(p){return p.key===r}),f=d.useContext(Yl),v=d.useContext(ye).rtl;if(i.every(function(p){return(p==null?void 0:p.props)&&(!("children"in p.props)||p.props.children===null)}))return null;var m=K(a+"-content-inner",(n={},n[a+"-animation"]=t,n));return l.createElement("div",{className:a+"-content "+a+"-content-"+s},l.createElement("div",{className:m,style:v?{marginRight:"-"+c*100+"%"}:{marginLeft:"-"+c*100+"%"}},i.map(function(p,g){var h,b=f.getIdPrefix(g),y=b.tabpane,C=b.tab,w="destroyOnHide"in p.props?p.props.destroyOnHide:u,E=p.key===r;return l.createElement("div",{key:p.key,className:K(a+"-content-item",(h={},h[a+"-content-item-active"]=E,h)),role:"tabpanel",id:y,"aria-hidden":E?void 0:!0,tabIndex:E?0:-1,"aria-labelledby":C},c!==g&&w?null:l.cloneElement(p,{lazyload:o,isActive:E}))})))}var Kt=globalThis&&globalThis.__assign||function(){return Kt=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Kt.apply(this,arguments)},c6=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},u6=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},f6=["mini","small","default","large"],d6=function(e){var n=e.children,t=[];return l.Children.forEach(n,function(r){r&&r.type&&r.type.isTabPane&&t.push(r)}),t},v6=function(e){var n=e.direction,t=e.tabPosition,r=e.animation;return n==="vertical"||t==="left"||t==="right"?!1:xe(r)?"tabPane"in r?r.tabPane:!1:r},p6={tabPosition:"top",type:"line",overflow:"scroll",showAddButton:!0,lazyload:!0,headerPadding:!0,scrollPosition:"auto"},Yl=l.createContext({});function m6(e,n){var t,r=d.useContext(ye),a=r.getPrefixCls,i=r.size,s=r.componentConfig,o=r.rtl,u=He(e,p6,s==null?void 0:s.Tabs),c=d6(u),f=d.useRef(),v=u6(ot(c[0]&&c[0].key,{defaultValue:"defaultActiveTab"in u?u.defaultActiveTab:void 0,value:"activeTab"in u?u.activeTab:void 0}),2),m=v[0],p=v[1],g=a("tabs"),h=u.size||(f6.indexOf(i)>-1?i:"default"),b=u.animation,y=u.className,C=u.direction,w=u.style,E=u.type,O=u.justify,x=u.destroyOnHide,_=u.lazyload,T=u.onChange,P=u.onClickTab,I=u.onDeleteTab,j=u.renderTabHeader,M=c6(u,["animation","className","direction","style","type","justify","destroyOnHide","lazyload","onChange","onClickTab","onDeleteTab","renderTabHeader"]),R=an(g+"-"),k=C==="vertical"?"left":u.tabPosition,L={animation:xe(b)&&"inkBar"in b?b.inkBar:!0,activeTab:m,tabPosition:k,direction:["left","right"].indexOf(k)>-1?"vertical":"horizontal",paneChildren:c,onClickTab:function(N){Ke(P)&&P(N),N!==m&&("activeTab"in u||p(N),Ke(T)&&T(N))},onDeleteTab:I,prefixCls:g};d.useImperativeHandle(n,function(){return f},[]);var S=l.createElement(s6,{direction:["left","right"].indexOf(k)>-1?"vertical":"horizontal",animation:v6(u),activeTab:m,paneChildren:c,prefixCls:g,destroyOnHide:x,lazyload:_});return l.createElement("div",Kt({},Ge(M,["headerPadding","tabPosition","defaultActiveTab","showAddButton","extra","onAddTab","activeTab","overflow","editable","renderTabTitle","addButton","deleteButton","icons","children","size","type","scrollPosition","offsetAlign"]),{style:w,className:K(g,g+"-"+(["left","right"].indexOf(k)>-1?"vertical":"horizontal"),g+"-"+E,g+"-"+k,g+"-size-"+h,(t={},t[g+"-justify"]=O,t[g+"-rtl"]=o,t),y),ref:f}),l.createElement(Yl.Provider,{value:Kt(Kt({},L),{getIdPrefix:function(N){return{tab:R&&R+"-tab-"+N,tabpane:R&&R+"-panel-"+N}}})},k==="bottom"&&S,Ke(j)?j(Kt(Kt(Kt({},Ge(u,["children","style","className"])),{size:h}),L),Vu):l.createElement(Vu,Kt({},Ge(u,["children","style","className"]),{size:h})),k!=="bottom"&&S))}var g6=l.forwardRef(m6),ql=g6;ql.displayName="Tabs";ql.TabPane=UE;const uw=ql;function h6(e,n){var t=d.useContext(ye).getPrefixCls,r=t("typography"),a=e.className,i=e.style,s=e.children,o=K(r,a);return l.createElement("article",{ref:n,style:i,className:o},s)}var cv=d.forwardRef(h6);cv.displayName="Typography";const y6=cv;var b6=globalThis&&globalThis.__awaiter||function(e,n,t,r){function a(i){return i instanceof t?i:new t(function(s){s(i)})}return new(t||(t=Promise))(function(i,s){function o(f){try{c(r.next(f))}catch(v){s(v)}}function u(f){try{c(r.throw(f))}catch(v){s(v)}}function c(f){f.done?i(f.value):a(f.value).then(o,u)}c((r=r.apply(e,n||[])).next())})},x6=globalThis&&globalThis.__generator||function(e,n){var t={label:0,sent:function(){if(i[0]&1)throw i[1];return i[1]},trys:[],ops:[]},r,a,i,s;return s={next:o(0),throw:o(1),return:o(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function o(c){return function(f){return u([c,f])}}function u(c){if(r)throw new TypeError("Generator is already executing.");for(;t;)try{if(r=1,a&&(i=c[0]&2?a.return:c[0]?a.throw||((i=a.return)&&i.call(a),0):a.next)&&!(i=i.call(a,c[1])).done)return i;switch(a=0,i&&(c=[c[0]&2,i.value]),c[0]){case 0:case 1:i=c;break;case 4:return t.label++,{value:c[1],done:!1};case 5:t.label++,a=c[1],c=[0];continue;case 7:c=t.ops.pop(),t.trys.pop();continue;default:if(i=t.trys,!(i=i.length>0&&i[i.length-1])&&(c[0]===6||c[0]===2)){t=0;continue}if(c[0]===3&&(!i||c[1]>i[0]&&c[1]<i[3])){t.label=c[1];break}if(c[0]===6&&t.label<i[1]){t.label=i[1],i=c;break}if(i&&t.label<i[2]){t.label=i[2],t.ops.push(c);break}i[2]&&t.ops.pop(),t.trys.pop();continue}c=n.call(e,t)}catch(f){c=[6,f],a=0}finally{r=i=0}if(c[0]&5)throw c[1];return{value:c[0]?c[1]:void 0,done:!0}}};function C6(e){return b6(this,void 0,void 0,function(){var n,t,r,a,i;return x6(this,function(s){switch(s.label){case 0:if(!(navigator.clipboard&&navigator.clipboard.writeText))return[3,4];s.label=1;case 1:return s.trys.push([1,3,,4]),[4,navigator.clipboard.writeText(e)];case 2:return s.sent(),[2];case 3:return n=s.sent(),console.error(n??new DOMException("The request is not allowed","NotAllowedError")),[3,4];case 4:t=document.createElement("span"),t.textContent=e,t.style.whiteSpace="pre",document.body.appendChild(t),r=window.getSelection(),a=window.document.createRange(),r.removeAllRanges(),a.selectNode(t),r.addRange(a),i=!1;try{i=window.document.execCommand("copy")}catch(o){console.log("error",o)}return r.removeAllRanges(),window.document.body.removeChild(t),[2,i?Promise.resolve():Promise.reject(new DOMException("The request is not allowed","NotAllowedError"))]}})})}function Hu(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Wu(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Hu(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Hu(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function E6(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Wu(Wu({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-copy")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"M20 6h18a2 2 0 0 1 2 2v22M8 16v24c0 1.105.891 2 1.996 2h20.007A1.99 1.99 0 0 0 32 40.008V15.997A1.997 1.997 0 0 0 30 14H10a2 2 0 0 0-2 2Z"}))}var Jl=l.forwardRef(E6);Jl.defaultProps={isIcon:!0};Jl.displayName="IconCopy";const w6=Jl;function Fu(e,n){var t=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n&&(r=r.filter(function(a){return Object.getOwnPropertyDescriptor(e,a).enumerable})),t.push.apply(t,r)}return t}function Bu(e){for(var n=1;n<arguments.length;n++){var t=arguments[n]!=null?arguments[n]:{};n%2?Fu(Object(t),!0).forEach(function(r){Ue(e,r,t[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):Fu(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))})}return e}function O6(e,n){var t=d.useContext(We),r=t.prefixCls,a=r===void 0?"arco":r,i=e.spin,s=e.className,o=Bu(Bu({"aria-hidden":!0,focusable:!1,ref:n},e),{},{className:"".concat(s?s+" ":"").concat(a,"-icon ").concat(a,"-icon-edit")});return i&&(o.className="".concat(o.className," ").concat(a,"-icon-loading")),delete o.spin,delete o.isIcon,l.createElement("svg",Ve({fill:"none",stroke:"currentColor",strokeWidth:"4",viewBox:"0 0 48 48"},o),l.createElement("path",{d:"m30.48 19.038 5.733-5.734a1 1 0 0 0 0-1.414l-5.586-5.586a1 1 0 0 0-1.414 0l-5.734 5.734m7 7L15.763 33.754a1 1 0 0 1-.59.286l-6.048.708a1 1 0 0 1-1.113-1.069l.477-6.31a1 1 0 0 1 .29-.631l14.7-14.7m7 7-7-7M6 42h36"}))}var Ql=l.forwardRef(O6);Ql.defaultProps={isIcon:!0};Ql.displayName="IconEdit";const _6=Ql;var Ku=function(e){return ar(e)||Ye(e)};function $n(e){var n=[""];return l.Children.forEach(e,function(t){var r=n.length-1,a=n[r];Ku(t)&&Ku(a)?n[r]=""+a+t:t&&t.props&&t.props.children&&n.push($n(t.props.children))}),n.join("")}var yr=globalThis&&globalThis.__assign||function(){return yr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},yr.apply(this,arguments)},P6=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function T6(e){var n=e.children,t=e.copyable,r=e.editable,a=e.ellipsis,i=e.expanding,s=e.setEditing,o=e.onClickExpand,u=e.forceShowExpand,c=e.isEllipsis,f=e.currentContext,v=f===void 0?{}:f,m=Er(),p=v.getPrefixCls,g=v.locale,h=p("typography"),b=P6(d.useState(!1),2),y=b[0],C=b[1],w=d.useRef(null),E=xe(t)?t:{},O=xe(a)?a:{},x=xe(r)?r:{},_=ct(O.expandNodes)?O.expandNodes:[g.Typography.fold,g.Typography.unfold];d.useEffect(function(){return function(){clearTimeout(w.current),w.current=null}},[]);function T(k){if(!y){var L=E.text!==void 0?E.text:$n(n);C6(L),C(!0),E.onCopy&&E.onCopy(L,k),w.current=setTimeout(function(){C(!1)},3e3)}}var P=function(k){x.onStart&&x.onStart($n(n),k),s(!0)},I=E.tooltips||[g.Typography.copy,g.Typography.copied],j=t&&l.createElement(Cr,yr({content:y?I[1]:I[0]},E.tooltipProps),l.createElement("span",yr({className:y?h+"-operation-copied":h+"-operation-copy",onClick:T,role:"button","aria-label":I[0],tabIndex:0},m({onPressEnter:T})),y?l.createElement(Fo,null):E.icon||l.createElement(w6,null))),M=r&&l.createElement(Cr,yr({content:g.Typography.edit},x.tooltipProps),l.createElement("span",yr({tabIndex:0,"aria-label":g.Typography.edit,role:"button",className:h+"-operation-edit",onClick:P},m({onPressEnter:P})),l.createElement(_6,null))),R=u||O.expandable&&c?l.createElement("a",yr({className:h+"-operation-expand",onClick:o,role:"button",tabIndex:0,"aria-label":g.Typography.unfold},m({onPressEnter:o})),i?_[0]:_[1]):null;return l.createElement(l.Fragment,null,R,M,j)}function S6(e,n){var t=e.prefixCls,r=e.children,a=e.setEditing,i=e.editableConfig,s=e.style,o=K(t+"-typography",t+"-edit-content",e.className),u=$n(r),c=d.useRef(null);d.useEffect(function(){if(c.current&&c.current.focus&&c.current.focus(),c.current&&c.current.dom){var p=c.current.dom.value.length;c.current.dom.setSelectionRange(p,p)}},[]);function f(){a(!1),i.onEnd&&i.onEnd(u)}function v(p){i.onChange&&i.onChange(p)}function m(){f()}return l.createElement("div",{className:o,style:s,ref:n},l.createElement(Za.TextArea,{className:t+"-edit-content-textarea",onBlur:m,ref:c,value:u,autoSize:!0,onChange:v,onPressEnter:f}))}const N6=d.forwardRef(S6);var Hr=globalThis&&globalThis.__assign||function(){return Hr=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Hr.apply(this,arguments)},xn=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},I6="hxj",at;(function(e){e[e.INIT=0]="INIT",e[e.BEFORE_MEASURE=1]="BEFORE_MEASURE",e[e.MEASURING=2]="MEASURING",e[e.MEASURE_END=3]="MEASURE_END",e[e.NO_NEED_ELLIPSIS=4]="NO_NEED_ELLIPSIS"})(at||(at={}));function j6(e){var n=e.children,t=e.rows,r=t===void 0?1:t,a=e.width,i=e.expanding,s=e.renderMeasureContent,o=e.simpleEllipsis,u=e.onEllipsis,c=e.suffix,f=e.expandNodes,v=e.expandable,m=e.ellipsisStr,p=d.useRef(),g=d.useRef(),h=xn(d.useState([0,0,0]),2),b=h[0],y=h[1],C=xn(d.useState(0),2),w=C[0],E=C[1],O=xn(d.useState(at.NO_NEED_ELLIPSIS),2),x=O[0],_=O[1],T=xn(b,3),P=T[0],I=T[1],j=T[2],M=xn(d.useState(!1),2),R=M[0],k=M[1],L=d.useMemo(function(){return l.Children.toArray(n)},[n]),S=d.useRef(0);Fd(function(){u&&u(R)},[R]);var N=function(X){return ar(X)||Ye(X)},A=function(X){var Z=0;return X.forEach(function(Y){N(Y)?Z+=String(Y).length:Z+=1}),Z},W=d.useMemo(function(){return A(L)},[L]),D=Dn(function(X){return y(X)}),F=function(X){var Z=[],Y=0;if(X>=W)return L;for(var J in L){var ae=L[J];if(Y>=X)return Z;var ve=N(ae)?String(ae).length:1;if(ve>X-Y)return Z.push(String(ae).slice(0,X-Y)),Y=X,Z;Y+=ve,Z.push(ae)}return Z},H=function(){var X,Z;if(w){if(x===at.INIT){var Y=r*w,J=(X=g.current)===null||X===void 0?void 0:X.offsetHeight,ae=J>Y;!ae||o||i?(_(at.MEASURE_END),k(ae),y([0,W,W])):(k(!0),_(at.BEFORE_MEASURE))}else if(x===at.BEFORE_MEASURE){var ve=p==null?void 0:p.current.offsetWidth,ne=r*a;if(ve>r*a){var G=Math.max(ne/ve-.1,0),U=Math.min(ne/ve+.1,1),oe=Math.floor(G*W),ie=Math.ceil(U*W),te=Math.floor((oe+ie)/2);S.current=te}_(at.MEASURING)}else if(x===at.MEASURING)if(P!==j-1){var J=(Z=g.current)===null||Z===void 0?void 0:Z.offsetHeight,Y=r*w,pe=P,Te=j;J<=Y?pe=I:Te=I;var Q=Math.floor((Te+pe)/2);D([pe,Q,Te])}else D([P,P,P]),_(at.MEASURE_END)}};Fr(function(){e.rows&&a?(y([0,Math.floor(W/2),W]),_(at.INIT)):_(at.NO_NEED_ELLIPSIS)},[W,o,i,a,c,f,v,m,e.rows]),Fr(function(){if(p.current&&x===at.INIT){var X=p.current.offsetHeight;E(X)}},[x]),Fr(function(){H()},[x,I,P,j,w]);var z={zIndex:-999,position:"fixed",opacity:0,padding:0,margin:0},B=Hr({whiteSpace:"nowrap"},z),re=o?Hr({textOverflow:"clip"},z):z,V;if(x===at.INIT||x===at.BEFORE_MEASURE)V=l.createElement("div",null,l.createElement("div",{ref:p,style:B},x===at.INIT?I6:s(n,!1)),l.createElement("div",{ref:g,style:Hr({width:a},re)},s(n,R))),V=V.props.children;else if(x===at.MEASURING){var $={height:w*r,overflow:"hidden"};V=l.createElement("div",null,l.createElement("div",{ref:g,style:Hr({width:a},re)},s(F(I),R)),l.createElement("div",{style:$},F(S.current))),V=V.props.children}else x===at.MEASURE_END?V=s(F(I),R):x===at.NO_NEED_ELLIPSIS&&(V=s(n,!1));return{ellipsisNode:V,isEllipsis:R,measureStatus:x}}var M6=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},R6=function(e,n){if(typeof window<"u"&&window.CSS&&window.CSS.supports)return ft(n)?window.CSS.supports(e):window.CSS.supports(e,n);if(typeof document<"u"&&document.createElement){var t=document.createElement("div");return t.setAttribute("style",e+":"+n+";"),typeof t.style[e]<"u"}return!1},k6={display:" -webkit-box","-webkit-line-clamp":2},A6=function(){return Object.entries(k6).every(function(e){var n=M6(e,2),t=n[0],r=n[1];return R6(t,r)})};function D6(e){var n=e.cssEllipsis,t=e.ellipsisStr,r=t===void 0?"...":t,a=e.suffix,i=e.rows,s=d.useMemo(function(){return!n||i>1&&!A6()?!1:r==="..."&&!a},[r,n,i,a]),o={textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden"},u={textOverflow:"ellipsis",whiteSpace:"normal",overflow:"hidden",WebkitLineClamp:""+e.rows,WebkitBoxOrient:"vertical",display:"-webkit-box"};return{simpleEllipsis:s,ellipsisStyle:s?e.rows>1?u:o:{}}}var Gu=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Uu=globalThis&&globalThis.__spreadArray||function(e,n,t){if(t||arguments.length===2)for(var r=0,a=n.length,i;r<a;r++)(i||!(r in n))&&(i||(i=Array.prototype.slice.call(n,0,r)),i[r]=n[r]);return e.concat(i||Array.prototype.slice.call(n))};function L6(e){var n=null,t=function(){for(var r=[],a=0;a<arguments.length;a++)r[a]=arguments[a];n||(e.apply(void 0,Uu([],Gu(r),!1)),n=Ct(function(){})),n&&ht(n),n=Ct(function(){e.apply(void 0,Uu([],Gu(r),!1)),n=null})};return t.cancel=function(){ht(n),n=null},t}var Ot=globalThis&&globalThis.__assign||function(){return Ot=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ot.apply(this,arguments)},z6=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t},Ri=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i};function $6(e,n){var t=e.type,r=e.bold,a=e.disabled,i=e.mark,s=e.underline,o=e.delete,u=e.code,c=[],f=[];return t&&f.push(n+"-"+t),a&&f.push(n+"-disabled"),r&&c.push("b"),s&&c.push("u"),o&&c.push("del"),u&&c.push("code"),i&&c.push("mark"),{component:c,className:f}}function V6(e,n){var t,r=e.componentType,a=e.style,i=e.className,s=e.children,o=e.editable,u=e.ellipsis,c=e.heading,f=e.blockquote,v=z6(e,["componentType","style","className","children","editable","ellipsis","heading","blockquote"]),m=d.useContext(ye),p=m.getPrefixCls,g=m.rtl,h=p("typography"),b=d.useRef(),y=$6(e,h),C=y.component,w=y.className,E=Ri(d.useState(!1),2),O=E[0],x=E[1],_=Ri(d.useState(0),2),T=_[0],P=_[1],I=xe(o)?o:{},j="editing"in I?I.editing:O,M=u?Ot({rows:1,ellipsisStr:"...",cssEllipsis:!1},xe(u)?u:{}):{},R=M.wrapper||l.Fragment,k=Ri(ot(!1,{defaultValue:M.defaultExpanded,value:M.expanded}),2),L=k[0],S=k[1],N=D6(M),A=N.simpleEllipsis,W=N.ellipsisStyle,D=function(J,ae){var ve=ft(M.ellipsisStr)?"...":M.ellipsisStr,ne=!ft(M.suffix)&&M.suffix;return l.createElement(R,null,J,ae&&!L&&!A?ve:"",ne,V(ae))},F=j6(Ot(Ot({},M),{children:s,expanding:L,width:T,renderMeasureContent:D,simpleEllipsis:A||L})),H=F.ellipsisNode,z=F.isEllipsis,B=F.measureStatus,re=L6(function(J){var ae=(J==null?void 0:J[0]).contentRect;if(ae){var ve=C.includes("code")?ae.width-18:ae.width,ne=[at.NO_NEED_ELLIPSIS,at.MEASURE_END];ne.includes(B)&&P(ve)}});function V(J){return l.createElement(l.Fragment,null,l.createElement(T6,Ot({},e,{setEditing:x,onClickExpand:$,expanding:L,isEllipsis:J,currentContext:m})))}function $(J){S(!L),e.onClickExpand&&e.onClickExpand(J),M.onExpand&&M.onExpand(!L,J)}Fd(function(){M.onEllipsis&&M.onEllipsis(z)},[z]),d.useImperativeHandle(n,function(){return b.current});function X(J,ae,ve,ne){ne===void 0&&(ne={});var G=J;return ae.forEach(function(U,oe){var ie=oe===0?ne:{},te=xe(ve.mark)&&ve.mark.color?Ot({style:{backgroundColor:ve.mark.color}},ie):Ot({},ie);G=l.createElement(U,Ot({},te),G)}),G}var Z;r==="Paragraph"?Z=f?"blockquote":"div":r==="Title"?Z="h"+c:r==="Text"&&(Z=u?"div":"span");function Y(){var J,ae=$n(l.Children.toArray(s)),ve=M.showTooltip,ne=xe(M.showTooltip)&&M.showTooltip.type==="popover"?"popover":"tooltip",G=xe(M.showTooltip)?M.showTooltip.props||{}:{},U=ne==="popover"?ml:Cr,oe=z&&!ve&&!L?{title:ae}:{},ie=Ot({style:a},oe),te=z&&ve&&!L,pe=l.createElement(pt,{onResize:re,getTargetDOMNode:function(){return b.current}},l.createElement(Z,Ot({ref:b,className:K(h,w,(J={},J[h+"-rtl"]=g,J),i)},ie,Ge(v,["spacing","type","close","bold","disabled","mark","underline","delete","code","copyable","isEllipsis","expanding","onClickExpand","setEditing","forceShowExpand"])),A&&B!==at.INIT&&!L&&z?X(D(l.createElement("span",{style:W},s),z),C.length?C:["span"],e,{className:h+"-simple-ellipsis"}):X(H,C,e)));return te?l.createElement(U,Ot({content:ae},G),l.createElement("span",null,pe)):pe}return j?l.createElement(N6,Ot({ref:n},e,{className:K(h,w,(t={},t[h+"-rtl"]=g,t),h+"-"+Z,i),prefixCls:h,setEditing:x,editableConfig:I})):Y()}const es=d.forwardRef(V6);var zo=globalThis&&globalThis.__assign||function(){return zo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},zo.apply(this,arguments)},H6=globalThis&&globalThis.__rest||function(e,n){var t={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&n.indexOf(r)<0&&(t[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var a=0,r=Object.getOwnPropertySymbols(e);a<r.length;a++)n.indexOf(r[a])<0&&Object.prototype.propertyIsEnumerable.call(e,r[a])&&(t[r[a]]=e[r[a]]);return t};function W6(e,n){var t=e.heading,r=t===void 0?1:t,a=H6(e,["heading"]);return l.createElement(es,zo({heading:r},a,{componentType:"Title",ref:n}))}var uv=d.forwardRef(W6);uv.displayName="Title";const F6=uv;var $o=globalThis&&globalThis.__assign||function(){return $o=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},$o.apply(this,arguments)};function B6(e,n){return l.createElement(es,$o({},e,{componentType:"Text",ref:n}))}var fv=d.forwardRef(B6);fv.displayName="Text";const K6=fv;var Vo=globalThis&&globalThis.__assign||function(){return Vo=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Vo.apply(this,arguments)};function G6(e,n){var t=e.spacing,r=t===void 0?"default":t,a=e.className,i=d.useContext(ye).getPrefixCls,s=i("typography"),o=r==="close"?K(s+"-spacing-close",a):a;return l.createElement(es,Vo({},e,{ref:n,componentType:"Paragraph",className:o}))}var dv=d.forwardRef(G6);dv.displayName="Paragraph";const U6=dv;var Ho=globalThis&&globalThis.__assign||function(){return Ho=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++){n=arguments[t];for(var a in n)Object.prototype.hasOwnProperty.call(n,a)&&(e[a]=n[a])}return e},Ho.apply(this,arguments)},aa=globalThis&&globalThis.__read||function(e,n){var t=typeof Symbol=="function"&&e[Symbol.iterator];if(!t)return e;var r=t.call(e),a,i=[],s;try{for(;(n===void 0||n-- >0)&&!(a=r.next()).done;)i.push(a.value)}catch(o){s={error:o}}finally{try{a&&!a.done&&(t=r.return)&&t.call(r)}finally{if(s)throw s.error}}return i},Z6={rows:1,expandable:!0,defaultExpanded:!1},X6=function(e,n){var t,r,a=d.useContext(ye),i=He(e,Z6,(t=a.componentConfig)===null||t===void 0?void 0:t["Typography.Ellipsis"]),s=i.className,o=i.style,u=i.rows,c=i.disabled,f=i.showTooltip,v=i.children,m=i.expandable,p=i.expandRender,g=i.onExpand,h=i.onEllipsis,b=a.locale,y=qr?!1:/^((?!chrome|android).)*safari/i.test((r=navigator==null?void 0:navigator.userAgent)!==null&&r!==void 0?r:""),C=d.useRef(null),w=d.useRef(null),E=aa(d.useState(""),2),O=E[0],x=E[1],_=aa(d.useState(!1),2),T=_[0],P=_[1],I=d.useRef(null),j=d.useRef(null),M=aa(ot(!1,{defaultValue:i.defaultExpanded,value:i.expanded}),2),R=M[0],k=M[1],L=aa(d.useState(!1),2),S=L[0],N=L[1],A=d.useMemo(function(){return xe(m)?!m.single&&u===1:u===1},[u,m]),W=d.useMemo(function(){return xe(f)?{tooltip:!0,tooltipProps:f}:{tooltip:!!f,tooltipProps:f}},[f]);d.useEffect(function(){if(w.current){var $=w.current.textContent;$&&x($)}},[v,w]);var D=a.getPrefixCls("ellipsis"),F=function(){return p?p(R):l.createElement("span",{className:D+"-action-text"},R?b.Typography.fold:b.Typography.unfold)},H=function(){var $;return m&&S?l.createElement("div",{className:K(D+"-action",($={},$[D+"-action-collapsed"]=!R,$)),onClick:function(X){R?(k(!1),g==null||g(!1,X)):(k(!0),P(!1),g==null||g(!0,X))}},F()):null},z=d.useCallback(Dn(function(){if(j.current&&I.current){var $=A?j.current.offsetWidth>I.current.offsetWidth:j.current.offsetHeight>I.current.offsetHeight;$?S===!1&&(N(!0),h==null||h(!0)):S===!0&&(N(!1),h==null||h(!1))}}),[S,A]),B=function(){return c?null:l.createElement(pt,{onResize:z},l.createElement("div",{className:A?K(D+"-content-mirror",D+"-single"):K(D+"-content-mirror",D+"-multiple",D+"-collapsed"),style:{WebkitBoxOrient:"vertical",MozBoxOrient:"vertical",WebkitLineClamp:u},ref:I},l.createElement(pt,{onResize:z},l.createElement("span",{ref:j,className:D+"-text"},v))))},re=function(){var $,X;return A?l.createElement("div",{className:K(D+"-content",D+"-single")},l.createElement("span",{ref:w,className:D+"-text"},v)):y?l.createElement("div",{className:K(D+"-content",D+"-multiple"),title:!W.tooltip&&S&&!R?O:void 0},!R&&H(),l.createElement("span",{ref:w,className:K(D+"-text",($={},$[D+"-collapsed"]=!R,$)),style:{WebkitBoxOrient:"vertical",MozBoxOrient:"vertical",WebkitLineClamp:u}},v),R&&H()):l.createElement("div",{className:K(D+"-content",D+"-multiple",(X={},X[D+"-collapsed"]=!R,X)),style:{WebkitBoxOrient:"vertical",MozBoxOrient:"vertical",WebkitLineClamp:u},title:!W.tooltip&&S&&!R?O:void 0},!R&&H(),l.createElement("span",{ref:w,className:D+"-text"},v),R&&H())},V=function(){return c?l.createElement("div",{className:D+"-content"},l.createElement("span",{ref:w,className:D+"-text"},v)):W.tooltip?l.createElement(Cr,Ho({content:O,popupVisible:T,disabled:!S||R,triggerProps:{mouseEnterDelay:100},onVisibleChange:function($){$?S&&!R&&P(!0):P(!1)}},W.tooltipProps),re()):re()};return l.createElement("div",{ref:C,className:K(D,s),style:o},B(),V())},Y6=d.forwardRef(X6);const q6=Y6;var Kn=y6;Kn.Title=F6;Kn.Text=K6;Kn.Paragraph=U6;Kn.Ellipsis=q6;const fw=Kn;export{Q6 as A,ew as B,rw as C,wl as D,cp as E,aw as G,We as I,iw as L,po as M,ow as P,lw as R,cw as S,fw as T,Ve as _,Ue as a,uw as b,tw as c,Bn as d,ia as e,ho as f,nw as g,pf as h,sw as i,gd as j,Za as k,pa as l,ap as z};
