package fc

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"

	"git.ngdevops.cn/ops/ngiq-point-desktop-upload-oss/pkg/config"
	"github.com/alibabacloud-go/darabonba-openapi/v2/client"
	fc "github.com/alibabacloud-go/fc-20230330/v4/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/sirupsen/logrus"
)

// Client 是对函数计算操作的封装
type Client struct {
	client          *fc.Client
	endpoint        string
	accessKeyID     string
	accessKeySecret string
}

// DeployInput 是部署函数的输入参数
type DeployInput struct {
	ServiceName   string // FC服务名称
	FunctionName  string // FC函数名称
	OSSBucketName string // OSS存储桶名称
	OSSObjectName string // OSS对象名称
	ZipFile       string // ZIP包的Base64编码内容（与OSS方式二选一）
	Runtime       string // 可选，运行时
	Handler       string // 可选，处理程序
	Env           string // 可选，环境变量
}

// NewClient 创建一个新的FC客户端
func NewClient(endpoint, accessKeyID, accessKeySecret string) (*Client, error) {
	logrus.WithFields(logrus.Fields{
		"endpoint":    endpoint,
		"accessKeyID": accessKeyID[:10] + "...", // 只显示前10位用于调试
	}).Info("正在创建FC客户端")

	// 检查参数是否为空
	if endpoint == "" || accessKeyID == "" || accessKeySecret == "" {
		errMsg := fmt.Sprintf("FC客户端参数为空: endpoint=%s, accessKeyID=%s, accessKeySecret=%s",
			endpoint,
			accessKeyID,
			func() string {
				if accessKeySecret == "" {
					return "empty"
				} else {
					return "not_empty"
				}
			}())
		logrus.Error(errMsg)
		return nil, fmt.Errorf(errMsg)
	}

	config := &client.Config{
		AccessKeyId:     tea.String(accessKeyID),
		AccessKeySecret: tea.String(accessKeySecret),
		Endpoint:        tea.String(endpoint),
	}

	fcClient, err := fc.NewClient(config)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"endpoint":    endpoint,
			"accessKeyID": accessKeyID[:10] + "...",
			"error":       err.Error(),
		}).Error("创建FC客户端失败")
		return nil, fmt.Errorf("创建FC客户端失败: %w", err)
	}

	logrus.WithFields(logrus.Fields{
		"endpoint":    endpoint,
		"accessKeyID": accessKeyID[:10] + "...",
	}).Info("FC客户端创建成功")
	return &Client{
		client:          fcClient,
		endpoint:        endpoint,
		accessKeyID:     accessKeyID,
		accessKeySecret: accessKeySecret,
	}, nil
}

// DeployFunction 部署函数
func (c *Client) DeployFunction(ctx context.Context, input *DeployInput) error {
	// 首先检查上下文是否已取消
	select {
	case <-ctx.Done():
		logrus.WithFields(logrus.Fields{
			"serviceName":  input.ServiceName,
			"functionName": input.FunctionName,
		}).Info("部署已取消")
		return ctx.Err()
	default:
		// 继续执行
	}

	logrus.WithFields(logrus.Fields{
		"serviceName":   input.ServiceName,
		"functionName":  input.FunctionName,
		"ossBucketName": input.OSSBucketName,
		"ossObjectName": input.OSSObjectName,
		"env":           input.Env,
	}).Info("开始部署函数")

	// 先检查函数是否存在
	functionPath := input.ServiceName + "$" + input.FunctionName
	logrus.WithFields(logrus.Fields{
		"functionPath": functionPath,
	}).Debug("检查函数是否存在")

	getFunctionRequest := &fc.GetFunctionRequest{}
	_, err := c.client.GetFunction(tea.String(functionPath), getFunctionRequest)
	if err != nil {
		logrus.WithFields(logrus.Fields{
			"functionPath": functionPath,
			"error":        err.Error(),
		}).Warn("函数不存在或获取失败，将尝试更新")
	} else {
		logrus.WithFields(logrus.Fields{
			"functionPath": functionPath,
		}).Info("函数存在，继续更新")
	}

	// 构建部署请求
	var codeLocation *fc.InputCodeLocation

	// 根据输入参数选择代码位置方式
	if input.ZipFile != "" {
		// 使用zipFile方式
		logrus.WithField("zipFileLength", len(input.ZipFile)).Debug("使用zipFile方式部署")
		codeLocation = &fc.InputCodeLocation{
			ZipFile: tea.String(input.ZipFile),
		}
	} else {
		// 使用OSS方式
		logrus.WithFields(logrus.Fields{
			"ossBucketName": input.OSSBucketName,
			"ossObjectName": input.OSSObjectName,
		}).Debug("使用OSS方式部署")
		codeLocation = &fc.InputCodeLocation{
			OssBucketName: tea.String(input.OSSBucketName),
			OssObjectName: tea.String(input.OSSObjectName),
		}
	}

	request := &fc.UpdateFunctionRequest{
		Body: &fc.UpdateFunctionInput{
			Code: codeLocation,
		},
	}

	// 如果提供了运行时和处理程序，则添加到请求中
	if input.Runtime != "" {
		request.Body.Runtime = tea.String(input.Runtime)
		logrus.WithField("runtime", input.Runtime).Debug("设置函数运行时")
	}

	if input.Handler != "" {
		request.Body.Handler = tea.String(input.Handler)
		logrus.WithField("handler", input.Handler).Debug("设置函数处理程序")
	}

	// 不设置环境变量，保持函数原有的环境变量不变
	// 注意：这里故意不设置 EnvironmentVariables，以保持函数现有的环境变量配置
	if input.Env != "" {
		logrus.WithField("env", input.Env).Debug("部署参数包含环境信息，但不修改函数环境变量")
	}

	// 再次检查上下文是否已取消
	select {
	case <-ctx.Done():
		logrus.WithFields(logrus.Fields{
			"serviceName":  input.ServiceName,
			"functionName": input.FunctionName,
		}).Info("部署已取消")
		return ctx.Err()
	default:
		// 继续执行
	}

	// 记录详细的请求信息
	logrus.WithFields(logrus.Fields{
		"functionPath": functionPath,
		"serviceName":  input.ServiceName,
		"functionName": input.FunctionName,
		"endpoint":     "fcv3.cn-hangzhou.aliyuncs.com",
		"method":       "PUT",
		"apiPath":      fmt.Sprintf("/2023-03-30/functions/%s", functionPath),
	}).Info("准备发送FC API请求")

	// 记录部署模式
	if input.ZipFile != "" {
		logrus.WithField("zipFileLength", len(input.ZipFile)).Debug("使用zipFile部署模式")
	} else {
		logrus.WithFields(logrus.Fields{
			"ossBucketName": input.OSSBucketName,
			"ossObjectName": input.OSSObjectName,
		}).Debug("使用OSS部署模式")
	}

	// 记录请求信息
	logrus.WithField("codeLocation", fmt.Sprintf("%+v", codeLocation)).Debug("FC API请求结构")

	// 验证base64格式
	if input.ZipFile != "" && !isValidBase64(input.ZipFile) {
		logrus.Warn("ZipFile可能不是有效的base64格式")
	}

	// 使用SDK方式调用FC UpdateFunction API
	logrus.WithField("functionPath", functionPath).Info("正在调用FC UpdateFunction API...")
	response, err := c.client.UpdateFunction(tea.String(functionPath), request)

	if err != nil {
		// 简化的错误处理
		logrus.WithFields(logrus.Fields{
			"serviceName":  input.ServiceName,
			"functionName": input.FunctionName,
			"functionPath": functionPath,
			"error":        err.Error(),
		}).Error("FC API调用失败")

		return fmt.Errorf("更新函数失败: %w", err)
	}

	// 成功响应处理
	logrus.WithFields(logrus.Fields{
		"serviceName":  input.ServiceName,
		"functionName": input.FunctionName,
		"functionPath": functionPath,
		"response":     fmt.Sprintf("%+v", response),
	}).Info("FC API调用成功")

	return nil
}

// isValidBase64 检查字符串是否为有效的base64格式
func isValidBase64(s string) bool {
	if len(s) == 0 {
		return false
	}
	_, err := base64.StdEncoding.DecodeString(s)
	return err == nil
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// GetFCDeploymentInfo 根据配置获取FC部署信息
func GetFCDeploymentInfo(serviceType, functionBaseName, env, version string,
	functionConfig []config.ServiceFunctionConfig) (*config.FCDeploymentInfo, error) {

	logrus.WithFields(logrus.Fields{
		"serviceType":      serviceType,
		"functionBaseName": functionBaseName,
		"env":              env,
		"version":          version,
	}).Info("获取函数部署配置")

	// 从新配置中查找
	for _, serviceConfig := range functionConfig {
		if serviceConfig.ServiceType == serviceType {
			for _, funcConfig := range serviceConfig.Functions {
				if funcConfig.Name == functionBaseName {
					// 替换模板中的变量
					serviceName := strings.Replace(funcConfig.ServiceNameTemplate, "{env}", env, -1)
					functionName := strings.Replace(funcConfig.FunctionNameTemplate, "{env}", env, -1)
					ossPath := strings.Replace(funcConfig.OssPathTemplate, "{version}", version, -1)

					logrus.WithFields(logrus.Fields{
						"serviceName":  serviceName,
						"functionName": functionName,
						"ossPath":      ossPath,
					}).Info("已获取到函数部署配置")

					return &config.FCDeploymentInfo{
						ServiceName:   serviceName,
						FunctionName:  functionName,
						OSSObjectPath: ossPath,
					}, nil
				}
			}
		}
	}

	// 如果没找到，返回错误
	errMsg := fmt.Sprintf("找不到服务类型 %s 下的函数 %s 的配置", serviceType, functionBaseName)
	logrus.WithFields(logrus.Fields{
		"serviceType":      serviceType,
		"functionBaseName": functionBaseName,
	}).Error(errMsg)

	return nil, fmt.Errorf(errMsg)
}
