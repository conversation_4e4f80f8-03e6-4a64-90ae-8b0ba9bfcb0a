@import '../../style/theme/default.less';

@checkbox-prefix-cls: ~'@{prefix}-checkbox';

@checkbox-mask-border-width: @border-2;
@checkbox-mask-border-style: @border-solid;
@checkbox-mask-border-radius: @radius-small;

@checkbox-mask-height: 14px;
@checkbox-mask-bg-height: @size-6;
@checkbox-mask-bg-color-bg: @icon-hover-color-bg;

@checkbox-mask-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-2');
@checkbox-mask-color-bg_checked: @color-primary-6;
@checkbox-mask-color-bg_disabled: var(~'@{arco-cssvars-prefix}-color-fill-2');
@checkbox-mask-color-bg_checked_disabled: var(~'@{arco-cssvars-prefix}-color-primary-light-3');

@checkbox-mask-color-border: var(~'@{arco-cssvars-prefix}-color-fill-3');
@checkbox-mask-color-border_hover: var(~'@{arco-cssvars-prefix}-color-fill-4');
@checkbox-mask-color-border_checked: @color-transparent;
@checkbox-mask-color-border_checked_disabled: @color-transparent;
@checkbox-mask-color-border_disabled: var(~'@{arco-cssvars-prefix}-color-fill-3');

@checkbox-color-text: var(~'@{arco-cssvars-prefix}-color-text-1');
@checkbox-color-text_disabled: var(~'@{arco-cssvars-prefix}-color-text-4');

@checkbox-group-spacing: @spacing-7;
@checkbox-text-mask-spacing: @spacing-4;
@checkbox-text-font-size: @font-size-body-3;

@checkbox-group-size-line-height_vertical: @size-8;
@checkbox-size-check-icon: @size-2;
@checkbox-color-check-icon: var(~'@{arco-cssvars-prefix}-color-white');
@checkbox-color-check-icon_disabled: var(~'@{arco-cssvars-prefix}-color-fill-2');
@checkbox-color-indeterminate-icon-width: 6px;
@checkbox-color-indeterminate-icon-height: 2px;
@checkbox-color-indeterminate-icon: var(~'@{arco-cssvars-prefix}-color-white');
