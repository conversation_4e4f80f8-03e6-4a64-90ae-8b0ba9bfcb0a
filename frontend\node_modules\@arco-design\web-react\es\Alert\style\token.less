@import '../../style/theme/default.less';

@alert-border-width: @border-1;
@alert-margin-close-icon-left: @spacing-4;
@alert-margin-icon-right:  @spacing-4;
@alert-margin-action-right: @spacing-4;
@alert-margin-action-left: @spacing-4;
@alert-border-radius: @radius-small;
@alert-line-height: 1.5715;
@alert-title-line-height: 1.5;
@alert-title-margin-bottom: @spacing-2;

@alert-padding-horizontal: @spacing-7;
@alert-padding-vertical: 9px;
@alert-padding-horizontal_with_title: @spacing-7;
@alert-padding-vertical_with_title: @spacing-7;

@alert-font-weight-title: @font-weight-500;
@alert-font-size-text-title: @font-size-title-1;
@alert-font-size-text-content: @font-size-body-3;
@alert-font-size-icon: 16px;
@alert-font-size-icon_with_title: 18px;
@alert-font-size-close-icon: 12px;
@alert-color-close-icon: var(~'@{arco-cssvars-prefix}-color-text-2');
@alert-color-close-icon_hover: var(~'@{arco-cssvars-prefix}-color-text-1');

/*****************************************************
 * type: info / warning / error /success
 *****************************************************/
@alert-info-color-bg: var(~'@{arco-cssvars-prefix}-color-primary-light-1');
@alert-info-color-border: @color-transparent;
@alert-info-color-icon: @color-primary-6;
@alert-info-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-info-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-info-color-text-content_title: var(~'@{arco-cssvars-prefix}-color-text-2');

@alert-warning-color-bg: var(~'@{arco-cssvars-prefix}-color-warning-light-1');
@alert-warning-color-border: @color-transparent;
@alert-warning-color-icon: @color-warning-6;
@alert-warning-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-warning-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-warning-color-text-content_title: var(~'@{arco-cssvars-prefix}-color-text-2');

@alert-error-color-bg: var(~'@{arco-cssvars-prefix}-color-danger-light-1');
@alert-error-color-border: @color-transparent;
@alert-error-color-icon: @color-danger-6;
@alert-error-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-error-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-error-color-text-content_title: var(~'@{arco-cssvars-prefix}-color-text-2');

@alert-success-color-bg: var(~'@{arco-cssvars-prefix}-color-success-light-1');
@alert-success-color-border: @color-transparent;
@alert-success-color-icon: @color-success-6;
@alert-success-color-text-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-success-color-text-content: var(~'@{arco-cssvars-prefix}-color-text-1');
@alert-success-color-text-content_title: var(~'@{arco-cssvars-prefix}-color-text-2');
