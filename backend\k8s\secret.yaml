apiVersion: v1
kind: Secret
metadata:
  name: ngiq-point-desktop-upload-oss-secret
  namespace: ops
type: Opaque
data:
  gitlab-token: "************************************"  #echo -n "your-gitlab-token" | base64
  dev-oss-access-key-id: "TFRBSTV0OVBEZ0V1N2c2RGR2YkZ6Z0dE"  #echo -n "your-oss-access-key-id" | base64
  dev-oss-access-key-secret: "****************************************"  #echo -n "your-oss-access-key-secret" | base64
  dev-fc-access-key-id: "TFRBSTV0OVBEZ0V1N2c2RGR2YkZ6Z0dE"  #echo -n "your-dev-fc-access-key-id" | base64
  dev-fc-access-key-secret: "****************************************"  #echo -n "your-dev-fc-access-key-secret" | base64
  dev-fc-endpoint: "************************************************************"  #echo -n "your-dev-account-id.cn-hangzhou.fc.aliyuncs.com" | base64
  prod-oss-access-key-id: "TFRBSTV0RmJWNmllRm9XeENQbWtUVGpU"  #echo -n "your-oss-access-key-id" | base64
  prod-oss-access-key-secret: "****************************************"  #echo -n "your-oss-access-key-secret" | base64
  prod-fc-access-key-id: "TFRBSTV0RmJWNmllRm9XeENQbWtUVGpU"  #echo -n "your-oss-access-key-id" | base64
  prod-fc-access-key-secret: "****************************************"  #echo -n "your-oss-access-key-secret" | base64
  prod-fc-endpoint: "************************************************************"  #echo -n "your-prod-account-id.cn-hangzhou.fc.aliyuncs.com" | base64
