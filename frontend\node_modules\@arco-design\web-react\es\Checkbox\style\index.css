.arco-icon-hover.arco-checkbox-icon-hover::before{width:24px;height:24px}.arco-checkbox{display:inline-block;cursor:pointer;box-sizing:border-box;font-size:14px;padding-left:5px;line-height:unset;position:relative}.arco-checkbox>input[type=checkbox]{position:absolute;opacity:0;top:0;left:0;width:0;height:0}.arco-checkbox>input[type=checkbox]:focus-visible+.arco-checkbox-icon-hover::before{background-color:var(--color-fill-2);opacity:1}.arco-checkbox:hover .arco-checkbox-icon-hover::before{background-color:var(--color-fill-2)}.arco-checkbox-text{color:var(--color-text-1);margin-left:8px}.arco-checkbox-mask-wrapper{vertical-align:middle;top:-.09em;position:relative;line-height:1}.arco-checkbox-mask{position:relative;box-sizing:border-box;width:14px;height:14px;border:2px solid var(--color-fill-3);border-radius:var(--border-radius-small);background-color:var(--color-bg-2);-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}.arco-checkbox-mask::after{content:'';display:block;height:2px;width:6px;background:var(--color-white);top:50%;left:50%;transform:translateX(-50%) translateY(-50%) scale(0);position:absolute;border-radius:.5px}.arco-checkbox-mask-icon{position:relative;height:100%;transform:scale(0);color:var(--color-white);transform-origin:center 75%;margin:0 auto;display:block;width:8px}.arco-checkbox:hover .arco-checkbox-mask{border-color:var(--color-fill-4);transition:border-color .1s cubic-bezier(0,0,1,1),transform .3s cubic-bezier(.3,1.3,.3,1)}.arco-checkbox-checked:hover .arco-checkbox-mask,.arco-checkbox-indeterminate:hover .arco-checkbox-mask{transition:transform .3s cubic-bezier(.3,1.3,.3,1)}.arco-checkbox-checked .arco-checkbox-mask{border-color:transparent;background-color:rgb(var(--primary-6))}.arco-checkbox-checked .arco-checkbox-mask-icon{transform:scale(1);transition:transform .3s cubic-bezier(.3,1.3,.3,1)}.arco-checkbox-indeterminate .arco-checkbox-mask{border-color:transparent;background-color:rgb(var(--primary-6))}.arco-checkbox-indeterminate .arco-checkbox-mask-icon{transform:scale(0)}.arco-checkbox-indeterminate .arco-checkbox-mask::after{transform:translateX(-50%) translateY(-50%) scale(1);transition:transform .3s cubic-bezier(.3,1.3,.3,1)}.arco-checkbox.arco-checkbox-disabled{cursor:not-allowed}.arco-checkbox.arco-checkbox-disabled .arco-checkbox-icon-hover{cursor:not-allowed}.arco-checkbox.arco-checkbox-disabled:hover .arco-checkbox-mask{border-color:var(--color-fill-3)}.arco-checkbox-checked:hover .arco-checkbox-mask,.arco-checkbox-indeterminate:hover .arco-checkbox-mask{border-color:transparent}.arco-checkbox-disabled .arco-checkbox-mask{border-color:var(--color-fill-3);background-color:var(--color-fill-2)}.arco-checkbox-disabled.arco-checkbox-checked .arco-checkbox-mask,.arco-checkbox-disabled.arco-checkbox-checked:hover .arco-checkbox-mask,.arco-checkbox-disabled.arco-checkbox-indeterminate .arco-checkbox-mask,.arco-checkbox-disabled.arco-checkbox-indeterminate:hover .arco-checkbox-mask{border-color:transparent;background-color:var(--color-primary-light-3)}.arco-checkbox-checked:hover .arco-checkbox-mask-wrapper::before,.arco-checkbox-disabled:hover .arco-checkbox-mask-wrapper::before,.arco-checkbox-indeterminate:hover .arco-checkbox-mask-wrapper::before{background-color:transparent}.arco-checkbox-disabled .arco-checkbox-text{color:var(--color-text-4)}.arco-checkbox-disabled .arco-checkbox-mask-icon{color:var(--color-fill-2)}.arco-checkbox-group{display:inline-block}.arco-checkbox-group .arco-checkbox{margin-right:16px}.arco-checkbox-group-direction-vertical .arco-checkbox{display:block;margin-right:0;line-height:32px}.arco-checkbox-rtl{direction:rtl;padding-left:0;padding-right:5px}.arco-checkbox-rtl .arco-checkbox-text{margin-left:0;margin-right:8px}.arco-checkbox-group-rtl .arco-checkbox{margin-right:0;margin-left:16px}