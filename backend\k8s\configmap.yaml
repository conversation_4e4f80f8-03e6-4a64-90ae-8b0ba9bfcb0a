apiVersion: v1
kind: ConfigMap
metadata:
  name: ngiq-point-desktop-upload-oss-config
  namespace: ops
data:
  config.yaml: |
    gitlab:
      base_url: "https://git.ngdevops.cn/api/v4/"
    logging:
      level: "info"
      output_file: true
      file_path: "backend.log"
      format: "text"
    # SSO认证配置
    auth:
      enabled: true           # 是否启用认证
      sso_url: "https://sso.ngdevops.cn" # Keycloak服务地址
      realm: "NeuralGalaxyOffice" # Keycloak Realm
      client_id: "ptc-tools"  # Keycloak Client ID
      client_secret: ""       # Keycloak Client Secret (如需)
      auth_endpoint: "/realms/NeuralGalaxyOffice/protocol/openid-connect/auth" # 认证端点
      token_endpoint: "/realms/NeuralGalaxyOffice/protocol/openid-connect/token" # Token端点
      userinfo_endpoint: "/realms/NeuralGalaxyOffice/protocol/openid-connect/userinfo" # 用户信息端点
      logout_endpoint: "/realms/NeuralGalaxyOffice/protocol/openid-connect/logout" # 登出端点
      redirect_uri: "https://ptc-tools.ngdevops.cn/callback" # 登录回调地址
      post_logout_redirect_uri: "https://ptc-tools.ngdevops.cn" # 登出后重定向地址
    # 飞书webhook配置
    feishu:
      enabled: true                   # 是否启用飞书通知
      webhook_url: "https://open.feishu.cn/open-apis/bot/v2/hook/50cfc21a-3f3b-444d-9d7a-ea1a37252576" # 飞书webhook地址
      notification:
        login:
          enabled: true              # 是否启用登录通知
          template: "用户 {username} 已登录系统，登录时间：{time}"
        desktop_upload:
          enabled: true              # 是否启用桌面客户端上传通知
          template: "用户 {username} 已发布桌面客户端版本 {version} 到 {env} 环境，发布时间：{time}"
        lambda_deploy:
          enabled: true              # 是否启用Lambda部署通知
          template: "用户 {username} 已部署Lambda服务 {service} 版本 {version} 到 {env} 环境，部署时间：{time}"
    desktop:
      gitlab:
        project_id: 77
        project_name: "ngiq-point-desktop"
        package_name: "ngiq-point-desktop"
      oss:
        access_key_id: "${DEV_OSS_ACCESS_KEY_ID}"
        access_key_secret: "${DEV_OSS_ACCESS_KEY_SECRET}"
        bucket_name: "service-ng-software"
        endpoint: "oss-cn-hangzhou.aliyuncs.com"
    lambda:
      gitlab:
        project_id: 544
        project_name: "lambda"
        package_name: "ngiq-lambda"
      oss:
        access_key_id: "${PROD_OSS_ACCESS_KEY_ID}"
        access_key_secret: "${PROD_OSS_ACCESS_KEY_SECRET}"
        bucket_name: "service-ngiq-serverless"
        endpoint: "oss-cn-hangzhou.aliyuncs.com"
      fc:
        endpoint: "1451119566254585.cn-hangzhou.fc.aliyuncs.com"
        # FC配置现在通过代码根据环境动态选择
        # dev环境使用: DEV_FC_ACCESS_KEY_ID, DEV_FC_ACCESS_KEY_SECRET, DEV_FC_ENDPOINT
        # prod环境使用: PROD_FC_ACCESS_KEY_ID, PROD_FC_ACCESS_KEY_SECRET, PROD_FC_ENDPOINT
        # 注意：上面的endpoint字段已废弃，实际使用环境变量中的endpoint
        access_key_id: ""
        access_key_secret: ""
      services:
        - name: "ngiq-lambda"
          project_name: "lambda"
          package_name: "ngiq-lambda"
          project_id: 544
          oss_base_path: "lambda-python-functions"
        - name: "target-report-word-export"
          project_name: "target-report-word-export"
          package_name: "com/neuralgalaxy/cloud/serverless/function/target-report-word-export"
          project_id: 114
          oss_base_path: "target-report-word-export"
        - name: "plan-report-pdf-export"
          project_name: "plan-report-pdf-export"
          package_name: "com/neuralgalaxy/cloud/serverless/function/plan-report-pdf-export"
          project_id: 134
          oss_base_path: "plan-report-pdf-export"
      function_config:
        - service_type: "ngiq-lambda"
          functions:
            - name: "surf2surf"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-surf2surf" 
              oss_path_template: "lambda-python-functions/{version}/ng_surf2surf.zip"
            - name: "vertex-fc-ptc"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ng-vertex-fc-ptc"
              oss_path_template: "lambda-python-functions/{version}/ng_vertex_fc_ptc.zip"
            - name: "vertex-fc"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ng-vertex-fc"
              oss_path_template: "lambda-python-functions/{version}/ng_vertex_fc.zip"
            - name: "surfparcel-fc-ptc"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ng-surfparcel-fc-ptc"
              oss_path_template: "lambda-python-functions/{version}/ng_surfparcel_fc_ptc.zip"
            - name: "surfparcel-fc"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ng-surfparcel-fc"
              oss_path_template: "lambda-python-functions/{version}/ng_surfparcel_fc.zip"
            - name: "dicom-anony"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ng-dicom-anony"
              oss_path_template: "lambda-python-functions/{version}/ng_dicom_anony.zip"
            - name: "archive-files"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-archive-files"
              oss_path_template: "lambda-python-functions/{version}/archive-files.zip"
            - name: "ptc-surf2surf"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ptc-surf2surf"
              oss_path_template: "lambda-python-functions/{version}/ng_surf2surf.zip"
            - name: "ng2bids"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-ng2bids"
              oss_path_template: "lambda-python-functions/{version}/ng2bids.zip"
        - service_type: "target-report-word-export"
          functions:
            - name: "target-report-word-export"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-target-report-word-export"
              oss_path_template: "target-report-word-export/target-report-word-export-{version}.jar"
        - service_type: "plan-report-pdf-export"
          functions:
            - name: "plan-report-pdf-export-v2"
              service_name_template: "{env}-ng-fc"
              function_name_template: "{env}-plan-report-pdf-export-v2"
              oss_path_template: "plan-report-pdf-export/plan-report-pdf-export-{version}.jar"
