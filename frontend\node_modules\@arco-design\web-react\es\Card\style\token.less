@import '../../style/theme/default.less';

@card-size-small-height-title: @size-10;
@card-size-small-font-size-title: @font-size-title-1;
@card-size-small-font-size-title-extra: @font-size-body-3;
@card-size-small-font-size: @font-size-body-3;
@card-size-small-padding-horizontal-title: @spacing-7;
@card-size-small-padding-horizontal-body: @spacing-7;
@card-size-small-padding-vertical-body: @spacing-6;

@card-size-default-height-title: 46px;
@card-size-default-font-size-title: @font-size-title-1;
@card-size-default-font-size-title-extra: @font-size-body-3;
@card-size-default-font-size: @font-size-body-3;
@card-size-default-padding-horizontal-title: @spacing-7;
@card-size-default-padding-horizontal-body: @spacing-7;
@card-size-default-padding-vertical-body: @spacing-7;

@card-line-height: @line-height-base;
@card-font-weight-title: @font-weight-500;
@card-margin-top-meta-footer: @spacing-8;
@card-margin-top-meta-description: @spacing-2;
@card-margin-right-action-item: @spacing-6;

@card-color-bg: var(~'@{arco-cssvars-prefix}-color-bg-2');
@card-color-border: var(~'@{arco-cssvars-prefix}-color-neutral-3');
@card-color-title: var(~'@{arco-cssvars-prefix}-color-text-1');
@card-color-title-extra: @color-primary-6;
@card-color-body: var(~'@{arco-cssvars-prefix}-color-text-2');
@card-color-action: var(~'@{arco-cssvars-prefix}-color-text-2');
@card-color-action_hover: @color-primary-6;
@card-color-box-shadow: rgb(var(~'@{arco-cssvars-prefix}-gray-2'));
@card-color-box-shadow_dark: rgba(var(~'@{arco-cssvars-prefix}-gray-1'), 40%);

@card-border-width: @border-1;
@card-border-width-title-bottom: @border-1;
@card-border-radius: @radius-small;
@card-border-radius-no-border: @radius-none;
